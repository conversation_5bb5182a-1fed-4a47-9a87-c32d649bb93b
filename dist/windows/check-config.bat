@echo off
chcp 65001 >nul
title U8扩展系统 - 配置检查
echo ========================================
echo    U8扩展系统 - 配置检查工具
echo ========================================
echo.

REM 检查配置文件
if not exist .env (
    echo [错误] 未找到 .env 配置文件
    echo 请运行 start.bat 或手动创建配置文件
    goto :end
)

echo [信息] 正在检查配置文件...
echo.

REM 显示关键配置项
for /f "tokens=1,2 delims==" %%a in (.env) do (
    if "%%a"=="DATABASE_URL" echo [配置] 数据库连接: %%b
    if "%%a"=="SERVER_PORT" echo [配置] 服务端口: %%b
    if "%%a"=="JWT_SECRET" echo [配置] JWT密钥: [已设置]
    if "%%a"=="APP_ENV" echo [配置] 运行环境: %%b
)

echo.
echo [信息] 配置检查完成
echo.

:end
pause
