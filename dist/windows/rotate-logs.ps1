# U8 Extend System - Advanced Log Rotation Script
# This script provides daily log rotation with proper naming and cleanup

param(
    [int]$DaysToKeep = 30,
    [string]$LogDirectory = "logs",
    [switch]$Force,
    [switch]$ForceRotateEmpty
)

# Ensure we're in the correct directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $ScriptDir

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "U8 Extend System - Log Rotation Tool" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if logs directory exists
if (-not (Test-Path $LogDirectory)) {
    Write-Host "[INFO] Creating logs directory..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $LogDirectory -Force | Out-Null
}

# Get current date for naming
$Today = Get-Date -Format "yyyy-MM-dd"
$CurrentTime = Get-Date -Format "HH-mm-ss"

Write-Host "[INFO] Starting log rotation for date: $Today" -ForegroundColor Green
Write-Host "[INFO] Keeping logs for last $DaysToKeep days" -ForegroundColor Green
Write-Host ""

# Function to rotate a specific log file
function Rotate-LogFile {
    param(
        [string]$LogFile,
        [string]$LogType
    )

    if (Test-Path $LogFile) {
        $FileInfo = Get-Item $LogFile
        $FileSize = [math]::Round($FileInfo.Length / 1MB, 2)

        Write-Host "[INFO] Processing $LogType log: $($FileInfo.Name) (${FileSize} MB)" -ForegroundColor White

        # Check if we should rotate this file
        $ShouldRotate = $false
        if ($ForceRotateEmpty) {
            $ShouldRotate = $true
            Write-Host "[INFO] Force rotating (including empty files)" -ForegroundColor Yellow
        } elseif ($FileInfo.Length -gt 0) {
            $ShouldRotate = $true
        }

        if ($ShouldRotate) {
            # Create rotated filename with date and time
            $RotatedName = "${LogType}_${Today}_${CurrentTime}.log"
            $RotatedPath = Join-Path $LogDirectory $RotatedName

            try {
                # Copy current log to rotated file
                Copy-Item $LogFile $RotatedPath -Force

                # Clear the original log file (don't delete, just clear content)
                Clear-Content $LogFile -Force

                Write-Host "[SUCCESS] Rotated to: $RotatedName" -ForegroundColor Green
                return $true
            }
            catch {
                Write-Host "[ERROR] Failed to rotate $LogType log: $($_.Exception.Message)" -ForegroundColor Red
                return $false
            }
        }
        else {
            Write-Host "[INFO] $LogType log is empty or too small, skipping rotation" -ForegroundColor Yellow
            return $false
        }
    }
    else {
        Write-Host "[INFO] $LogType log file not found: $LogFile" -ForegroundColor Yellow
        return $false
    }
}

# Function to rename NSSM rotated files to date format
function Rename-NSSMRotatedFiles {
    Write-Host "[INFO] Checking for NSSM rotated files..." -ForegroundColor White

    # Look for NSSM's numbered rotation files
    $NSSMFiles = Get-ChildItem -Path $LogDirectory -Filter "*.log.*" | Where-Object { $_.Name -match "\.log\.\d+$" }

    foreach ($File in $NSSMFiles) {
        $BaseName = $File.Name -replace "\.log\.\d+$", ""
        $FileDate = $File.LastWriteTime.ToString("yyyy-MM-dd_HH-mm-ss")
        $NewName = "${BaseName}_${FileDate}.log"
        $NewPath = Join-Path $LogDirectory $NewName

        try {
            Rename-Item $File.FullName $NewPath -Force
            Write-Host "[RENAMED] $($File.Name) -> $NewName" -ForegroundColor Cyan
        }
        catch {
            Write-Host "[ERROR] Failed to rename $($File.Name): $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Rotate main service logs
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Step 1: Rotating current logs" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

$ServiceLog = Join-Path $LogDirectory "service.log"
$ErrorLog = Join-Path $LogDirectory "service_error.log"

$ServiceRotated = Rotate-LogFile -LogFile $ServiceLog -LogType "service"
$ErrorRotated = Rotate-LogFile -LogFile $ErrorLog -LogType "service_error"

# Also rename any NSSM rotated files to date format
Rename-NSSMRotatedFiles

Write-Host ""

# Clean up old log files
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Step 2: Cleaning old logs" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

$CutoffDate = (Get-Date).AddDays(-$DaysToKeep)
Write-Host "[INFO] Removing logs older than: $($CutoffDate.ToString('yyyy-MM-dd'))" -ForegroundColor White

$OldLogs = Get-ChildItem -Path $LogDirectory -Filter "*.log" | Where-Object { 
    $_.LastWriteTime -lt $CutoffDate -and $_.Name -match "^\w+_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.log$"
}

if ($OldLogs.Count -eq 0) {
    Write-Host "[INFO] No old logs found to clean up" -ForegroundColor Yellow
}
else {
    Write-Host "[INFO] Found $($OldLogs.Count) old log files to remove:" -ForegroundColor White
    
    foreach ($OldLog in $OldLogs) {
        $FileSize = [math]::Round($OldLog.Length / 1KB, 2)
        Write-Host "  - $($OldLog.Name) (${FileSize} KB, $($OldLog.LastWriteTime.ToString('yyyy-MM-dd')))" -ForegroundColor Gray
    }
    
    if (-not $Force) {
        $Confirm = Read-Host "`n[CONFIRM] Delete these old log files? (y/N)"
        if ($Confirm -ne 'y' -and $Confirm -ne 'Y') {
            Write-Host "[CANCELLED] Old log cleanup cancelled" -ForegroundColor Yellow
            Write-Host ""
            return
        }
    }
    
    $DeletedCount = 0
    $DeletedSize = 0
    
    foreach ($OldLog in $OldLogs) {
        try {
            $DeletedSize += $OldLog.Length
            Remove-Item $OldLog.FullName -Force
            $DeletedCount++
            Write-Host "[DELETED] $($OldLog.Name)" -ForegroundColor Red
        }
        catch {
            Write-Host "[ERROR] Failed to delete $($OldLog.Name): $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    $DeletedSizeMB = [math]::Round($DeletedSize / 1MB, 2)
    Write-Host "[SUCCESS] Deleted $DeletedCount files, freed ${DeletedSizeMB} MB" -ForegroundColor Green
}

Write-Host ""

# Display current log statistics
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Step 3: Current log statistics" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

$AllLogs = Get-ChildItem -Path $LogDirectory -Filter "*.log"
if ($AllLogs.Count -eq 0) {
    Write-Host "[INFO] No log files found" -ForegroundColor Yellow
}
else {
    $TotalSize = ($AllLogs | Measure-Object -Property Length -Sum).Sum
    $TotalSizeMB = [math]::Round($TotalSize / 1MB, 2)
    
    Write-Host "[INFO] Total log files: $($AllLogs.Count)" -ForegroundColor White
    Write-Host "[INFO] Total size: ${TotalSizeMB} MB" -ForegroundColor White
    Write-Host ""
    
    # Group by type and date
    $CurrentLogs = $AllLogs | Where-Object { $_.Name -in @("service.log", "service_error.log") }
    $ArchivedLogs = $AllLogs | Where-Object { $_.Name -match "^\w+_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.log$" }
    
    if ($CurrentLogs.Count -gt 0) {
        Write-Host "[CURRENT LOGS]" -ForegroundColor Green
        foreach ($Log in $CurrentLogs) {
            $FileSize = [math]::Round($Log.Length / 1KB, 2)
            Write-Host "  $($Log.Name) - ${FileSize} KB" -ForegroundColor White
        }
        Write-Host ""
    }
    
    if ($ArchivedLogs.Count -gt 0) {
        Write-Host "[ARCHIVED LOGS] (Last 10)" -ForegroundColor Cyan
        $RecentArchived = $ArchivedLogs | Sort-Object LastWriteTime -Descending | Select-Object -First 10
        foreach ($Log in $RecentArchived) {
            $FileSize = [math]::Round($Log.Length / 1KB, 2)
            $LogDate = $Log.LastWriteTime.ToString('yyyy-MM-dd HH:mm')
            Write-Host "  $($Log.Name) - ${FileSize} KB ($LogDate)" -ForegroundColor Gray
        }
        
        if ($ArchivedLogs.Count -gt 10) {
            Write-Host "  ... and $($ArchivedLogs.Count - 10) more archived logs" -ForegroundColor DarkGray
        }
    }
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "LOG ROTATION COMPLETED!" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Create a scheduled task suggestion
Write-Host "[AUTOMATION TIP]" -ForegroundColor Yellow
Write-Host "To automate daily log rotation, create a Windows scheduled task:" -ForegroundColor White
Write-Host "  Command: powershell.exe" -ForegroundColor Gray
Write-Host "  Arguments: -ExecutionPolicy Bypass -File `"$($MyInvocation.MyCommand.Path)`" -Force" -ForegroundColor Gray
Write-Host "  Schedule: Daily at 2:00 AM" -ForegroundColor Gray
Write-Host ""
Write-Host "[USAGE EXAMPLES]" -ForegroundColor Yellow
Write-Host "  Normal rotation:     .\rotate-logs.ps1" -ForegroundColor White
Write-Host "  Force rotate empty:  .\rotate-logs.ps1 -ForceRotateEmpty" -ForegroundColor White
Write-Host "  Keep 60 days:        .\rotate-logs.ps1 -DaysToKeep 60" -ForegroundColor White
Write-Host "  Auto cleanup:        .\rotate-logs.ps1 -Force -ForceRotateEmpty" -ForegroundColor White
Write-Host ""
