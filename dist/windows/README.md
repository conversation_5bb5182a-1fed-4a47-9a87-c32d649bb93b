# U8扩展系统 - Windows 部署包

## 部署步骤

### 1. 环境准备
- Windows Server 2016 或更高版本
- SQL Server 2016 或更高版本
- 确保防火墙允许 8081 端口访问

### 2. 配置数据库
1. 根据环境选择配置文件：
   - 测试环境：复制 `.env.testing` 为 `.env`
   - 生产环境：复制 `.env.production` 为 `.env`
   - 自定义环境：复制 `.env.example` 为 `.env`
2. 修改 `.env` 文件中的数据库连接信息：
   ```
   DATABASE_URL=mssql://用户名:密码@服务器地址:端口/数据库名
   ```

### 3. 启动应用
1. 双击 `start.bat` 启动应用
2. 或在命令行中运行 `u8_extend.exe`

### 4. 验证部署
- 访问 `http://服务器IP:8081/health` 检查服务状态
- 使用默认管理员账户登录：
  - 用户名: admin
  - 密码: admin123

### 5. 生产环境配置
- 修改 JWT_SECRET 为强密钥
- 设置合适的 CORS_ORIGINS
- 配置日志级别为 warn 或 error
- 启用 HTTPS (建议使用反向代理)

## 故障排除

### 常见问题
1. **数据库连接失败**
   - 检查 SQL Server 是否启动
   - 验证连接字符串是否正确
   - 确认用户权限

2. **端口被占用**
   - 修改 `.env` 中的 `SERVER_PORT`
   - 或停止占用端口的程序

3. **权限问题**
   - 确保应用有读写权限
   - 检查防火墙设置

### 日志查看
应用日志会输出到控制台，生产环境建议：
- 使用 Windows 服务运行
- 配置日志文件输出
- 设置日志轮转

## 技术支持
如有问题，请联系技术支持团队。
