@echo off
chcp 65001 >nul
title U8扩展系统
echo ========================================
echo    U8扩展系统 - Windows版本
echo ========================================
echo.

REM 检查是否存在 .env 文件
if not exist .env (
    echo [警告] 未找到 .env 配置文件！
    echo.
    echo 请选择配置文件：
    echo   1. 测试环境 - 复制 .env.testing 为 .env
    echo   2. 生产环境 - 复制 .env.production 为 .env
    echo   3. 自定义配置 - 复制 .env.example 为 .env
    echo.
    set /p choice="请输入选择 (1-3): "

    if "%choice%"=="1" (
        copy .env.testing .env >nul
        echo [信息] 已使用测试环境配置
    ) else if "%choice%"=="2" (
        copy .env.production .env >nul
        echo [信息] 已使用生产环境配置
        echo [警告] 请修改 .env 文件中的数据库连接信息！
    ) else if "%choice%"=="3" (
        copy .env.example .env >nul
        echo [信息] 已创建自定义配置文件
        echo [警告] 请修改 .env 文件中的配置信息！
    ) else (
        echo [错误] 无效选择！
        pause
        exit /b 1
    )
    echo.
)

REM 显示配置信息
echo [信息] 正在启动服务器...
echo [信息] 配置文件: .env
echo [信息] 日志输出: 控制台
echo.

REM 启动应用程序
u8_extend.exe

REM 如果程序异常退出，暂停以查看错误信息
if %ERRORLEVEL% neq 0 (
    echo.
    echo [错误] 程序异常退出，错误代码: %ERRORLEVEL%
    echo.
    echo 常见问题解决方案：
    echo   1. 检查数据库连接配置
    echo   2. 确认数据库服务是否启动
    echo   3. 验证端口是否被占用
    echo   4. 查看上方的错误信息
    echo.
    pause
)
