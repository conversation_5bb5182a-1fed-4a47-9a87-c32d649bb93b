@echo off
title U8 Extend System - Service Update & Publish Tool
echo ========================================
echo    U8 Extend System - Service Update ^& Publish Tool
echo ========================================
echo.

REM Check administrator privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Administrator privileges required!
    echo [INFO] Please right-click this script and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo [INFO] This script will update the U8 Extend System service with new files
echo [INFO] Process:
echo   1. Stop the running service (if running)
echo   2. Backup current executable
echo   3. Update application files
echo   4. Restart the service
echo   5. Verify the update
echo.
echo Do you want to continue? (y/N)
set /p confirm="Please enter choice: "
if /i not "%confirm%"=="y" (
    echo [CANCELLED] Service update cancelled
    pause
    exit /b 0
)

echo.
echo ========================================
echo [Step 1] Locating NSSM tool...
echo ========================================

REM Check for NSSM in current directory or subdirectories
set NSSM_PATH=
if exist "nssm.exe" (
    set NSSM_PATH=nssm.exe
    echo [INFO] Found NSSM in current directory
) else if exist "win64\nssm.exe" (
    set NSSM_PATH=win64\nssm.exe
    echo [INFO] Found NSSM in win64 subdirectory
) else if exist "nssm-2.24\win64\nssm.exe" (
    set NSSM_PATH=nssm-2.24\win64\nssm.exe
    echo [INFO] Found NSSM in nssm-2.24\win64 subdirectory
) else (
    echo [ERROR] NSSM not found!
    echo [INFO] Please ensure nssm.exe is available for service management
    pause
    exit /b 1
)

set SERVICE_NAME=U8ExtendSystem

echo.
echo ========================================
echo [Step 2] Checking service status...
echo ========================================

%NSSM_PATH% status %SERVICE_NAME% >nul 2>&1
if %errorLevel% neq 0 (
    echo [WARNING] Service '%SERVICE_NAME%' not found!
    echo [INFO] Please install the service first using: deploy-service-en.bat
    pause
    exit /b 1
)

echo [INFO] Current service status:
%NSSM_PATH% status %SERVICE_NAME%

echo.
echo ========================================
echo [Step 3] Stopping service for update...
echo ========================================

echo [INFO] Stopping service '%SERVICE_NAME%' for update...
%NSSM_PATH% stop %SERVICE_NAME%
if %errorLevel% equ 0 (
    echo [SUCCESS] Service stopped successfully
) else (
    echo [WARNING] Service may already be stopped
)

echo [INFO] Waiting for service to fully stop...
timeout /t 5 /nobreak >nul

echo.
echo ========================================
echo [Step 4] Creating backup...
echo ========================================

if not exist "backup" mkdir backup
set BACKUP_DATE=%date:~10,4%-%date:~4,2%-%date:~7,2%_%time:~0,2%-%time:~3,2%-%time:~6,2%
set BACKUP_DATE=%BACKUP_DATE: =0%

if exist "u8_extend.exe" (
    echo [INFO] Backing up current executable...
    copy "u8_extend.exe" "backup\u8_extend_%BACKUP_DATE%.exe" >nul
    if %errorLevel% equ 0 (
        echo [SUCCESS] Backup created: backup\u8_extend_%BACKUP_DATE%.exe
    ) else (
        echo [WARNING] Backup failed, continuing anyway...
    )
) else (
    echo [WARNING] No existing executable found to backup
)

echo.
echo ========================================
echo [Step 5] Checking for new files...
echo ========================================

REM Check for new executable in common locations
set NEW_EXE_PATH=
if exist "u8_extend_new.exe" (
    set NEW_EXE_PATH=u8_extend_new.exe
    echo [INFO] Found new executable: u8_extend_new.exe
) else if exist "..\u8_extend.exe" (
    set NEW_EXE_PATH=..\u8_extend.exe
    echo [INFO] Found new executable: ..\u8_extend.exe
) else if exist "..\..\target\release\u8_extend.exe" (
    set NEW_EXE_PATH=..\..\target\release\u8_extend.exe
    echo [INFO] Found new executable: ..\..\target\release\u8_extend.exe
) else (
    echo [WARNING] No new executable found in expected locations
    echo [INFO] Expected locations:
    echo   - u8_extend_new.exe (in current directory)
    echo   - ..\u8_extend.exe (parent directory)
    echo   - ..\..\target\release\u8_extend.exe (build output)
    echo.
    echo [INFO] Please manually copy the new executable to this directory
    echo [INFO] and name it 'u8_extend_new.exe', then run this script again
    pause
    goto :restart_service
)

echo.
echo ========================================
echo [Step 6] Updating application files...
echo ========================================

echo [INFO] Updating executable from: %NEW_EXE_PATH%
copy "%NEW_EXE_PATH%" "u8_extend.exe" >nul
if %errorLevel% equ 0 (
    echo [SUCCESS] Executable updated successfully
) else (
    echo [ERROR] Failed to update executable!
    echo [INFO] Attempting to restore from backup...
    if exist "backup\u8_extend_%BACKUP_DATE%.exe" (
        copy "backup\u8_extend_%BACKUP_DATE%.exe" "u8_extend.exe" >nul
        echo [INFO] Backup restored
    )
    goto :restart_service
)

REM Update configuration if new one exists
if exist ".env.new" (
    echo [INFO] Found new configuration file, updating...
    copy ".env" ".env.backup_%BACKUP_DATE%" >nul 2>&1
    copy ".env.new" ".env" >nul
    echo [SUCCESS] Configuration updated (backup created)
)

:restart_service
echo.
echo ========================================
echo [Step 7] Starting updated service...
echo ========================================

echo [INFO] Starting service '%SERVICE_NAME%'...
%NSSM_PATH% start %SERVICE_NAME%
if %errorLevel% equ 0 (
    echo [SUCCESS] Service started successfully
) else (
    echo [ERROR] Failed to start service!
    echo [INFO] Check logs for details: logs\service_error.log
    pause
    exit /b 1
)

echo.
echo ========================================
echo [Step 8] Verifying update...
echo ========================================

echo [INFO] Waiting for service to initialize...
timeout /t 10 /nobreak >nul

echo [INFO] Checking service status...
%NSSM_PATH% status %SERVICE_NAME%

echo.
echo [INFO] Testing service health...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:1444/health' -TimeoutSec 10 -UseBasicParsing; Write-Host '[SUCCESS] Service is responding: HTTP' $response.StatusCode } catch { Write-Host '[WARNING] Service health check failed:' $_.Exception.Message }"

echo.
echo ========================================
echo SERVICE UPDATE COMPLETED!
echo ========================================
echo.
echo [Update Summary]
echo   Service: %SERVICE_NAME%
echo   Status: Running
echo   Backup: backup\u8_extend_%BACKUP_DATE%.exe
echo   Logs: logs\ directory
echo.
echo [Verification]
echo   Health check: http://localhost:1444/health
echo   Web interface: http://localhost:1444/
echo.
echo [Rollback (if needed)]
echo   1. Stop service: %NSSM_PATH% stop %SERVICE_NAME%
echo   2. Restore backup: copy "backup\u8_extend_%BACKUP_DATE%.exe" "u8_extend.exe"
echo   3. Start service: %NSSM_PATH% start %SERVICE_NAME%
echo.
pause
