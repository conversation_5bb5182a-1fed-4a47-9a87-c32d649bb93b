@echo off
title U8 Extend System - Log Management Tool
echo ========================================
echo    U8 Extend System - Log Management Tool
echo ========================================
echo.

if not exist "logs" (
    echo [INFO] No logs directory found
    echo [INFO] Logs will be created when the service runs
    pause
    exit /b 0
)

echo [INFO] Current log directory: %~dp0logs
echo.

:menu
echo ========================================
echo Log Management Options:
echo ========================================
echo 1. View current logs
echo 2. View log statistics
echo 3. Clean old logs (keep last 7 days)
echo 4. Clean old logs (keep last 30 days)
echo 5. Archive logs to ZIP
echo 6. View real-time logs
echo 7. Exit
echo.
set /p choice="Please select an option (1-7): "

if "%choice%"=="1" goto :view_logs
if "%choice%"=="2" goto :log_stats
if "%choice%"=="3" goto :clean_7days
if "%choice%"=="4" goto :clean_30days
if "%choice%"=="5" goto :archive_logs
if "%choice%"=="6" goto :realtime_logs
if "%choice%"=="7" goto :exit
echo [ERROR] Invalid choice, please try again
echo.
goto :menu

:view_logs
echo.
echo ========================================
echo Current Log Files:
echo ========================================
dir /b logs\*.log 2>nul
if %errorLevel% neq 0 (
    echo [INFO] No log files found
) else (
    echo.
    echo [INFO] Select a log file to view (or press Enter to return to menu):
    set /p logfile="Log file name: "
    if not "%logfile%"=="" (
        if exist "logs\%logfile%" (
            echo.
            echo [Last 50 lines of %logfile%]
            echo ----------------------------------------
            powershell -Command "Get-Content 'logs\%logfile%' -Tail 50 -ErrorAction SilentlyContinue"
        ) else (
            echo [ERROR] Log file not found: %logfile%
        )
    )
)
echo.
pause
goto :menu

:log_stats
echo.
echo ========================================
echo Log Statistics:
echo ========================================
set /a total_files=0
set /a total_size=0

for %%f in (logs\*.log) do (
    set /a total_files+=1
    for %%s in ("%%f") do set /a total_size+=%%~zs
)

if %total_files% equ 0 (
    echo [INFO] No log files found
) else (
    echo Total log files: %total_files%
    set /a total_size_mb=%total_size%/1048576
    echo Total size: %total_size% bytes (~%total_size_mb% MB)
    echo.
    echo [Detailed file list]
    for %%f in (logs\*.log) do (
        set file_size=%%~zf
        set /a file_size_kb=%%~zf/1024
        echo   %%~nxf - !file_size_kb! KB (%%~tf)
    )
)
echo.
pause
goto :menu

:clean_7days
echo.
echo ========================================
echo Cleaning logs older than 7 days...
echo ========================================
set /a days_to_keep=7
goto :clean_logs

:clean_30days
echo.
echo ========================================
echo Cleaning logs older than 30 days...
echo ========================================
set /a days_to_keep=30
goto :clean_logs

:clean_logs
echo [WARNING] This will delete log files older than %days_to_keep% days
echo [INFO] Current log files will be preserved
echo.
set /p confirm="Continue? (y/N): "
if /i not "%confirm%"=="y" (
    echo [CANCELLED] Log cleanup cancelled
    goto :menu
)

echo [INFO] Searching for old log files...
set /a deleted_count=0

REM Use PowerShell to find and delete old files
powershell -Command "Get-ChildItem 'logs\*.log' | Where-Object { $_.LastWriteTime -lt (Get-Date).AddDays(-%days_to_keep%) } | ForEach-Object { Write-Host '[DELETED]' $_.Name; Remove-Item $_.FullName -Force }" 2>nul

echo [SUCCESS] Log cleanup completed
echo [INFO] Files older than %days_to_keep% days have been removed
echo.
pause
goto :menu

:archive_logs
echo.
echo ========================================
echo Archiving logs to ZIP file...
echo ========================================

if not exist "archive" mkdir archive
set ARCHIVE_DATE=%date:~10,4%-%date:~4,2%-%date:~7,2%_%time:~0,2%-%time:~3,2%-%time:~6,2%
set ARCHIVE_DATE=%ARCHIVE_DATE: =0%
set ARCHIVE_NAME=archive\logs_%ARCHIVE_DATE%.zip

echo [INFO] Creating archive: %ARCHIVE_NAME%
powershell -Command "Compress-Archive -Path 'logs\*' -DestinationPath '%ARCHIVE_NAME%' -Force" 2>nul
if %errorLevel% equ 0 (
    echo [SUCCESS] Logs archived to: %ARCHIVE_NAME%
    echo.
    set /p delete_after="Delete original log files after archiving? (y/N): "
    if /i "!delete_after!"=="y" (
        del /q logs\*.log 2>nul
        echo [INFO] Original log files deleted
    )
) else (
    echo [ERROR] Failed to create archive
)
echo.
pause
goto :menu

:realtime_logs
echo.
echo ========================================
echo Real-time Log Monitoring
echo ========================================
echo [INFO] Monitoring current service log (Press Ctrl+C to stop)
echo.

if exist "logs\service.log" (
    powershell -Command "Get-Content 'logs\service.log' -Wait -Tail 10"
) else (
    echo [ERROR] Current service log not found: logs\service.log
    echo [INFO] Available log files:
    dir /b logs\*.log 2>nul
)
echo.
pause
goto :menu

:exit
echo.
echo [INFO] Log management tool closed
exit /b 0
