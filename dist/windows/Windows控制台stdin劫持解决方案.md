# Windows控制台stdin劫持问题解决方案

## 问题描述

在Windows环境下，当通过CMD启动Rust编译的exe应用程序时，会出现以下问题：
- 应用启动正常，日志正常输出
- 约2分钟后，HTTP请求无法进入应用程序
- 按Ctrl+C后，被阻塞的请求突然能够处理
- 这种现象只在Windows CMD环境下出现

## 根本原因

这是Windows特有的控制台应用程序行为问题：

1. **控制台附加机制**：Windows应用程序会自动附加到启动它的控制台进程
2. **stdin劫持**：应用程序会尝试读取控制台的stdin，导致异步任务被阻塞
3. **请求队列**：被阻塞的异步任务导致新的HTTP请求无法被处理
4. **Ctrl+C释放**：中断信号释放了被阻塞的stdin读取操作

## 解决方案

### 方案1：分离控制台启动（推荐）

**使用脚本**：`start-detached.bat`

**原理**：使用Windows的`start`命令在新窗口中启动应用，完全分离控制台

**优点**：
- 简单易用，无需额外工具
- 完全解决stdin劫持问题
- 保留日志输出功能

**使用方法**：
```cmd
# 双击运行
start-detached.bat

# 或命令行运行
start "U8扩展系统" /D "%~dp0" u8_extend.exe
```

### 方案2：NSSM服务方式（生产环境推荐）

**使用脚本**：
- `download-nssm.bat` - 下载NSSM工具
- `install-nssm-service.bat` - 安装Windows服务
- `manage-nssm-service.bat` - 管理服务

**原理**：使用NSSM将应用程序安装为Windows服务，完全脱离控制台环境

**优点**：
- 开机自启动
- 自动重启功能
- 完整的日志管理
- 服务级别的权限控制
- 生产环境最佳实践

**使用方法**：
```cmd
# 1. 下载NSSM
download-nssm.bat

# 2. 安装服务（需要管理员权限）
install-nssm-service.bat

# 3. 管理服务
manage-nssm-service.bat
```

### 方案3：PowerShell启动

**使用方法**：
```powershell
# 在PowerShell中运行
.\u8_extend.exe
```

**优点**：
- 无需额外脚本
- PowerShell处理stdin的方式不同于CMD

### 方案4：start /wait命令

**使用方法**：
```cmd
start /wait u8_extend.exe
```

**优点**：
- 简单的命令行解决方案
- 等待程序完成后返回

## 技术细节

### Windows控制台附加机制

Windows应用程序分为两种类型：
- **控制台应用程序**：自动附加到父进程的控制台
- **GUI应用程序**：不附加控制台，除非显式调用

Rust默认编译为控制台应用程序，因此会出现stdin劫持问题。

### 异步任务阻塞

当应用程序尝试读取stdin时：
1. Tokio运行时中的某个任务被阻塞
2. 阻塞的任务占用了执行器资源
3. 新的HTTP请求无法被调度执行
4. 导致请求超时或无响应

### 解决原理

通过分离控制台或使用服务方式：
1. 应用程序不再附加到CMD的控制台
2. 没有stdin读取操作
3. 异步任务正常执行
4. HTTP请求正常处理

## 推荐使用方式

### 开发测试环境
使用 `start-detached.bat`：
- 快速启动
- 保留日志查看
- 易于调试

### 生产环境
使用NSSM服务方式：
- 稳定可靠
- 自动重启
- 日志管理
- 开机自启

## 验证方法

1. **启动应用**：使用任一解决方案启动应用
2. **等待3分钟**：确保超过问题出现的时间点
3. **测试请求**：访问 `http://localhost:1444/health`
4. **检查响应**：应该能正常返回响应
5. **查看日志**：确认日志持续输出

## 相关资源

- [Factorio论坛讨论](https://forums.factorio.com/viewtopic.php?t=75627)
- [NSSM官网](https://nssm.cc/)
- [Windows服务管理文档](https://docs.microsoft.com/en-us/windows/win32/services/)

## 总结

Windows控制台stdin劫持是一个已知的平台特定问题，通过适当的启动方式可以完全解决。建议：

- **开发环境**：使用 `start-detached.bat`
- **生产环境**：使用NSSM服务方式
- **临时测试**：使用PowerShell或start命令

选择合适的解决方案可以确保应用程序在Windows环境下稳定运行。
