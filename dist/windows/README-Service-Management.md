# U8 Extend System - Windows Service Management

This directory contains comprehensive tools for managing the U8 Extend System as a Windows service with advanced logging capabilities.

## 📁 Script Overview

### Core Service Management
- **`deploy-service-en.bat`** - One-click service deployment with daily log rotation
- **`manage-nssm-service-en.bat`** - Main service management interface
- **`remove-service-en.bat`** - Complete service removal tool
- **`publish-service-en.bat`** - Service update and deployment tool

### Log Management
- **`manage-logs-en.bat`** - Interactive log management tool
- **`rotate-logs.ps1`** - Advanced PowerShell log rotation script

## 🚀 Quick Start

### 1. Initial Deployment
```batch
# Run as Administrator
deploy-service-en.bat
```
This will:
- Install NSSM (if needed)
- Configure the service with daily log rotation
- Start the service automatically
- Set up automatic startup with Windows

### 2. Daily Management
```batch
# Interactive management
manage-nssm-service-en.bat
```
Provides options for:
- Start/Stop/Restart service
- View logs and status
- Manage log files
- Update service
- Complete removal

## 📊 Logging Features

### Daily Log Rotation
The service now supports **automatic daily log rotation**:

- **Current logs**: `service.log`, `service_error.log`
- **Rotated logs**: `service_YYYY-MM-DD_HH-mm-ss.log`
- **Automatic cleanup**: Configurable retention period
- **Size limits**: 50MB per log file before rotation

### Log Management Options
```batch
# Interactive log management
manage-logs-en.bat

# PowerShell advanced rotation
powershell -ExecutionPolicy Bypass -File rotate-logs.ps1
```

Available operations:
- View current logs
- Real-time log monitoring
- Clean old logs (7/30 days)
- Archive logs to ZIP
- View log statistics

## 🔄 Service Updates

### Update Existing Service
```batch
# Update with new executable
publish-service-en.bat
```
This will:
- Stop the service safely
- Backup current executable
- Deploy new version
- Restart service
- Verify update success

### Complete Reinstallation
```batch
# Remove old service
remove-service-en.bat

# Deploy fresh installation
deploy-service-en.bat
```

## 📋 Service Configuration

### NSSM Settings Applied
```
Service Name: U8ExtendSystem
Display Name: U8 Extend System Service
Startup Type: Automatic
Recovery: Restart on failure (5 second delay)

Log Rotation:
- Daily rotation (86400 seconds)
- 50MB size limit per file
- Online rotation enabled
- Automatic file creation
```

### Log File Structure
```
logs/
├── service.log              # Current service output
├── service_error.log        # Current error output
├── service_2024-01-15_09-30-00.log    # Rotated logs
├── service_error_2024-01-15_09-30-00.log
└── ...
```

## 🛠️ Troubleshooting

### Service Won't Start
1. Check logs: `logs\service_error.log`
2. Verify configuration: `.env` file exists
3. Test manually: `u8_extend.exe` (should run without errors)
4. Check permissions: Run as Administrator

### Log Issues
1. **Logs not rotating**: Check NSSM configuration
2. **Disk space**: Use log cleanup tools
3. **Permission errors**: Ensure service has write access to logs directory

### Update Problems
1. **Backup available**: `backup\u8_extend_YYYY-MM-DD_HH-mm-ss.exe`
2. **Rollback process**: Stop service → Restore backup → Start service
3. **Manual update**: Copy new executable over old one while service is stopped

## 🔧 Advanced Configuration

### Custom Log Retention
```powershell
# Keep logs for 60 days
.\rotate-logs.ps1 -DaysToKeep 60

# Force cleanup without confirmation
.\rotate-logs.ps1 -DaysToKeep 30 -Force
```

### Scheduled Log Rotation
Create Windows Task Scheduler entry:
- **Program**: `powershell.exe`
- **Arguments**: `-ExecutionPolicy Bypass -File "C:\path\to\rotate-logs.ps1" -Force`
- **Schedule**: Daily at 2:00 AM
- **Run as**: SYSTEM or Administrator

### Service Port Configuration
Edit `.env` file:
```env
SERVER_PORT=8081
DATABASE_URL=mssql://user:pass@server:port/database
```

## 📁 File Dependencies

### Required Files
- `u8_extend.exe` - Main application
- `.env` - Configuration file
- `nssm.exe` - Service wrapper (auto-downloaded)

### Optional Files
- `.env.testing` - Testing configuration
- `.env.production` - Production configuration
- `backup/` - Automatic backups directory

## 🔒 Security Notes

### Administrator Privileges
All service management scripts require Administrator privileges for:
- Service installation/removal
- File system access
- Registry modifications
- Network port binding

### Service Account
The service runs under:
- **Default**: Local System account
- **Recommended**: Dedicated service account with minimal privileges
- **Network**: Ensure firewall rules allow required ports

## 📞 Support

### Health Check
Test service availability:
```
http://localhost:8081/health
```

### Log Monitoring
Real-time log viewing:
```batch
# From management tool
manage-nssm-service-en.bat → Option 5 → Option 6

# Direct PowerShell
Get-Content logs\service.log -Wait -Tail 10
```

### Common Commands
```batch
# Service status
nssm status U8ExtendSystem

# Manual start/stop
nssm start U8ExtendSystem
nssm stop U8ExtendSystem

# Service configuration
nssm edit U8ExtendSystem
```

---

## 📝 Change Log

### Version 2.0 Features
- ✅ Daily log rotation with proper naming
- ✅ Advanced log management tools
- ✅ Service update mechanism with backup
- ✅ Complete removal tool
- ✅ Integrated management interface
- ✅ PowerShell automation scripts
- ✅ Comprehensive error handling

### Improvements Over v1.0
- **Better logging**: Daily rotation instead of size-only
- **Easier updates**: Automated backup and restore
- **Cleaner removal**: Complete cleanup with preservation options
- **Better UX**: Integrated management interface
- **Automation ready**: PowerShell scripts for scheduling
