@echo off
title U8 Extend System - Service Removal Tool
echo ========================================
echo    U8 Extend System - Service Removal Tool
echo ========================================
echo.

REM Check administrator privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Administrator privileges required!
    echo [INFO] Please right-click this script and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo [WARNING] This script will completely remove the U8 Extend System service!
echo [INFO] This action will:
echo   1. Stop the running service
echo   2. Remove the service from Windows
echo   3. Keep log files and configuration (for safety)
echo.
echo Do you want to continue? (y/N)
set /p confirm="Please enter choice: "
if /i not "%confirm%"=="y" (
    echo [CANCELLED] Service removal cancelled
    pause
    exit /b 0
)

echo.
echo ========================================
echo [Step 1] Locating NSSM tool...
echo ========================================

REM Check for NSSM in current directory or subdirectories
set NSSM_PATH=
if exist "nssm.exe" (
    set NSSM_PATH=nssm.exe
    echo [INFO] Found NSSM in current directory
) else if exist "win64\nssm.exe" (
    set NSSM_PATH=win64\nssm.exe
    echo [INFO] Found NSSM in win64 subdirectory
) else if exist "nssm-2.24\win64\nssm.exe" (
    set NSSM_PATH=nssm-2.24\win64\nssm.exe
    echo [INFO] Found NSSM in nssm-2.24\win64 subdirectory
) else (
    echo [ERROR] NSSM not found!
    echo [INFO] Please ensure nssm.exe is in one of these locations:
    echo   - Current directory: nssm.exe
    echo   - Subdirectory: win64\nssm.exe
    echo   - Extracted: nssm-2.24\win64\nssm.exe
    echo.
    echo [INFO] Alternatively, you can remove the service manually:
    echo   1. Open Services (services.msc)
    echo   2. Find "U8 Extend System Service"
    echo   3. Stop and delete the service
    pause
    exit /b 1
)

set SERVICE_NAME=U8ExtendSystem

echo.
echo ========================================
echo [Step 2] Checking service status...
echo ========================================

%NSSM_PATH% status %SERVICE_NAME% >nul 2>&1
if %errorLevel% neq 0 (
    echo [INFO] Service '%SERVICE_NAME%' not found or already removed
    echo [SUCCESS] Nothing to remove
    pause
    exit /b 0
)

echo [INFO] Service found, current status:
%NSSM_PATH% status %SERVICE_NAME%

echo.
echo ========================================
echo [Step 3] Stopping service...
echo ========================================

echo [INFO] Stopping service '%SERVICE_NAME%'...
%NSSM_PATH% stop %SERVICE_NAME%
if %errorLevel% equ 0 (
    echo [SUCCESS] Service stopped successfully
) else (
    echo [WARNING] Service may already be stopped or failed to stop
)

echo [INFO] Waiting for service to fully stop...
timeout /t 3 /nobreak >nul

echo.
echo ========================================
echo [Step 4] Removing service...
echo ========================================

echo [INFO] Removing service '%SERVICE_NAME%'...
%NSSM_PATH% remove %SERVICE_NAME% confirm
if %errorLevel% equ 0 (
    echo [SUCCESS] Service removed successfully!
) else (
    echo [ERROR] Failed to remove service!
    echo [INFO] You may need to remove it manually using Services (services.msc)
    pause
    exit /b 1
)

echo.
echo ========================================
echo [Step 5] Cleanup verification...
echo ========================================

echo [INFO] Verifying service removal...
%NSSM_PATH% status %SERVICE_NAME% >nul 2>&1
if %errorLevel% neq 0 (
    echo [SUCCESS] Service completely removed from system
) else (
    echo [WARNING] Service may still exist, please check manually
)

echo.
echo ========================================
echo SERVICE REMOVAL COMPLETED!
echo ========================================
echo.
echo [What was removed]
echo   - Windows service: %SERVICE_NAME%
echo   - Service registration and configuration
echo   - Automatic startup configuration
echo.
echo [What was preserved]
echo   - Application files: u8_extend.exe and related files
echo   - Configuration files: .env and related configs
echo   - Log files: logs\ directory (for troubleshooting)
echo   - NSSM tool: for future reinstallation
echo.
echo [To reinstall the service]
echo   Run: deploy-service-en.bat
echo.
echo [To manually clean up remaining files]
echo   - Delete logs\ directory if no longer needed
echo   - Delete .env file if starting fresh
echo   - Delete NSSM files if no longer needed
echo.
pause
