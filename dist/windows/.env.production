# U8扩展系统 - 生产环境配置文件
# 请根据实际生产环境修改以下配置
#
# 🔒 安全提示：
# 1. 在生产环境中，权限检查将被强制启用，无论 ENABLE_PERMISSION_CHECK 设置如何
# 2. 设置 RUST_ENV=production 将触发生产环境安全模式
# 3. 发布构建（无 Cargo.toml 文件）也会自动启用安全模式

# ===========================================
# 数据库配置 (生产环境)
# ===========================================

# 主数据库连接字符串 (SQL Server)
# 请修改为生产环境的数据库连接信息
DATABASE_URL=mssql://sa:YourProductionPassword@your-db-server:1433/zlkdata

# 数据库连接池配置 (生产环境优化)
DB_MAX_CONNECTIONS=20
DB_MIN_CONNECTIONS=5
DB_CONNECTION_TIMEOUT=30

# ===========================================
# 服务器配置 (生产环境)
# ===========================================

# 服务器监听配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8081

# JWT配置 (生产环境必须使用强密钥)
JWT_SECRET=your-super-strong-production-jwt-secret-key-at-least-32-characters-long
JWT_EXPIRATION=28800

# ===========================================
# 日志配置 (生产环境)
# ===========================================

# 生产环境日志级别
RUST_LOG=warn

# 日志格式 (生产环境建议使用json)
LOG_FORMAT=json

# ===========================================
# 应用配置 (生产环境)
# ===========================================

# 应用环境
APP_ENV=production

# 生产环境关闭调试
DEBUG=false

# 生产环境跨域配置 (请根据实际前端域名配置)
CORS_ORIGINS=https://your-frontend-domain.com

# ===========================================
# 安全配置 (生产环境加强)
# ===========================================

# 密码加密轮数 (生产环境建议更高)
BCRYPT_ROUNDS=14

# 会话超时时间
SESSION_TIMEOUT=3600

# 登录安全配置
MAX_LOGIN_ATTEMPTS=3
LOGIN_LOCKOUT_DURATION=1800

# ===========================================
# 性能配置 (生产环境)
# ===========================================

# 文件上传配置
UPLOAD_DIR=/var/app/uploads
MAX_FILE_SIZE=52428800
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx,xls,xlsx

# 缓存配置
CACHE_TTL=7200

# ===========================================
# 监控配置 (生产环境)
# ===========================================

# 启用性能监控
ENABLE_METRICS=true
METRICS_PATH=/metrics
HEALTH_CHECK_PATH=/health

# ===========================================
# 生产环境安全设置
# ===========================================

# 环境标识（触发生产环境安全模式）
RUST_ENV=production

# 权限检查配置（生产环境将强制启用，无论此设置如何）
ENABLE_PERMISSION_CHECK=true
ENABLE_RATE_LIMIT=true
ENABLE_CORS=true

# 管理员配置（请修改为安全的密码）
ADMIN_USERNAME=admin
ADMIN_PASSWORD=YourSecureAdminPassword123!
ADMIN_EMAIL=<EMAIL>

# CORS配置（请修改为实际的前端域名）
CORS_ALLOWED_ORIGINS=https://yourapp.com,https://www.yourapp.com
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization

# 限流配置（生产环境建议值）
RATE_LIMIT_REQUESTS_PER_MINUTE=120
RATE_LIMIT_BURST=20

# 关闭开发功能
HOT_RELOAD=false
SHOW_SQL_LOGS=false
ENABLE_API_DOCS=false
