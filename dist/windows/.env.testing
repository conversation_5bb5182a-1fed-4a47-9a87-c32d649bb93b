# U8扩展系统 - 测试环境配置文件
# 此文件用于测试环境部署
#
# 🔒 安全提示：
# 1. 测试环境可以灵活配置权限检查开关
# 2. 设置 RUST_ENV=production 将触发生产环境安全模式（强制启用权限检查）
# 3. 测试环境建议启用权限检查以验证权限逻辑

# ===========================================
# 数据库配置 (测试环境)
# ===========================================

# 测试环境数据库连接字符串
DATABASE_URL=mssql://sa:Jwaa6696884@127.0.0.1:1443/ZLKDATA
SOURCE_DATABASE_NAME=ZLKDATA

# 数据库连接池配置
DB_MAX_CONNECTIONS=10
DB_MIN_CONNECTIONS=2
DB_CONNECTION_TIMEOUT=30
DB_IDLE_TIMEOUT=600

# ===========================================
# 服务器配置 (测试环境)
# ===========================================

# 服务器监听配置
SERVER_HOST=0.0.0.0
SERVER_PORT=14444

# JWT配置 (测试环境)
JWT_SECRET=test-environment-jwt-secret-key-for-u8-extend-system
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# ===========================================
# 日志配置 (测试环境)
# ===========================================

# 测试环境日志级别
RUST_LOG=info
LOG_FORMAT=pretty

# ===========================================
# 应用配置 (测试环境)
# ===========================================

# 应用环境
APP_ENV=testing

# 测试环境启用调试
DEBUG=true

# 测试环境跨域配置（已移至安全配置部分）
# CORS_ORIGINS=http://localhost:3000,http://localhost:1444,http://*************:3000,http://*************:8080,

# ===========================================
# 安全配置 (测试环境)
# ===========================================

# 环境标识（设置为 production 将强制启用权限检查）
RUST_ENV=testing

# 权限检查配置（测试环境建议启用以验证权限逻辑）
ENABLE_PERMISSION_CHECK=false
ENABLE_RATE_LIMIT=true
ENABLE_CORS=true

# 密码加密轮数 (测试环境可以较低)
BCRYPT_ROUNDS=10

# 会话超时时间
SESSION_TIMEOUT=7200

# 登录安全配置 (测试环境较宽松)
MAX_LOGIN_ATTEMPTS=10
LOGIN_LOCKOUT_DURATION=300

# CORS配置（测试环境）
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:1444,http://*************:3000,http://*************:8080
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization

# ===========================================
# 管理员配置 (测试环境)
# ===========================================

# 测试环境管理员账户
ADMIN_USERNAME=admin
ADMIN_PASSWORD=1234
ADMIN_EMAIL=<EMAIL>

# ===========================================
# 文件上传配置 (测试环境)
# ===========================================

# 文件上传目录
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx,xls,xlsx

# ===========================================
# 缓存配置 (测试环境)
# ===========================================

# 缓存过期时间
CACHE_TTL=1800

# ===========================================
# 监控配置 (测试环境)
# ===========================================

# 启用性能监控
ENABLE_METRICS=true
METRICS_PATH=/metrics
HEALTH_CHECK_PATH=/health

# ===========================================
# 开发配置 (测试环境)
# ===========================================

# 测试环境配置
HOT_RELOAD=false
SHOW_SQL_LOGS=true
ENABLE_API_DOCS=true
API_DOCS_PATH=/docs

# ===========================================
# 速率限制 (测试环境)
# ===========================================

# 测试环境较宽松的速率限制
RATE_LIMIT_REQUESTS_PER_MINUTE=120
RATE_LIMIT_BURST=20
