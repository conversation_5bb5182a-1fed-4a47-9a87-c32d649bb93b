@echo off
title U8 Extend System - NSSM Service Manager
echo ========================================
echo    U8 Extend System - NSSM Service Manager
echo ========================================
echo.

set SERVICE_NAME=U8ExtendSystem

REM Check administrator privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Administrator privileges required to manage service!
    echo [INFO] Please right-click this script and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

REM Check if NSSM exists
if not exist "nssm.exe" (
    echo [ERROR] nssm.exe file not found!
    echo [INFO] Please run download-nssm-en.bat first to download NSSM
    echo.
    pause
    exit /b 1
)

:menu
cls
echo ========================================
echo    U8 Extend System - NSSM Service Manager
echo ========================================
echo.

REM Get service status
for /f "tokens=*" %%i in ('nssm status %SERVICE_NAME% 2^>nul') do set SERVICE_STATUS=%%i
if "%SERVICE_STATUS%"=="" set SERVICE_STATUS=Not installed

echo [Current Status] %SERVICE_STATUS%
echo.
echo [Management Options]
echo   1. Check service status
echo   2. Start service
echo   3. Stop service
echo   4. Restart service
echo   5. View service logs
echo   6. Edit service configuration
echo   7. Uninstall service
echo   8. Install service
echo   9. Manage logs (rotate/clean/archive)
echo  10. Update service (publish new version)
echo  11. Remove service completely
echo   0. Exit
echo.
set /p choice="Please select operation (0-11): "

if "%choice%"=="1" goto :status
if "%choice%"=="2" goto :start
if "%choice%"=="3" goto :stop
if "%choice%"=="4" goto :restart
if "%choice%"=="5" goto :logs
if "%choice%"=="6" goto :config
if "%choice%"=="7" goto :uninstall
if "%choice%"=="8" goto :install
if "%choice%"=="9" goto :manage_logs
if "%choice%"=="10" goto :update_service
if "%choice%"=="11" goto :remove_service
if "%choice%"=="0" goto :exit
echo [ERROR] Invalid choice!
pause
goto :menu

:status
echo.
echo [Service Status]
nssm status %SERVICE_NAME%
echo.
echo [Detailed Information]
sc query %SERVICE_NAME%
echo.
pause
goto :menu

:start
echo.
echo [Start Service]
nssm start %SERVICE_NAME%
if %errorLevel% equ 0 (
    echo [SUCCESS] Service started successfully
) else (
    echo [ERROR] Service start failed
)
echo.
pause
goto :menu

:stop
echo.
echo [Stop Service]
nssm stop %SERVICE_NAME%
if %errorLevel% equ 0 (
    echo [SUCCESS] Service stopped successfully
) else (
    echo [ERROR] Service stop failed
)
echo.
pause
goto :menu

:restart
echo.
echo [Restart Service]
nssm restart %SERVICE_NAME%
if %errorLevel% equ 0 (
    echo [SUCCESS] Service restarted successfully
) else (
    echo [ERROR] Service restart failed
)
echo.
pause
goto :menu

:logs
echo.
echo [View Logs]
if exist "logs\service.log" (
    echo [Latest Log Content]
    echo ----------------------------------------
    powershell -Command "Get-Content 'logs\service.log' -Tail 20"
    echo ----------------------------------------
    echo.
    echo Full log file location: %~dp0logs\service.log
) else (
    echo [INFO] Log file does not exist: logs\service.log
)
echo.
if exist "logs\service_error.log" (
    echo [Error Log]
    echo ----------------------------------------
    powershell -Command "Get-Content 'logs\service_error.log' -Tail 10"
    echo ----------------------------------------
) else (
    echo [INFO] Error log file does not exist
)
echo.
pause
goto :menu

:config
echo.
echo [Edit Service Configuration]
nssm edit %SERVICE_NAME%
echo.
pause
goto :menu

:uninstall
echo.
echo [Uninstall Service]
echo WARNING: This will completely remove the service!
set /p confirm="Confirm uninstall? (y/N): "
if /i "%confirm%"=="y" (
    nssm stop %SERVICE_NAME% >nul 2>&1
    nssm remove %SERVICE_NAME% confirm
    if %errorLevel% equ 0 (
        echo [SUCCESS] Service uninstalled successfully
    ) else (
        echo [ERROR] Service uninstall failed
    )
) else (
    echo [CANCELLED] Uninstall operation cancelled
)
echo.
pause
goto :menu

:install
echo.
echo [Install Service]
echo [INFO] Please run deploy-service-en.bat for complete installation
echo.
pause
goto :menu

:manage_logs
echo.
echo [Log Management]
if exist "manage-logs-en.bat" (
    echo [INFO] Starting log management tool...
    call manage-logs-en.bat
) else (
    echo [ERROR] Log management script not found: manage-logs-en.bat
    echo [INFO] Please ensure all management scripts are in the same directory
)
echo.
pause
goto :menu

:update_service
echo.
echo [Update Service]
if exist "publish-service-en.bat" (
    echo [INFO] Starting service update tool...
    call publish-service-en.bat
) else (
    echo [ERROR] Service update script not found: publish-service-en.bat
    echo [INFO] Please ensure all management scripts are in the same directory
)
echo.
pause
goto :menu

:remove_service
echo.
echo [Remove Service]
if exist "remove-service-en.bat" (
    echo [INFO] Starting service removal tool...
    call remove-service-en.bat
) else (
    echo [ERROR] Service removal script not found: remove-service-en.bat
    echo [INFO] Please ensure all management scripts are in the same directory
)
echo.
pause
goto :menu

:exit
echo.
echo [EXIT] Thank you for using U8 Extend System Service Manager
echo.
pause
exit /b 0
