@echo off
title U8 Extend System - One-Click Service Deployment
echo ========================================
echo    U8 Extend System - One-Click Service Deployment
echo ========================================
echo.

REM Check administrator privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Administrator privileges required!
    echo [INFO] Please right-click this script and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo [INFO] This script will:
echo   1. Download NSSM tool (if not exists)
echo   2. Configure environment file
echo   3. Install U8 Extend System as Windows service
echo   4. Start the service
echo.
echo Do you want to continue? (y/N)
set /p confirm="Please enter choice: "
if /i not "%confirm%"=="y" (
    echo [CANCELLED] Deployment cancelled
    pause
    exit /b 0
)

echo.
echo ========================================
echo [Step 1] Checking NSSM tool...
echo ========================================

REM Check for NSSM in current directory or subdirectories
set NSSM_PATH=
if exist "nssm.exe" (
    set NSSM_PATH=nssm.exe
    echo [INFO] Found NSSM in current directory
) else if exist "win64\nssm.exe" (
    set NSSM_PATH=win64\nssm.exe
    echo [INFO] Found NSSM in win64 subdirectory
) else if exist "nssm-2.24\win64\nssm.exe" (
    set NSSM_PATH=nssm-2.24\win64\nssm.exe
    echo [INFO] Found NSSM in nssm-2.24\win64 subdirectory
) else (
    echo [ERROR] NSSM not found!
    echo [INFO] Please ensure nssm.exe is in one of these locations:
    echo   - Current directory: nssm.exe
    echo   - Subdirectory: win64\nssm.exe
    echo   - Extracted: nssm-2.24\win64\nssm.exe
    pause
    exit /b 1
)

echo [INFO] Using NSSM: %NSSM_PATH%
%NSSM_PATH% version

set SERVICE_NAME=U8ExtendSystem

echo.
echo ========================================
echo [Step 1.5] Removing existing service (if any)...
echo ========================================

REM Check if service exists and remove it
%NSSM_PATH% status %SERVICE_NAME% >nul 2>&1
if %errorLevel% equ 0 (
    echo [INFO] Found existing service, removing...
    %NSSM_PATH% stop %SERVICE_NAME% >nul 2>&1
    timeout /t 2 /nobreak >nul
    %NSSM_PATH% remove %SERVICE_NAME% confirm >nul 2>&1
    if %errorLevel% equ 0 (
        echo [SUCCESS] Existing service removed
    ) else (
        echo [WARNING] Failed to remove existing service, continuing anyway...
    )
) else (
    echo [INFO] No existing service found
)

echo.
echo ========================================
echo [Step 2] Configuring environment...
echo ========================================

if not exist ".env" (
    if exist ".env.testing" (
        copy .env.testing .env >nul
        echo [INFO] Using testing environment configuration
    ) else if exist ".env.production" (
        copy .env.production .env >nul
        echo [INFO] Using production environment configuration
        echo [WARNING] Please verify database connection settings!
    ) else if exist ".env.example" (
        copy .env.example .env >nul
        echo [INFO] Created configuration from example
        echo [WARNING] Please modify .env file before starting service!
    ) else (
        echo [ERROR] No configuration template found!
        echo [INFO] Please create .env file manually
        pause
        exit /b 1
    )
) else (
    echo [INFO] Configuration file .env already exists
)

echo.
echo ========================================
echo [Step 3] Installing Windows service...
echo ========================================

set APP_PATH=%~dp0u8_extend.exe
set WORK_DIR=%~dp0
REM Remove trailing backslash from WORK_DIR
if "%WORK_DIR:~-1%"=="\" set WORK_DIR=%WORK_DIR:~0,-1%

echo [INFO] Service name: %SERVICE_NAME%
echo [INFO] Application: %APP_PATH%
echo [INFO] Working directory: %WORK_DIR%

REM Install service
%NSSM_PATH% install %SERVICE_NAME% "%APP_PATH%"
if %errorLevel% neq 0 (
    echo [ERROR] Service installation failed!
    pause
    exit /b 1
)

REM Configure service
%NSSM_PATH% set %SERVICE_NAME% AppDirectory "%WORK_DIR%"
%NSSM_PATH% set %SERVICE_NAME% DisplayName "U8 Extend System Service"
%NSSM_PATH% set %SERVICE_NAME% Description "U8 Permission Management System Extension Service"
%NSSM_PATH% set %SERVICE_NAME% Start SERVICE_AUTO_START

REM Configure logging with enhanced daily rotation
if not exist "logs" mkdir logs
echo [INFO] Configuring enhanced daily log rotation...

REM Configure NSSM for daily log rotation with proper settings
%NSSM_PATH% set %SERVICE_NAME% AppStdout "%WORK_DIR%\logs\service.log"
%NSSM_PATH% set %SERVICE_NAME% AppStderr "%WORK_DIR%\logs\service_error.log"

REM Enable file rotation with daily and size limits
%NSSM_PATH% set %SERVICE_NAME% AppRotateFiles 1
%NSSM_PATH% set %SERVICE_NAME% AppRotateOnline 1
%NSSM_PATH% set %SERVICE_NAME% AppRotateSeconds 86400
%NSSM_PATH% set %SERVICE_NAME% AppRotateBytes 52428800

REM Set file creation mode (4 = OPEN_ALWAYS)
%NSSM_PATH% set %SERVICE_NAME% AppStdoutCreationDisposition 4
%NSSM_PATH% set %SERVICE_NAME% AppStderrCreationDisposition 4

REM Create initial log files
echo [INFO] Creating initial log files...
echo. > "logs\service.log"
echo. > "logs\service_error.log"

echo [INFO] Log rotation configured:
echo   - Daily rotation: Every 24 hours (86400 seconds)
echo   - Size limit: 50MB per file
echo   - Online rotation: Enabled (no service restart needed)
echo   - Rotated files: service.log.1, service.log.2, etc.

REM Configure recovery
%NSSM_PATH% set %SERVICE_NAME% AppExit Default Restart
%NSSM_PATH% set %SERVICE_NAME% AppRestartDelay 5000

echo [INFO] Service configured successfully

echo.
echo ========================================
echo [Step 4] Starting service...
echo ========================================

%NSSM_PATH% start %SERVICE_NAME%
if %errorLevel% neq 0 (
    echo [ERROR] Service start failed!
    echo [INFO] Check logs in: %WORK_DIR%logs\
    pause
    exit /b 1
)

echo [SUCCESS] Service started successfully!

echo.
echo ========================================
echo [Step 5] Verifying service...
echo ========================================

timeout /t 5 /nobreak >nul
%NSSM_PATH% status %SERVICE_NAME%

echo.
echo [INFO] Checking service logs...
if exist "logs\service.log" (
    echo [SERVICE LOG - Last 10 lines]
    powershell -Command "Get-Content 'logs\service.log' -Tail 10 -ErrorAction SilentlyContinue"
) else (
    echo [WARNING] Service log file not found
)

if exist "logs\service_error.log" (
    for /f %%i in ('powershell -Command "(Get-Content 'logs\service_error.log' -ErrorAction SilentlyContinue).Length"') do set LOG_SIZE=%%i
    if not "!LOG_SIZE!"=="0" (
        echo [ERROR LOG]
        type "logs\service_error.log"
    )
)

echo.
echo ========================================
echo DEPLOYMENT COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo [Service Information]
echo   Service name: %SERVICE_NAME%
echo   Display name: U8 Extend System Service
echo   Status: Running
echo   Startup type: Automatic (starts with Windows)
echo   Logs: %WORK_DIR%logs\
echo.
echo [Test Service]
echo   Health check: http://localhost:1444/health
echo   Web interface: http://localhost:1444/
echo.
echo [Management]
echo   Use manage-nssm-service-en.bat to manage the service
echo   Or use Windows Services (services.msc)
echo.
echo [Next Steps]
echo   1. Test the service: http://localhost:1444/health
echo   2. Configure firewall if needed
echo   3. Set up database connections
echo   4. Monitor logs in logs\ directory
echo.
echo [IMPORTANT NOTES]
echo   - NO REBOOT REQUIRED: Service is ready to use immediately
echo   - Service will auto-start when Windows boots
echo   - This completely solves the Windows console stdin hijacking issue
echo   - Service runs independently of user sessions
echo.
pause
