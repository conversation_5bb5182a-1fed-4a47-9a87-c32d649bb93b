[package]
name = "u8_extend"
version = "0.1.0"
edition = "2021"
authors = ["U8 Team"]
description = "U8 权限管理系统扩展项目"

[dependencies]
# Web 框架
tokio = { version = "1.0", features = ["full"] }
axum = { version = "0.7", features = ["macros"] }
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "trace", "timeout"] }

# 数据库
tiberius = { version = "0.12", default-features = false, features = ["tds73", "sql-browser-tokio", "bigdecimal", "time", "chrono"] }
tokio-util = { version = "0.7", features = ["compat"] }
bb8 = "0.8"
bb8-tiberius = "0.15"

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_urlencoded = "0.7"

# 时间处理
chrono = { version = "0.4", features = ["serde"] }
time = { version = "0.3", features = ["formatting", "macros"] }

# 认证和安全
jsonwebtoken = "9.0"
bcrypt = "0.15"
uuid = { version = "1.0", features = ["v4", "serde"] }
async-trait = "0.1"

# 配置管理
config = "0.14"
dotenvy = "0.15"
toml = "0.8"

# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# 日志
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "time"] }

# 定时任务
tokio-cron-scheduler = "0.10"

# HTTP 客户端
reqwest = { version = "0.11", features = ["json"] }
url = "2.4"

# 验证
validator = { version = "0.18", features = ["derive"] }

# 全局状态管理
lazy_static = "1.4"


# 
bigdecimal = "0.4.8"
[lib]
name = "u8_extend"
path = "src/lib.rs"

[dev-dependencies]
tokio-test = "0.4"
