# 零活录入 API Postman 集合使用说明

## 📋 概述

这个Postman集合包含了零活录入功能的完整API接口测试，包括认证、CRUD操作、权限测试、数据查询等功能。

## 🚀 快速开始

### 1. 导入集合
1. 打开Postman
2. 点击 "Import" 按钮
3. 选择 `flexible_entry_complete_api.json` 文件
4. 导入成功后会看到 "零活录入完整API集合"

### 2. 设置环境变量
创建一个新的环境或使用现有环境，设置以下变量：

```
base_url: http://localhost:8081
token: (自动设置)
admin_token: (自动设置)
leader_token: (自动设置)
manager_token: (自动设置)
user_token: (自动设置)
entry_id: (自动设置)
```

### 3. 测试流程

#### 步骤1: 认证
1. 运行 "1. 认证管理" 文件夹中的登录接口
2. 建议按顺序执行：
   - 管理员登录（获取admin_token）
   - 班长登录（获取leader_token）
   - 负责人登录（获取manager_token）
   - 普通用户登录（获取user_token）

#### 步骤2: 基本功能测试
1. 运行 "2. 零活录入管理" 文件夹中的接口
2. 按顺序执行：
   - 创建零活录入
   - 获取零活录入详情
   - 查询零活录入列表
   - 审核零活录入

#### 步骤3: 筛选查询测试
运行 "3. 筛选查询" 文件夹中的各种筛选接口

#### 步骤4: 边界测试
运行 "4. 测试用例" 文件夹中的边界和异常测试

#### 步骤5: 权限测试
运行 "5. 权限测试" 文件夹中的权限验证测试

## 📝 API 接口说明

### 认证接口
- `POST /api/auth/login` - 用户登录

### 零活录入接口
- `POST /api/flexible-entries` - 创建零活录入
- `GET /api/flexible-entries/:id` - 获取详情
- `GET /api/flexible-entries` - 分页查询列表
- `PUT /api/flexible-entries/:id/approve` - 审核零活录入

### 数据查询接口
- `GET /api/products` - 查询产品列表
- `GET /api/operations` - 查询工序列表

## 🔒 权限说明

| 角色 | 创建权限 | 查看权限 | 审核权限 |
|------|----------|----------|----------|
| **admin** | ✅ 所有 | ✅ 所有 | ✅ 所有 |
| **manager** | ✅ 允许 | ✅ 本工作中心 | ✅ 本工作中心 |
| **team_leader** | ✅ 允许 | ✅ 自己创建的 | ❌ 无权限 |
| **user** | ❌ 禁止 | ❌ 禁止 | ❌ 无权限 |

## 📊 测试数据

### 有效的产品ID
- `60110005` - 头料
- `60110007` - 其他产品

### 有效的工序ID
- `001` - 原材料入库检验
- `002` - 下料（头）
- `003` - 其他工序

### 测试用户
- `admin` / `admin123` - 管理员
- `test_leader` / `123456` - 测试班长
- `test_manager` / `123456` - 测试负责人
- `test_user` / `123456` - 测试普通用户

## ⚠️ 注意事项

1. **Token管理**: 登录后token会自动保存到环境变量中
2. **权限测试**: 确保使用正确的token进行权限测试
3. **数据验证**: 创建零活录入时使用真实存在的产品和工序ID
4. **数量范围**: 零活数量必须在1-10000之间
5. **审核限制**: 不能审核自己创建的零活录入

## 🐛 常见错误

### 401 Unauthorized
- 检查token是否正确设置
- 确认用户是否已登录

### 403 Forbidden
- 检查用户权限是否足够
- 确认操作是否符合业务规则

### 400 Bad Request
- 检查请求参数是否正确
- 确认数据格式是否符合要求

### 404 Not Found
- 检查产品ID或工序ID是否存在
- 确认零活录入ID是否正确

## 📈 测试建议

1. **按顺序测试**: 先认证，再基本功能，最后权限测试
2. **数据清理**: 测试完成后可以删除测试数据
3. **权限验证**: 重点测试不同角色的权限限制
4. **边界测试**: 测试数量边界值和无效数据
5. **工作中心隔离**: 验证不同工作中心的数据隔离

## 🔧 自定义配置

如果需要修改服务器地址或其他配置：
1. 修改环境变量中的 `base_url`
2. 根据实际情况调整测试数据
3. 更新用户凭据信息

---

**版本**: 1.0.0  
**更新时间**: 2025-07-03  
**维护者**: 零活录入开发团队
