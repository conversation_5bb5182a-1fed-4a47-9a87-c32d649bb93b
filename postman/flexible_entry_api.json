{"info": {"_postman_id": "flexible-entry-api-collection", "name": "零活录入 API 集合", "description": "零活录入功能的完整API接口集合，包含创建、查询、获取详情、审核等功能", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "认证", "item": [{"name": "管理员登录", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"admin\",\n  \"password\": \"admin123\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}}, "response": []}, {"name": "班长登录", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"test_leader\",\n  \"password\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}}, "response": []}, {"name": "负责人登录", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"test_manager\",\n  \"password\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}}, "response": []}]}, {"name": "零活录入管理", "item": [{"name": "创建零活录入", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"inventory_id\": \"60110005\",\n  \"operation_id\": \"001\",\n  \"flexible_quantity\": 100\n}"}, "url": {"raw": "{{base_url}}/api/flexible-entries", "host": ["{{base_url}}"], "path": ["api", "flexible-entries"]}}, "response": []}, {"name": "获取零活录入详情", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/flexible-entries/1", "host": ["{{base_url}}"], "path": ["api", "flexible-entries", "1"]}}, "response": []}, {"name": "查询零活录入列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/flexible-entries?page=1&page_size=10", "host": ["{{base_url}}"], "path": ["api", "flexible-entries"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}]}}, "response": []}, {"name": "按状态筛选零活录入", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/flexible-entries?status=0&page=1&page_size=10", "host": ["{{base_url}}"], "path": ["api", "flexible-entries"], "query": [{"key": "status", "value": "0", "description": "0=待审核, 1=已审核"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}]}}, "response": []}, {"name": "按产品筛选零活录入", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/flexible-entries?inventory_id=60110005&page=1&page_size=10", "host": ["{{base_url}}"], "path": ["api", "flexible-entries"], "query": [{"key": "inventory_id", "value": "60110005"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}]}}, "response": []}, {"name": "按工序筛选零活录入", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/flexible-entries?operation_id=001&page=1&page_size=10", "host": ["{{base_url}}"], "path": ["api", "flexible-entries"], "query": [{"key": "operation_id", "value": "001"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}]}}, "response": []}, {"name": "按时间范围筛选零活录入", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/flexible-entries?created_time_start=1751500000&created_time_end=1751600000&page=1&page_size=10", "host": ["{{base_url}}"], "path": ["api", "flexible-entries"], "query": [{"key": "created_time_start", "value": "1751500000", "description": "开始时间戳"}, {"key": "created_time_end", "value": "1751600000", "description": "结束时间戳"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}]}}, "response": []}, {"name": "审核零活录入", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"approved_quantity\": 80,\n  \"approval_remarks\": \"审核通过，调整数量为80\"\n}"}, "url": {"raw": "{{base_url}}/api/flexible-entries/1/approve", "host": ["{{base_url}}"], "path": ["api", "flexible-entries", "1", "approve"]}}, "response": []}]}], "variable": [{"key": "base_url", "value": "http://localhost:8081", "type": "string"}, {"key": "token", "value": "", "type": "string"}]}