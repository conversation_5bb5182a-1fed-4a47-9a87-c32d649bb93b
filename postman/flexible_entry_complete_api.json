{"info": {"_postman_id": "flexible-entry-complete-api", "name": "零活录入完整API集合", "description": "零活录入功能的完整API接口集合，包含认证、CRUD操作、权限测试、数据查询等", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "flexible-entry-api"}, "item": [{"name": "1. 认证管理", "item": [{"name": "管理员登录", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('token', response.data.token);", "    pm.environment.set('admin_token', response.data.token);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"admin\",\n  \"password\": \"admin123\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}}}, {"name": "班长登录", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('leader_token', response.data.token);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"test_leader\",\n  \"password\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}}}, {"name": "负责人登录", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('manager_token', response.data.token);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"test_manager\",\n  \"password\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}}}, {"name": "普通用户登录", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('user_token', response.data.token);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"test_user\",\n  \"password\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}}}]}, {"name": "2. 零活录入管理", "item": [{"name": "创建零活录入", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.environment.set('entry_id', response.data.id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"inventory_id\": \"60110005\",\n  \"operation_id\": \"001\",\n  \"flexible_quantity\": 100\n}"}, "url": {"raw": "{{base_url}}/api/flexible-entries", "host": ["{{base_url}}"], "path": ["api", "flexible-entries"]}}}, {"name": "获取零活录入详情", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/flexible-entries/{{entry_id}}", "host": ["{{base_url}}"], "path": ["api", "flexible-entries", "{{entry_id}}"]}}}, {"name": "查询零活录入列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/flexible-entries?page=1&page_size=10", "host": ["{{base_url}}"], "path": ["api", "flexible-entries"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}]}}}, {"name": "审核零活录入", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"approved_quantity\": 80,\n  \"approval_remarks\": \"审核通过，调整数量为80\"\n}"}, "url": {"raw": "{{base_url}}/api/flexible-entries/{{entry_id}}/approve", "host": ["{{base_url}}"], "path": ["api", "flexible-entries", "{{entry_id}}", "approve"]}}}]}, {"name": "3. 筛选查询", "item": [{"name": "按状态筛选 - 待审核", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/flexible-entries?status=0&page=1&page_size=10", "host": ["{{base_url}}"], "path": ["api", "flexible-entries"], "query": [{"key": "status", "value": "0", "description": "0=待审核"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}]}}}, {"name": "按状态筛选 - 已审核", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/flexible-entries?status=1&page=1&page_size=10", "host": ["{{base_url}}"], "path": ["api", "flexible-entries"], "query": [{"key": "status", "value": "1", "description": "1=已审核"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}]}}}, {"name": "按产品筛选", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/flexible-entries?inventory_id=60110005&page=1&page_size=10", "host": ["{{base_url}}"], "path": ["api", "flexible-entries"], "query": [{"key": "inventory_id", "value": "60110005"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}]}}}, {"name": "按工序筛选", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/flexible-entries?operation_id=001&page=1&page_size=10", "host": ["{{base_url}}"], "path": ["api", "flexible-entries"], "query": [{"key": "operation_id", "value": "001"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}]}}}, {"name": "按时间范围筛选", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/flexible-entries?created_time_start=1751500000&created_time_end=1751600000&page=1&page_size=10", "host": ["{{base_url}}"], "path": ["api", "flexible-entries"], "query": [{"key": "created_time_start", "value": "1751500000", "description": "开始时间戳"}, {"key": "created_time_end", "value": "1751600000", "description": "结束时间戳"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}]}}}]}, {"name": "4. 测试用例", "item": [{"name": "创建零活录入 - 最大数量", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"inventory_id\": \"60110005\",\n  \"operation_id\": \"003\",\n  \"flexible_quantity\": 10000\n}"}, "url": {"raw": "{{base_url}}/api/flexible-entries", "host": ["{{base_url}}"], "path": ["api", "flexible-entries"]}}}, {"name": "创建零活录入 - 无效产品（应失败）", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"inventory_id\": \"INVALID_PRODUCT\",\n  \"operation_id\": \"001\",\n  \"flexible_quantity\": 100\n}"}, "url": {"raw": "{{base_url}}/api/flexible-entries", "host": ["{{base_url}}"], "path": ["api", "flexible-entries"]}}}, {"name": "创建零活录入 - 无效数量（应失败）", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"inventory_id\": \"60110005\",\n  \"operation_id\": \"001\",\n  \"flexible_quantity\": 0\n}"}, "url": {"raw": "{{base_url}}/api/flexible-entries", "host": ["{{base_url}}"], "path": ["api", "flexible-entries"]}}}, {"name": "审核零活录入 - 拒绝", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"approved_quantity\": 0,\n  \"approval_remarks\": \"数量不符合要求，拒绝审核\"\n}"}, "url": {"raw": "{{base_url}}/api/flexible-entries/2/approve", "host": ["{{base_url}}"], "path": ["api", "flexible-entries", "2", "approve"]}}}]}, {"name": "5. 权限测试", "item": [{"name": "普通用户创建零活录入（应失败）", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{user_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"inventory_id\": \"60110005\",\n  \"operation_id\": \"001\",\n  \"flexible_quantity\": 100\n}"}, "url": {"raw": "{{base_url}}/api/flexible-entries", "host": ["{{base_url}}"], "path": ["api", "flexible-entries"]}}}, {"name": "普通用户查看零活录入（应失败）", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{user_token}}"}], "url": {"raw": "{{base_url}}/api/flexible-entries?page=1&page_size=10", "host": ["{{base_url}}"], "path": ["api", "flexible-entries"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}]}}}, {"name": "班长创建零活录入（应成功）", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{leader_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"inventory_id\": \"60110005\",\n  \"operation_id\": \"002\",\n  \"flexible_quantity\": 50\n}"}, "url": {"raw": "{{base_url}}/api/flexible-entries", "host": ["{{base_url}}"], "path": ["api", "flexible-entries"]}}}, {"name": "负责人审核零活录入（应成功）", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{manager_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"approved_quantity\": 45,\n  \"approval_remarks\": \"负责人审核通过\"\n}"}, "url": {"raw": "{{base_url}}/api/flexible-entries/3/approve", "host": ["{{base_url}}"], "path": ["api", "flexible-entries", "3", "approve"]}}}]}, {"name": "6. 数据查询", "item": [{"name": "查询产品列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/products?page=1&page_size=10", "host": ["{{base_url}}"], "path": ["api", "products"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}]}}}, {"name": "查询工序列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/operations?page=1&page_size=10", "host": ["{{base_url}}"], "path": ["api", "operations"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}]}}}]}], "variable": [{"key": "base_url", "value": "http://localhost:8081", "type": "string"}, {"key": "token", "value": "", "type": "string"}, {"key": "admin_token", "value": "", "type": "string"}, {"key": "leader_token", "value": "", "type": "string"}, {"key": "manager_token", "value": "", "type": "string"}, {"key": "user_token", "value": "", "type": "string"}, {"key": "entry_id", "value": "1", "type": "string"}]}