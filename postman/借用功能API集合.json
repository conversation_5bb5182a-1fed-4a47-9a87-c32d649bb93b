{"info": {"_postman_id": "borrow-api-collection", "name": "借用功能API集合", "description": "包含自动审核功能的完整借用API集合，支持基于工作中心dep字段的自动审核逻辑", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:8081", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}, {"key": "pending_borrow_id", "value": "", "type": "string"}], "item": [{"name": "认证登录", "item": [{"name": "班长A1登录", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('auth_token', response.data.token);", "    console.log('班长A1 Token saved');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"leader_a1\",\n    \"password\": \"123123\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}}}, {"name": "班长B1登录", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('auth_token', response.data.token);", "    console.log('班长B1 Token saved');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"leader_b1\",\n    \"password\": \"123123\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}}}]}, {"name": "借用申请", "item": [{"name": "同dep字段借用（自动通过）", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    console.log('✅ 自动审核通过！ID:', response.data.borrow_id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"member_psn_num\": \"MA003\",\n    \"start_date\": 1751472000,\n    \"end_date\": 1751479200,\n    \"remarks\": \"测试同dep字段自动审核功能\"\n}"}, "url": {"raw": "{{base_url}}/api/teams/6/borrow-request", "host": ["{{base_url}}"], "path": ["api", "teams", "6", "borrow-request"]}, "description": "班长A1借用成员A3，两者都属于dep='DEP_A'的工作中心，应该自动通过"}}, {"name": "不同dep字段借用（需要审核）", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    console.log('⏳ 需要手动审核，ID:', response.data.borrow_id);", "    pm.collectionVariables.set('pending_borrow_id', response.data.borrow_id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"member_psn_num\": \"MB001\",\n    \"start_date\": 1751479200,\n    \"end_date\": 1751486400,\n    \"remarks\": \"测试不同dep字段需要手动审核功能\"\n}"}, "url": {"raw": "{{base_url}}/api/teams/6/borrow-request", "host": ["{{base_url}}"], "path": ["api", "teams", "6", "borrow-request"]}, "description": "班长A1借用成员B1，A属于dep='DEP_A'，B属于dep='DEP_B'，需要手动审核"}}]}, {"name": "借用审核", "item": [{"name": "审核通过借用申请", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    console.log('✅ 借用申请审核通过！');", "}"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"action\": \"approve\",\n    \"remarks\": \"班长B1审核通过\"\n}"}, "url": {"raw": "{{base_url}}/api/teams/8/borrow-request/{{pending_borrow_id}}/approve", "host": ["{{base_url}}"], "path": ["api", "teams", "8", "borrow-request", "{{pending_borrow_id}}", "approve"]}, "description": "班长B1审核通过借用申请"}}, {"name": "审核拒绝借用申请", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"action\": \"reject\",\n    \"remarks\": \"班长B1拒绝此申请\"\n}"}, "url": {"raw": "{{base_url}}/api/teams/8/borrow-request/{{pending_borrow_id}}/approve", "host": ["{{base_url}}"], "path": ["api", "teams", "8", "borrow-request", "{{pending_borrow_id}}", "approve"]}, "description": "班长B1拒绝借用申请"}}]}, {"name": "借用查询", "item": [{"name": "查询班组借用申请列表", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    console.log('借用申请总数:', response.data.total);", "    response.data.requests.forEach(req => {", "        console.log(`申请ID: ${req.borrow_id}, 状态: ${req.borrow_status}, 被借用人: ${req.member_name}`);", "    });", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/teams/6/borrow-requests", "host": ["{{base_url}}"], "path": ["api", "teams", "6", "borrow-requests"]}, "description": "查询班组6的所有借用申请"}}, {"name": "查询借入成员列表", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    console.log('借入成员数量:', response.data.length);", "    response.data.forEach(member => {", "        console.log(`借入成员: ${member.member_name} (${member.member_psn_num}), 状态: ${member.borrow_status.status_description}`);", "    });", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/teams/6/borrowed-in-members", "host": ["{{base_url}}"], "path": ["api", "teams", "6", "borrowed-in-members"]}, "description": "查询班组6借入的成员列表（被借来的成员）"}}, {"name": "查询借出成员列表", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    console.log('借出成员数量:', response.data.length);", "    response.data.forEach(member => {", "        console.log(`借出成员: ${member.member_name} (${member.member_psn_num}), 借给: ${member.borrow_status.borrow_team_name}`);", "    });", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/teams/8/borrowed-out-members", "host": ["{{base_url}}"], "path": ["api", "teams", "8", "borrowed-out-members"]}, "description": "查询班组8借出的成员列表（被借走的成员）"}}]}]}