# 零活录入前端API文档

## 🔗 基础信息
- **服务器地址**: `http://localhost:8081`
- **API前缀**: `/api`
- **认证方式**: Bearer <PERSON> (JWT)

## 🔐 认证接口

### 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": {
      "cpsn_num": "admin",
      "name": "系统管理员",
      "username": "admin",
      "roles": [{"name": "admin"}],
      "permissions": ["*:*"]
    }
  }
}
```

## 📝 零活录入接口

### 1. 创建零活录入
```http
POST /api/flexible-entries
Authorization: Bearer {token}
Content-Type: application/json

{
  "inventory_id": "60110005",
  "operation_id": "001", 
  "flexible_quantity": 100
}
```

**权限要求**: `team_leader`、`manager`、`admin`

**参数说明:**
- `inventory_id`: 产品ID (必填)
- `operation_id`: 工序ID (必填)
- `flexible_quantity`: 零活数量 (必填, 范围: 1-10000)

### 2. 查询零活录入列表
```http
GET /api/flexible-entries?page=1&page_size=10&status=0&inventory_id=60110005
Authorization: Bearer {token}
```

**查询参数** (所有参数都是可选的):
- `page`: 页码，默认1
- `page_size`: 每页大小，默认10
- `status`: 状态筛选 (0=待审核, 1=已审核)
- `inventory_id`: 产品ID筛选
- `operation_id`: 工序ID筛选
- `creator_psn_num`: 创建人筛选
- `approver_psn_num`: 审核人筛选
- `created_time_start`: 创建时间开始 (时间戳)
- `created_time_end`: 创建时间结束 (时间戳)

**权限控制:**
- `admin`: 查看所有零活录入
- `manager`: 查看本工作中心的零活录入
- `team_leader`: 查看自己创建的零活录入
- `user`: 无权限

**响应示例:**
```json
{
  "code": 200,
  "data": {
    "items": [
      {
        "id": 1,
        "inventory_id": "60110005",
        "operation_id": "001",
        "flexible_quantity": 100,
        "approved_quantity": null,
        "creator_psn_num": "admin",
        "approver_psn_num": null,
        "status": 0,
        "created_time": 1751509229,
        "cinvname": "头料",
        "cInvStd": "规格1",
        "operation_name": "原材料入库检验",
        "creator_name": "系统管理员"
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 10,
    "total_pages": 1
  }
}
```

### 3. 获取零活录入详情
```http
GET /api/flexible-entries/{id}
Authorization: Bearer {token}
```

**路径参数:**
- `id`: 零活录入ID

### 4. 审核零活录入
```http
PUT /api/flexible-entries/{id}/approve
Authorization: Bearer {token}
Content-Type: application/json

{
  "approved_quantity": 80,
  "approval_remarks": "审核通过，调整数量为80"
}
```

**权限要求**: `manager`、`admin`
**业务限制**: 不能审核自己创建的记录

**参数说明:**
- `approved_quantity`: 审核数量 (0表示拒绝, >0表示通过)
- `approval_remarks`: 审核备注 (可选)

## 📊 基础数据接口

### 查询产品列表
```http
GET /api/products?page=1&page_size=50&search=头料
Authorization: Bearer {token}
```

**用途**: 前端下拉选择框数据源

### 查询工序列表
```http
GET /api/operations?page=1&page_size=50&search=检验
Authorization: Bearer {token}
```

**用途**: 前端下拉选择框数据源

## 🔒 权限说明

| 角色 | 创建 | 查看 | 审核 |
|------|------|------|------|
| `admin` | ✅ 所有 | ✅ 所有 | ✅ 所有 |
| `manager` | ✅ | ✅ 本工作中心 | ✅ 本工作中心 |
| `team_leader` | ✅ | ✅ 自己创建的 | ❌ |
| `user` | ❌ | ❌ | ❌ |

## 📋 状态说明

### 零活录入状态
- `0`: 待审核
- `1`: 已审核

## ⚠️ 错误码说明

- `200`: 成功
- `201`: 创建成功
- `400`: 请求参数错误
- `401`: 未认证
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器错误

## 💡 前端开发建议

### 1. Token管理
```javascript
// 登录后保存token
localStorage.setItem('token', response.data.token);

// 请求时添加token
headers: {
  'Authorization': `Bearer ${localStorage.getItem('token')}`
}
```

### 2. 权限控制
```javascript
// 根据用户角色显示不同功能
const userRole = user.roles[0].name;
const canCreate = ['admin', 'manager', 'team_leader'].includes(userRole);
const canApprove = ['admin', 'manager'].includes(userRole);
```

### 3. 状态显示
```javascript
const statusMap = {
  0: { text: '待审核', color: 'orange' },
  1: { text: '已审核', color: 'green' }
};
```

### 4. 时间处理
```javascript
// 时间戳转换
const formatTime = (timestamp) => {
  return new Date(timestamp * 1000).toLocaleString();
};
```

## 🧪 测试数据

### 测试用户
- `admin` / `admin123` - 管理员
- `test_leader` / `123456` - 班长
- `test_manager` / `123456` - 负责人

### 测试产品
- `60110005` - 头料
- `60110007` - 其他产品

### 测试工序
- `001` - 原材料入库检验
- `002` - 下料（头）
- `003` - 其他工序
