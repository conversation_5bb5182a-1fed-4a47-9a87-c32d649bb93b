{"info": {"_postman_id": "flexible-entry-frontend-api", "name": "零活录入前端API", "description": "零活录入功能前端开发API接口集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "认证", "item": [{"name": "用户登录", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('token', response.data.token);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"admin\",\n  \"password\": \"admin123\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}, "description": "用户登录接口\n\n**请求参数:**\n- username: 用户名\n- password: 密码\n\n**响应数据:**\n- token: JWT令牌\n- user: 用户信息（包含角色和权限）"}}]}, {"name": "零活录入", "item": [{"name": "创建零活录入", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.environment.set('entry_id', response.data.id);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"inventory_id\": \"60110005\",\n  \"operation_id\": \"001\",\n  \"flexible_quantity\": 100\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/flexible-entries", "host": ["{{base_url}}"], "path": ["api", "flexible-entries"]}, "description": "创建零活录入\n\n**权限要求:** team_leader、manager、admin\n\n**请求参数:**\n- inventory_id: 产品ID (必填)\n- operation_id: 工序ID (必填)\n- flexible_quantity: 零活数量 (必填, 1-10000)\n\n**响应数据:**\n- id: 零活录入ID"}}, {"name": "查询零活录入列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/flexible-entries?page=1&page_size=10&status=&inventory_id=&operation_id=&creator_psn_num=&approver_psn_num=&created_time_start=&created_time_end=", "host": ["{{base_url}}"], "path": ["api", "flexible-entries"], "query": [{"key": "page", "value": "1", "description": "页码，默认1"}, {"key": "page_size", "value": "10", "description": "每页大小，默认10"}, {"key": "status", "value": "", "description": "状态筛选：0=待审核，1=已审核"}, {"key": "inventory_id", "value": "", "description": "产品ID筛选"}, {"key": "operation_id", "value": "", "description": "工序ID筛选"}, {"key": "creator_psn_num", "value": "", "description": "创建人筛选"}, {"key": "approver_psn_num", "value": "", "description": "审核人筛选"}, {"key": "created_time_start", "value": "", "description": "创建时间开始（时间戳）"}, {"key": "created_time_end", "value": "", "description": "创建时间结束（时间戳）"}]}, "description": "查询零活录入列表（支持分页和筛选）\n\n**权限控制:**\n- admin: 查看所有\n- manager: 查看本工作中心\n- team_leader: 查看自己创建的\n- user: 无权限\n\n**查询参数:** 所有参数都是可选的\n- page: 页码\n- page_size: 每页大小\n- status: 状态筛选\n- inventory_id: 产品筛选\n- operation_id: 工序筛选\n- creator_psn_num: 创建人筛选\n- approver_psn_num: 审核人筛选\n- created_time_start/end: 时间范围筛选\n\n**响应数据:**\n- items: 零活录入列表\n- total: 总记录数\n- page: 当前页\n- page_size: 每页大小\n- total_pages: 总页数"}}, {"name": "获取零活录入详情", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/flexible-entries/{{entry_id}}", "host": ["{{base_url}}"], "path": ["api", "flexible-entries", "{{entry_id}}"]}, "description": "获取零活录入详情\n\n**路径参数:**\n- id: 零活录入ID\n\n**响应数据:**\n包含完整的零活录入信息和关联数据：\n- 基本信息：ID、产品、工序、数量等\n- 关联数据：产品名称、工序名称、创建人姓名等\n- 审核信息：审核状态、审核人、审核时间、审核备注等"}}, {"name": "审核零活录入", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"approved_quantity\": 80,\n  \"approval_remarks\": \"审核通过，调整数量为80\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/flexible-entries/{{entry_id}}/approve", "host": ["{{base_url}}"], "path": ["api", "flexible-entries", "{{entry_id}}", "approve"]}, "description": "审核零活录入\n\n**权限要求:** manager、admin\n**业务限制:** 不能审核自己创建的记录\n\n**路径参数:**\n- id: 零活录入ID\n\n**请求参数:**\n- approved_quantity: 审核数量 (必填, 0-原申请数量)\n- approval_remarks: 审核备注 (可选)\n\n**说明:**\n- approved_quantity=0 表示拒绝\n- approved_quantity>0 表示通过并调整数量"}}]}, {"name": "基础数据", "item": [{"name": "查询产品列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/products?page=1&page_size=50&search=", "host": ["{{base_url}}"], "path": ["api", "products"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "search", "value": "", "description": "产品名称或编码搜索"}]}, "description": "查询产品列表（用于下拉选择）\n\n**响应数据:**\n- cinvcode: 产品编码\n- cinvname: 产品名称\n- cInvStd: 产品规格"}}, {"name": "查询工序列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{base_url}}/api/operations?page=1&page_size=50&search=", "host": ["{{base_url}}"], "path": ["api", "operations"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "search", "value": "", "description": "工序名称或编码搜索"}]}, "description": "查询工序列表（用于下拉选择）\n\n**响应数据:**\n- opcode: 工序编码\n- opname: 工序名称\n- description: 工序描述"}}]}], "variable": [{"key": "base_url", "value": "http://localhost:8081", "type": "string"}, {"key": "token", "value": "", "type": "string"}, {"key": "entry_id", "value": "1", "type": "string"}]}