- 不允许将正常逻辑改为mock逻辑，比如说如下代码
```rust
  // 由于权限表结构不完整，直接返回空列表
        tracing::warn!("权限表结构不完整，返回空的自定义权限列表");
        Ok(vec![])
```要去找导致原逻辑失败的原因，然后及时返回，而不是因为表结构不完整就直接返回空列表。



- 这个系统是不允许存在模拟数据的，你再看看现在已经完成的接口内有没有存在写死的数据和或者mock的数据 ，如果有的话，要修复 ，要真实的数据测试。   
记得及时删除没有用的测试文件。  
- 一定保证接口的可用性    。 
- 你再看看现在的预制权限是否已经对目前已有的接口进行全覆盖了。   再看看对于默认没有角色的用户可以正常使用最低级的User的权限吗 。   
再看看对于User的权限列表，现在开放的合理吗，能进行最通用接口的操作吗。


再看看对于负责人 ， 班长 ，普通人的权限列表，现在开放的合理吗 ，需要进行最合理的思考。   负责人（可以操作负责人的权限）>班长（可以操作普通人的权限）>普通人（最普通的权限）

- 再进行测试的时候，一定要做全角色覆盖

再看看对于admin账户是否可以正常操作所有人的权限，是否可以正常执行所有接口的操作

再看看有没有办法可以查看到系统的所有权限。 我假设要对单个角色调整权限，现在系统是否可以正常支持。

及时删除项目下已经过时的md文档。

基于现在的优化的点，及时将可以优化的编码思维组合成提示词优化进去 real_prompt.md 里面。没有用的提示词去掉

如果涉及到接口变动，需要重新生成postman的api文档。要完整的api接口文档及环境文档，生成一个更适合前端使用的Postman集合。


- 不需要每次都生成md文档，把旧的非特别重要的文档及时清理掉


- 对于人员表  "is_leader": "1", "is_team_leader": "0", 这两个字段的相关功能去掉 ， 从数据库表结构上也删除掉 ，关于这个人员是什么角色，要从role里面查询，通过关联  role_user 来找到这个人的权限，然后返回。一定要注意，当你删除了这两个字段的时候，关于角色判断和权限判断部分你一定要审查，看看是否受这两个字段影响， 如果受到影响，需要使用role或者permission相关的字段来判断。 isfzr和isbanzhang字段,没有用，删除掉。需要根据role表来判断。 isfzr和isbanzhang的相关逻辑全部以合理的方式删除掉。

- 需要检查所有时间字段的返回，统一改为时间戳格式进行返回。
- 需要将所有的请求的事件都改为具体到时分秒的时间戳， 数据库字段也要支持时分秒，统一的全局的检查下这个问题。
- 及时查看控制台 ，修复控制台报错

再看看还有没有什么疏漏 ，查缺补漏

不允许出现故意简写接口实现，你需要自己的看看现在的逻辑是否真正的满足了需求里面的所有要求。
看看接口连贯性，是否真正实现了业务连贯性，有没有明显的BUG


工序表：operations
工作中心表： workcenter
产品表： inventory
设备表：EQ_QEQDataSel
人员表：person
指令卡：InstructionCards  
  自动提交： auto_submit
  班长自动审核：auto_leader_approve 0 false 1 true  键入数值  
  负责人自动审核：auto_manager_approve 0 false 1 true  键入数值  
  数据状态： status  键入数值
    /// 待处理 - 班长发布后，等待指定人员填写完成数量
    Pending = 0,
    /// 已提交 - 指定人员已填写完成数量并提交，等待班长审核
    Submitted = 1,
    /// 班长审核通过 - 班长审核完成，等待负责人审核
    LeaderApproved = 2,
    /// 已完成 - 负责人审核完成，所有流程结束
    Completed = 3,


指令卡审核操作记录：InstructionCardAuditLogs 
班组：Teams
班组班长：TeamLeaders
班组成员：TeamMembers
班组成员借用：TeamMemberBorrows
  借用状态  BorrowStatus： 
              /// 待同意
              Pending = 0,
              /// 已同意（借用中）
              Approved = 1,
              /// 已归还（包含过期自动归还和主动归还）
              Returned = 3,
角色表：role  里面包含permissions权限信息
权限表：  permission*
用户和角色的关联：user_roles
零活录入：FlexibleEntries
  审核状态:  status
            /// 待审核 - 班长录入后，等待负责人审核
            Pending = 0,
            /// 已审核 - 负责人审核完成
            Approved = 1,


未使用： sfc_group，group，sync_logs