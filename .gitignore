# Rust 相关
/target/
**/*.rs.bk
*.pdb
Cargo.lock

# 编译输出
debug/
release/
*.exe
*.dll
*.so
*.dylib

# IDE 相关
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统相关
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 环境变量文件
# .env
# .env.local
# .env.development
# .env.test
# .env.production

# 日志文件
*.log
logs/
log/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 临时文件
tmp/
temp/
*.tmp
*.temp

# 备份文件
*.bak
*.backup
*.old

# 测试覆盖率
coverage/
*.profraw

# 文档生成
/doc/
/docs/target/

# 包管理器
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 项目特定
config/local.toml
config/development.toml
config/production.toml
migrations/
dumps/
backups/

# 密钥和证书
*.key
*.pem
*.crt
*.p12
*.pfx

# 缓存
.cache/
*.cache

# 构建工具
.cargo/
