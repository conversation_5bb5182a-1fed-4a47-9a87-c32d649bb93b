#!/bin/bash

# U8扩展系统 - 快速构建脚本
# 专门用于开发阶段的快速增量构建

echo "⚡ 快速构建模式 - 仅编译应用代码"
echo ""

# 设置构建目标
TARGET="x86_64-pc-windows-msvc"
BINARY_NAME="u8_extend"
BUILD_DIR="target/${TARGET}/release"
DIST_DIR="dist/windows"

# 检查是否安装了 Windows 目标
if ! rustup target list --installed | grep -q "x86_64-pc-windows-msvc"; then
    echo "📦 安装 Windows 目标..."
    rustup target add x86_64-pc-windows-msvc
fi

echo "🏗️  快速编译 (跳过依赖项重新编译)..."
cargo build --release --target ${TARGET} --bin ${BINARY_NAME}

echo "📁 更新分发目录..."
mkdir -p ${DIST_DIR}

echo "📋 复制可执行文件..."
cp ${BUILD_DIR}/${BINARY_NAME}.exe ${DIST_DIR}/

echo "✅ 快速构建完成！"
echo ""
echo "📦 更新的文件: ${DIST_DIR}/${BINARY_NAME}.exe"
echo ""
echo "💡 提示:"
echo "   - 此脚本仅更新可执行文件，不更新配置文件和脚本"
echo "   - 如需完整构建包，请使用 './build-windows.sh'"
echo "   - 如遇到问题，请使用 './build-windows.sh --clean'"
echo ""
