# 班组绑定管理 API 使用指南

## 📋 概述

班组绑定管理API提供了完整的班组资源绑定功能，包括产品、设备、工序三个维度的绑定管理。通过这些API，可以为每个班组配置其可使用的资源，确保指令卡创建时只能选择班组绑定的资源。

## 🔧 API 端点总览

### 班组产品绑定
- `POST /api/team-bindings/products` - 创建产品绑定
- `POST /api/team-bindings/products/batch` - 批量创建产品绑定
- `GET /api/team-bindings/products` - 查询产品绑定列表
- `DELETE /api/team-bindings/products/{id}` - 删除产品绑定
- `PUT /api/team-bindings/products/{id}/status` - 更新产品绑定状态

### 班组设备绑定
- `POST /api/team-bindings/equipments` - 创建设备绑定
- `POST /api/team-bindings/equipments/batch` - 批量创建设备绑定
- `GET /api/team-bindings/equipments` - 查询设备绑定列表
- `DELETE /api/team-bindings/equipments/{id}` - 删除设备绑定
- `PUT /api/team-bindings/equipments/{id}/status` - 更新设备绑定状态

### 班组工序绑定
- `POST /api/team-bindings/operations` - 创建工序绑定
- `POST /api/team-bindings/operations/batch` - 批量创建工序绑定
- `GET /api/team-bindings/operations` - 查询工序绑定列表
- `DELETE /api/team-bindings/operations/{id}` - 删除工序绑定
- `PUT /api/team-bindings/operations/{id}/status` - 更新工序绑定状态

### 班组资源查询（用于指令卡创建）
- `GET /api/teams/{team_id}/available-resources` - 获取所有可用资源
- `GET /api/teams/{team_id}/available-products` - 获取可用产品列表
- `GET /api/teams/{team_id}/available-equipments` - 获取可用设备列表
- `GET /api/teams/{team_id}/available-operations` - 获取可用工序列表
- `GET /api/teams/{team_id}/validate-resources/{inventory_id}/{equipment_id}/{operation_id}` - 验证资源可用性

## 🚀 快速开始

### 1. 获取认证令牌
```bash
curl -X POST "http://localhost:8081/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "1234"}'
```

### 2. 创建班组产品绑定
```bash
curl -X POST "http://localhost:8081/api/team-bindings/products" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"team_id": 49, "inventory_id": "TEST001"}'
```

### 3. 批量创建产品绑定
```bash
curl -X POST "http://localhost:8081/api/team-bindings/products/batch" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"team_id": 48, "inventory_ids": ["PROD001", "PROD002", "PROD003"]}'
```

### 4. 查询班组可用资源
```bash
curl -X GET "http://localhost:8081/api/teams/49/available-resources" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 📊 请求参数说明

### 创建绑定请求参数
- `team_id` (integer): 班组ID，必填
- `inventory_id` (string): 产品编码，创建产品绑定时必填
- `equipment_id` (string): 设备编码，创建设备绑定时必填
- `operation_id` (string): 工序编码，创建工序绑定时必填

### 批量创建请求参数
- `team_id` (integer): 班组ID，必填
- `inventory_ids` (array): 产品编码数组，批量创建产品绑定时必填
- `equipment_ids` (array): 设备编码数组，批量创建设备绑定时必填
- `operation_ids` (array): 工序编码数组，批量创建工序绑定时必填

### 查询参数
- `team_id` (integer): 班组ID，可选
- `page` (integer): 页码，默认1
- `page_size` (integer): 每页数量，默认20
- `status` (integer): 状态筛选，1-启用，0-禁用
- `keyword` (string): 关键字搜索，可选

### 状态更新参数
- `status` (integer): 新状态，1-启用，0-禁用

## 📝 响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 具体数据内容
  }
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误信息",
  "error_code": "ERROR_CODE"
}
```

## 🔍 使用场景

### 1. 班组资源配置
管理员为每个班组配置其可使用的产品、设备、工序，确保生产任务分配的合理性。

### 2. 指令卡创建限制
在创建指令卡时，系统会调用资源查询接口，确保只能选择班组绑定的资源。

### 3. 资源权限管理
通过启用/禁用绑定状态，灵活控制班组对特定资源的使用权限。

## ⚠️ 注意事项

1. **认证要求**: 所有API都需要JWT认证，请在请求头中包含有效的Authorization token
2. **权限控制**: 建议只有管理员角色才能进行绑定管理操作
3. **数据一致性**: 系统会自动防止重复绑定，确保数据一致性
4. **软删除**: 删除操作采用软删除机制，数据不会真正删除
5. **关联查询**: 查询结果会包含关联的班组名称、产品名称等信息

## 📚 Postman 集合

已提供完整的Postman API集合文件 `team_bindings_postman_collection.json`，可直接导入Postman使用。

### 导入步骤：
1. 打开Postman
2. 点击Import按钮
3. 选择文件 `team_bindings_postman_collection.json`
4. 设置环境变量 `base_url` 和 `token`
5. 开始测试API

## 🎯 最佳实践

1. **批量操作**: 对于多个资源的绑定，建议使用批量接口提高效率
2. **状态管理**: 使用状态更新而非删除来临时禁用绑定关系
3. **资源验证**: 在指令卡创建前，使用验证接口确保资源可用性
4. **分页查询**: 对于大量数据，合理使用分页参数避免性能问题
