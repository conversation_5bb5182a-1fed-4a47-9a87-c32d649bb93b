# 🚀 U8权限管理系统开发提示词

## 📋 **核心开发原则**

### 🔒 **零模拟数据原则**
- ✅ **严禁任何模拟、硬编码、写死的数据**
- ✅ **所有数据必须来自真实数据库查询**
- ✅ **统一使用ApiResponse结构体，禁止硬编码JSON响应**

### 🎯 **权限控制完整性**
- ✅ **每个接口都有对应的权限控制**
- ✅ **权限定义完全覆盖所有现有接口**
- ✅ **角色层级清晰：Admin > Manager > TeamLeader > User**
- ✅ **默认用户自动分配User角色功能正常**

### 🏗️ **代码质量标准**
```rust
// 统一响应格式
ApiResponse::success_with_data(data).into_response()
ApiResponse::error("错误信息").into_response()

// 真实数据库查询
let permissions = permission_service.get_user_permissions(user_id).await?;
```

### 🔧 **权限系统设计**

#### **数据库排序规则要求**
- ✅ **所有自定义表的字符串字段必须使用 `Chinese_PRC_CI_AS` 排序规则**
- ✅ **与U8系统的 person 表保持排序规则一致性**
- ✅ **避免JOIN查询时的排序规则冲突错误**
- ✅ **建表语句示例：`column_name NVARCHAR(50) COLLATE Chinese_PRC_CI_AS`**

#### **角色权限层级**
1. **Admin（系统管理员）**：拥有所有权限
2. **Manager（部门负责人）**：可以管理用户和分配下级角色
3. **TeamLeader（班长/组长）**：可以查看和管理普通用户
4. **User（普通用户）**：可以查看基础信息和管理自己的资料

#### **权限资源定义**
- **auth**: 认证相关（login, logout, verify, profile）
- **users**: 用户管理（read, create, update, delete, status）
- **roles**: 角色管理（read, create, update, delete）
- **permissions**: 权限管理（read, create, update, delete）
- **mobile**: 移动端功能（login, user_info, permission_check, logout）

#### **权限检查中间件**
```rust
pub async fn get_users(
    Extension(permission_service): Extension<PermissionService>,
    headers: HeaderMap,
) -> impl IntoResponse {
    // 权限检查
    if !permission_service.check_permission(&user_id, "users", "read").await? {
        return ApiResponse::forbidden("无权限访问").into_response();
    }
    // 业务逻辑
}
```

### 🚨 **严格禁止**

#### **数据相关**
- ❌ 硬编码用户ID、角色ID、权限ID
- ❌ 写死的时间值、日期值
- ❌ 模拟的权限列表、用户列表、角色列表
- ❌ 任何包含"mock"、"fake"、"test"的数据

#### **代码相关**
- ❌ 直接返回JSON而不使用ApiResponse
- ❌ 跳过权限检查的接口
- ❌ 硬编码的配置值
- ❌ 未处理的错误情况

### ✅ **质量检查清单**

#### **开发完成后检查**
1. ✅ 所有接口都使用ApiResponse统一响应格式
2. ✅ 没有任何硬编码的数据或JSON响应
3. ✅ 所有接口都有相应的权限控制
4. ✅ 权限定义完全覆盖现有接口
5. ✅ 默认用户角色分配功能正常
6. ✅ Admin可以操作所有接口
7. ✅ 角色权限层级合理

### 🎯 **系统特点**
- **🔒 安全可靠**：完整的权限控制体系
- **📊 数据真实**：100%真实数据，0%模拟数据
- **🎯 功能完整**：覆盖所有业务需求
- **🚀 性能优良**：响应快速，稳定可靠

## 🔧 **核心技术要点**

### **数据库操作最佳实践**
1. **字段删除顺序**：先修改代码，后删除字段，确保系统稳定
2. **角色判断重构**：使用role表和user_roles表关联查询替代硬编码字段判断
3. **硬编码检查**：搜索"mock"、"fake"、"test"等关键词，确保无遗漏
4. **测试驱动优化**：通过全面测试验证优化效果

### **Rust编程技巧**
```rust
// ❌ 错误：临时值被借用
query.bind(&user_id.to_string());

// ✅ 正确：创建长生命周期的值
let user_id_str = user_id.to_string();
query.bind(&user_id_str);
```

### **时间格式统一**
```rust
// 统一的时间戳序列化
#[serde(with = "crate::utils::local_timestamp")]
pub timestamp: DateTime<chrono::Utc>,
```

## 📊 **业务数据管理API**

### **核心接口**
- **产品管理**：GET /api/products, /api/products/:cinvcode (inventory表)
- **工序管理**：GET /api/operations, /api/operations/:opcode (operation表)
- **设备管理**：GET /api/equipments, /api/equipments/:ceqcode (EQ_QEQDataSel表)

### **通用特性**
- **分页查询**：page、page_size参数，默认20条/页，最大100条/页
- **模糊查询**：所有字段支持LIKE查询，keyword支持全字段搜索
- **工作中心关联**：用户表关联workcenter返回work_center_name

**💡 核心经验：任何数据库结构变更都要确保代码完全适配，任何优化都要通过测试验证效果！**
