


工序表: operations
工作中心表： workcenter
产品表： inventory
设备表：EQ_QEQDataSel
人员表：person
指令卡：InstructionCards  
  自动提交： auto_submit
  班长自动审核：auto_leader_approve 0 false 1 true  键入数值  
  负责人自动审核：auto_manager_approve 0 false 1 true  键入数值  
  数据状态： status  键入数值
    /// 待处理 - 班长发布后，等待指定人员填写完成数量
    Pending = 0,
    /// 已提交 - 指定人员已填写完成数量并提交，等待班长审核
    Submitted = 1,
    /// 班长审核通过 - 班长审核完成，等待负责人审核
    LeaderApproved = 2,
    /// 已完成 - 负责人审核完成，所有流程结束
    Completed = 3,


指令卡审核操作记录：InstructionCardAuditLogs 
班组：Teams
班组班长：TeamLeaders
班组成员：TeamMembers
班组成员借用：TeamMemberBorrows
  借用状态  BorrowStatus： 
              /// 待同意
              Pending = 0,
              /// 已同意（借用中）
              Approved = 1,
              /// 已归还（包含过期自动归还和主动归还）
              Returned = 3,
角色表：role  里面包含permissions权限信息
权限表：  permission*
用户和角色的关联：user_roles
零活录入：FlexibleEntries
  审核状态:  status
            /// 待审核 - 班长录入后，等待负责人审核
            Pending = 0,
            /// 已审核 - 负责人审核完成
            Approved = 1,


未使用： sfc_group，group，sync_logs