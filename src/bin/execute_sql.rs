// 简单的SQL执行工具，用于执行数据库迁移脚本
use std::env;
use std::fs;
use tiberius::{Client, Config, AuthMethod};
use tokio::net::TcpStream;
use tokio_util::compat::{TokioAsyncWriteCompatExt, Compat};

type SqlConnection = Client<Compat<TcpStream>>;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 从环境变量或命令行参数获取SQL文件路径
    let args: Vec<String> = env::args().collect();
    if args.len() != 2 {
        eprintln!("用法: {} <sql_file_path>", args[0]);
        std::process::exit(1);
    }

    let sql_file_path = &args[1];
    
    // 读取SQL文件
    let sql_content = fs::read_to_string(sql_file_path)?;
    println!("读取SQL文件: {}", sql_file_path);

    // 数据库连接配置
    let mut config = Config::new();
    config.host("*************");
    config.port(1443);
    config.database("ZLKDATA");
    config.authentication(AuthMethod::sql_server("sa", "Jwaa6696884"));
    config.trust_cert();

    println!("连接到数据库: *************:1443/ZLKDATA");

    // 建立连接
    let tcp = TcpStream::connect(config.get_addr()).await?;
    tcp.set_nodelay(true)?;
    let mut client = Client::connect(config, tcp.compat_write()).await?;

    println!("数据库连接成功！");

    // 把整个文件作为一个语句执行
    let sql_statements = vec![sql_content.trim()];

    println!("找到 {} 个SQL语句", sql_statements.len());

    // 执行每个SQL语句
    for (i, sql) in sql_statements.iter().enumerate() {
        if sql.trim().is_empty() {
            continue;
        }

        println!("执行语句 {}: {}", i + 1, &sql[..std::cmp::min(100, sql.len())]);

        match client.simple_query(*sql).await {
            Ok(stream) => {
                let results = stream.into_results().await?;
                for result in results {
                    let rows = result;
                    if !rows.is_empty() {
                        println!("  -> 返回 {} 行结果", rows.len());
                        // 显示前几行结果
                        for (i, row) in rows.iter().take(10).enumerate() {
                            let mut row_data = Vec::new();
                            for col_idx in 0..row.len() {
                                if let Ok(Some(value)) = row.try_get::<&str, _>(col_idx) {
                                    row_data.push(value.to_string());
                                } else if let Ok(Some(value)) = row.try_get::<i32, _>(col_idx) {
                                    row_data.push(value.to_string());
                                } else {
                                    row_data.push("NULL".to_string());
                                }
                            }
                            println!("     行 {}: {}", i + 1, row_data.join(" | "));
                        }
                        if rows.len() > 10 {
                            println!("     ... 还有 {} 行", rows.len() - 10);
                        }
                    }
                }
                println!("  -> 执行成功");
            }
            Err(e) => {
                eprintln!("  -> 执行失败: {}", e);
                // 继续执行下一个语句，不中断
            }
        }
    }

    println!("SQL脚本执行完成！");
    Ok(())
}
