use serde::{Deserialize, Serialize};

/// 设备模型 - 对应 EQ_QEQDataSel 表
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Equipment {
    /// 设备编码
    pub ceqcode: String,
    /// 设备名称
    pub ceqname: Option<String>,
}

/// 设备查询请求
#[derive(Debug, Deserialize)]
pub struct EquipmentQueryRequest {
    /// 页码
    pub page: Option<u32>,
    /// 每页大小
    pub page_size: Option<u32>,
    /// 设备编码（模糊查询）
    pub ceqcode: Option<String>,
    /// 设备名称（模糊查询）
    pub ceqname: Option<String>,
    /// 全字段模糊查询关键词
    pub keyword: Option<String>,
}

/// 设备分页响应
#[derive(Debug, Serialize)]
pub struct EquipmentPageResponse {
    pub items: Vec<Equipment>,
    pub total: u32,
    pub page: u32,
    pub page_size: u32,
    pub total_pages: u32,
}

impl EquipmentQueryRequest {
    /// 获取页码，默认为1
    pub fn get_page(&self) -> u32 {
        self.page.unwrap_or(1).max(1)
    }

    /// 获取每页大小，默认为20，最大100
    pub fn get_page_size(&self) -> u32 {
        self.page_size.unwrap_or(20).min(200).max(1)
    }

    /// 获取偏移量
    pub fn get_offset(&self) -> u32 {
        (self.get_page() - 1) * self.get_page_size()
    }

    /// 检查是否有查询条件
    pub fn has_filters(&self) -> bool {
        self.ceqcode.is_some() 
            || self.ceqname.is_some() 
            || self.keyword.is_some()
    }
}
