use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CustomPermission {
    pub id: i64,
    pub resource_name: String,
    pub resource_label: String,
    pub description: Option<String>,
    pub category: String,
    pub icon: String,
    pub actions: String, // JSON格式的操作列表
    #[serde(with = "crate::utils::local_timestamp")]
    pub created_at: DateTime<Utc>,
    #[serde(with = "crate::utils::local_timestamp")]
    pub updated_at: DateTime<Utc>,
    pub created_by: Option<i64>,
    pub is_active: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CustomPermissionAction {
    pub name: String,
    pub label: String,
    pub description: String,
}

#[derive(Debug, Deserialize)]
pub struct CreateCustomPermissionRequest {
    pub resource_name: String,
    pub resource_label: String,
    pub description: Option<String>,
    pub category: String,
    pub icon: Option<String>,
    pub actions: Vec<CustomPermissionAction>,
}

// UpdateCustomPermissionRequest 已废弃，更新功能已整合到服务层
// #[derive(Debug, Deserialize)]
// pub struct UpdateCustomPermissionRequest {
//     pub resource_label: Option<String>,
//     pub description: Option<String>,
//     pub category: Option<String>,
//     pub icon: Option<String>,
//     pub actions: Option<Vec<CustomPermissionAction>>,
//     pub is_active: Option<bool>,
// }

impl CustomPermission {
    /// 解析actions JSON字符串为结构化数据
    pub fn get_actions(&self) -> Result<Vec<CustomPermissionAction>, serde_json::Error> {
        serde_json::from_str(&self.actions)
    }

    /// 检查是否包含指定的操作
    pub fn has_action(&self, action_name: &str) -> bool {
        if let Ok(actions) = self.get_actions() {
            actions.iter().any(|a| a.name == action_name)
        } else {
            false
        }
    }

    // 以下方法已废弃，功能已整合到服务层
    // pub fn set_actions(actions: &[CustomPermissionAction]) -> Result<String, serde_json::Error> { ... }
    // pub fn get_action_names(&self) -> Vec<String> { ... }
}
