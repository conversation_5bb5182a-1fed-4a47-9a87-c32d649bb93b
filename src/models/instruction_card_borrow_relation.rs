use serde::{Deserialize, Serialize};

/// 指令卡借用关联模型
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct InstructionCardBorrowRelation {
    /// 主键ID
    pub id: i32,
    /// 指令卡ID
    pub instruction_card_id: i64,
    /// 借用记录ID
    pub borrow_id: i32,
}

/// 创建指令卡借用关联请求DTO
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateInstructionCardBorrowRelationRequest {
    /// 指令卡ID
    pub instruction_card_id: i64,
    /// 借用记录ID
    pub borrow_id: i32,
}

/// 指令卡借用关联响应DTO
#[derive(Debug, Serialize, Deserialize)]
pub struct InstructionCardBorrowRelationResponse {
    /// 关联信息
    #[serde(flatten)]
    pub relation: InstructionCardBorrowRelation,
    /// 指令卡编号
    pub card_number: Option<String>,
    /// 借用人员编号
    pub member_psn_num: Option<String>,
    /// 借用人员姓名
    pub member_name: Option<String>,
    /// 原班组名称
    pub original_team_name: Option<String>,
    /// 借用班组名称
    pub borrow_team_name: Option<String>,
}
