use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Role {
    pub id: i64,
    pub name: String,
    pub description: Option<String>,
    pub level: i32, // 角色级别：1=admin, 2=manager, 3=team_leader, 4=user
    pub permissions: Option<String>, // 权限JSON字符串
    #[serde(with = "crate::utils::local_timestamp")]
    pub created_at: DateTime<Utc>,
    #[serde(with = "crate::utils::local_timestamp")]
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RoleResponse {
    pub id: i64,
    pub name: String,
    pub description: Option<String>,
    pub permissions: Option<serde_json::Value>,
    #[serde(with = "crate::utils::local_timestamp")]    
    pub created_at: DateTime<Utc>,
    #[serde(with = "crate::utils::local_timestamp")]    
    pub updated_at: DateTime<Utc>,
}

impl From<Role> for RoleResponse {
    fn from(role: Role) -> Self {
        // 根据level生成简单的permissions结构
        let permissions = match role.level {
            1 => Some(serde_json::json!({"level": 1, "name": "admin"})),
            2 => Some(serde_json::json!({"level": 2, "name": "manager"})),
            3 => Some(serde_json::json!({"level": 3, "name": "team_leader"})),
            _ => Some(serde_json::json!({"level": 4, "name": "user"})),
        };

        Self {
            id: role.id,
            name: role.name,
            description: role.description,
            permissions,
            created_at: role.created_at,
            updated_at: role.updated_at,
        }
    }
}

#[derive(Debug, Deserialize)]
pub struct CreateRoleRequest {
    pub name: String,
    pub description: Option<String>,
    pub permissions: Option<serde_json::Value>,
}

#[derive(Debug, Deserialize)]
pub struct UpdateRoleRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub permissions: Option<serde_json::Value>,
}

#[derive(Debug, Deserialize)]
pub struct AssignRoleRequest {
    pub user_id: String,  // 用户ID，对应cpsn_num
    pub role_id: i64,     // 角色ID
    /// 是否覆盖现有角色（默认 true，实现一对一关系）
    #[serde(default = "default_overwrite")]
    pub overwrite: bool,
    /// 对于 team_leader 角色，是否自动分配到班组（可选）
    #[serde(default)]
    pub auto_assign_team: Option<bool>,
    /// 对于 team_leader 角色，指定要分配的班组ID（可选）
    pub target_team_id: Option<i32>,
}

/// 默认覆盖设置为 true
fn default_overwrite() -> bool {
    true
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserRole {
    pub user_id: String,  // 改为String类型，对应cpsn_num
    pub role_id: i64,
    pub assigned_by: Option<String>,  // 也改为String类型
    #[serde(with = "crate::utils::local_timestamp")]    
    pub assigned_at: DateTime<Utc>,
}

// 权限定义
#[derive(Debug, Serialize, Deserialize)]
pub struct Permission {
    pub resource: String,    // 资源名称，如 "users", "roles"
    pub actions: Vec<String>, // 操作列表，如 ["read", "write", "delete"]
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RolePermissions {
    pub permissions: Vec<Permission>,
}
