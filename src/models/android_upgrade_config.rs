use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

/// 安卓升级配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AndroidUpgradeConfig {
    pub id: i32,
    pub config_json: String,
    #[serde(with = "crate::utils::local_timestamp")]    
    pub created_at: DateTime<Utc>,
}

/// 安卓升级配置响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AndroidUpgradeConfigResponse {
    pub id: i32,
    pub config_json: String,
    #[serde(with = "crate::utils::local_timestamp")]    
    pub created_at: DateTime<Utc>,
}

/// 创建安卓升级配置请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateAndroidUpgradeConfigRequest {
    pub config_json: String,
}

/// 安卓升级配置分页响应
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AndroidUpgradeConfigPageResponse {
    pub items: Vec<AndroidUpgradeConfigResponse>,
    pub total: u32,
    pub page: u32,
    pub page_size: u32,
    pub total_pages: u32,
}

/// 安卓升级配置查询请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AndroidUpgradeConfigQueryRequest {
    #[serde(default = "default_page")]
    pub page: u32,
    #[serde(default = "default_page_size")]
    pub page_size: u32,
}

fn default_page() -> u32 {
    1
}

fn default_page_size() -> u32 {
    10
}

impl AndroidUpgradeConfigQueryRequest {
    pub fn get_page(&self) -> u32 {
        if self.page == 0 { 1 } else { self.page }
    }

    pub fn get_page_size(&self) -> u32 {
        if self.page_size == 0 { 10 } else { self.page_size }
    }

    pub fn get_offset(&self) -> u32 {
        (self.get_page() - 1) * self.get_page_size()
    }
}
