use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

/// 班组产品绑定模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TeamProduct {
    /// 主键ID
    pub id: i64,
    /// 班组ID
    pub team_id: i32,
    /// 产品ID
    pub inventory_id: String,
    /// 创建人员工号
    pub created_by: Option<String>,
    /// 状态：1=启用，0=禁用
    pub status: u8,
    /// 创建时间
    #[serde(with = "crate::utils::local_timestamp")]
    pub created_at: DateTime<Utc>,
        /// 更新时间
    #[serde(with = "crate::utils::local_timestamp")]
    pub updated_at: DateTime<Utc>,
}

/// 班组设备绑定模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TeamEquipment {
    /// 主键ID
    pub id: i64,
    /// 班组ID
    pub team_id: i32,
    /// 设备ID
    pub equipment_id: String,
    /// 创建人员工号
    pub created_by: Option<String>,
    /// 状态：1=启用，0=禁用
    pub status: u8,
    /// 创建时间
    #[serde(with = "crate::utils::local_timestamp")]    
    pub created_at: DateTime<Utc>,
        /// 更新时间
    #[serde(with = "crate::utils::local_timestamp")]    
    pub updated_at: DateTime<Utc>,
}

/// 班组工序绑定模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TeamOperation {
    /// 主键ID
    pub id: i64,
    /// 班组ID
    pub team_id: i32,
    /// 工序ID
    pub operation_id: String,
    /// 创建人员工号
    pub created_by: Option<String>,
    /// 状态：1=启用，0=禁用
    pub status: u8,
    /// 创建时间
    #[serde(with = "crate::utils::local_timestamp")]
    pub created_at: DateTime<Utc>,
        /// 更新时间
    #[serde(with = "crate::utils::local_timestamp")]
    pub updated_at: DateTime<Utc>,
}

/// 班组产品绑定响应DTO（包含关联数据）
#[derive(Debug, Serialize, Deserialize)]
pub struct TeamProductResponse {
    /// 主键ID
    pub id: i64,
    /// 班组ID
    pub team_id: i32,
    /// 班组名称
    pub team_name: Option<String>,
    /// 产品ID
    pub inventory_id: String,
    /// 产品名称
    pub cinvname: Option<String>,
    /// 产品规格
    pub cinv_std: Option<String>,
    /// 创建人员工号
    pub created_by: Option<String>,
    /// 创建人姓名
    pub created_by_name: Option<String>,
    /// 状态：1=启用，0=禁用
    pub status: u8,
    /// 创建时间
    #[serde(with = "crate::utils::local_timestamp")]    
    pub created_at: DateTime<Utc>,
        /// 更新时间
    #[serde(with = "crate::utils::local_timestamp")]    
    pub updated_at: DateTime<Utc>,
}

/// 班组设备绑定响应DTO（包含关联数据）
#[derive(Debug, Serialize, Deserialize)]
pub struct TeamEquipmentResponse {
    /// 主键ID
    pub id: i64,
    /// 班组ID
    pub team_id: i32,
    /// 班组名称
    pub team_name: Option<String>,
    /// 设备ID
    pub equipment_id: String,
    /// 设备名称
    pub ceqname: Option<String>,
    /// 创建人员工号
    pub created_by: Option<String>,
    /// 创建人姓名
    pub created_by_name: Option<String>,
    /// 状态：1=启用，0=禁用
    pub status: u8,
    /// 创建时间
    #[serde(with = "crate::utils::local_timestamp")]    
    pub created_at: DateTime<Utc>,
        /// 更新时间
    #[serde(with = "crate::utils::local_timestamp")]   
    pub updated_at: DateTime<Utc>,
}

/// 班组工序绑定响应DTO（包含关联数据）
#[derive(Debug, Serialize, Deserialize)]
pub struct TeamOperationResponse {
    /// 主键ID
    pub id: i64,
    /// 班组ID
    pub team_id: i32,
    /// 班组名称
    pub team_name: Option<String>,
    /// 工序ID
    pub operation_id: String,
    /// 工序名称
    pub description: Option<String>,
    /// 创建人员工号
    pub created_by: Option<String>,
    /// 创建人姓名
    pub created_by_name: Option<String>,
    /// 状态：1=启用，0=禁用
    pub status: u8,
    /// 创建时间
    #[serde(with = "crate::utils::local_timestamp")]    
    pub created_at: DateTime<Utc>,
        /// 更新时间
    #[serde(with = "crate::utils::local_timestamp")]    
    pub updated_at: DateTime<Utc>,
}

/// 创建班组产品绑定请求DTO
#[derive(Debug, Deserialize)]
pub struct CreateTeamProductRequest {
    /// 班组ID
    pub team_id: i32,
    /// 产品ID
    pub inventory_id: String,
}

/// 创建班组设备绑定请求DTO
#[derive(Debug, Deserialize)]
pub struct CreateTeamEquipmentRequest {
    /// 班组ID
    pub team_id: i32,
    /// 设备ID
    pub equipment_id: String,
}

/// 创建班组工序绑定请求DTO
#[derive(Debug, Deserialize)]
pub struct CreateTeamOperationRequest {
    /// 班组ID
    pub team_id: i32,
    /// 工序ID
    pub operation_id: String,
}

/// 批量创建班组产品绑定请求DTO
#[derive(Debug, Deserialize)]
pub struct BatchCreateTeamProductRequest {
    /// 班组ID
    pub team_id: i32,
    /// 产品ID列表
    pub inventory_ids: Vec<String>,
}

/// 批量创建班组设备绑定请求DTO
#[derive(Debug, Deserialize)]
pub struct BatchCreateTeamEquipmentRequest {
    /// 班组ID
    pub team_id: i32,
    /// 设备ID列表
    pub equipment_ids: Vec<String>,
}

/// 批量创建班组工序绑定请求DTO
#[derive(Debug, Deserialize)]
pub struct BatchCreateTeamOperationRequest {
    /// 班组ID
    pub team_id: i32,
    /// 工序ID列表
    pub operation_ids: Vec<String>,
}

/// 班组绑定查询请求DTO
#[derive(Debug, Deserialize)]
pub struct TeamBindingQueryRequest {
    /// 页码
    pub page: Option<u32>,
    /// 每页大小
    pub page_size: Option<u32>,
    /// 班组ID（精确查询）
    pub team_id: Option<i32>,
    /// 状态过滤：1=启用，0=禁用
    pub status: Option<u8>,
    /// 全字段模糊查询关键词
    pub keyword: Option<String>,
}

/// 班组产品绑定分页响应
#[derive(Debug, Serialize)]
pub struct TeamProductPageResponse {
    pub items: Vec<TeamProductResponse>,
    pub total: u32,
    pub page: u32,
    pub page_size: u32,
    pub total_pages: u32,
}

/// 班组设备绑定分页响应
#[derive(Debug, Serialize)]
pub struct TeamEquipmentPageResponse {
    pub items: Vec<TeamEquipmentResponse>,
    pub total: u32,
    pub page: u32,
    pub page_size: u32,
    pub total_pages: u32,
}

/// 班组工序绑定分页响应
#[derive(Debug, Serialize)]
pub struct TeamOperationPageResponse {
    pub items: Vec<TeamOperationResponse>,
    pub total: u32,
    pub page: u32,
    pub page_size: u32,
    pub total_pages: u32,
}

/// 班组可选资源响应DTO（用于指令卡创建时的选择）
#[derive(Debug, Serialize)]
pub struct TeamAvailableResourcesResponse {
    /// 可选产品列表
    pub products: Vec<TeamProductResponse>,
    /// 可选设备列表
    pub equipments: Vec<TeamEquipmentResponse>,
    /// 可选工序列表
    pub operations: Vec<TeamOperationResponse>,
}

/// 更新班组绑定状态请求DTO
#[derive(Debug, Deserialize)]
pub struct UpdateTeamBindingStatusRequest {
    /// 状态：1=启用，0=禁用
    pub status: u8,
}
