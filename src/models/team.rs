use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

/// 班组定义模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Team {
    /// 班组ID (主键)
    pub team_id: i32,
    /// 班组名称
    pub team_name: String,
    /// 工作中心部门代码
    pub workcenter_dept_code: String,
    /// 班组描述
    pub description: Option<String>,
    /// 班组状态：1=启用，0=禁用
    pub status: u8,
    /// 创建时间
    #[serde(with = "crate::utils::local_timestamp")]
    pub created_at: DateTime<Utc>,
    /// 更新时间
    #[serde(with = "crate::utils::local_timestamp")]
    pub updated_at: DateTime<Utc>,
    /// 创建人员工号
    pub created_by: Option<String>,
}

/// 班长任命模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TeamLeader {
    /// 班组ID
    pub team_id: i32,
    /// 班长人员编号
    pub leader_psn_num: String,
    /// 任命时间
    #[serde(with = "crate::utils::local_timestamp")]
    pub assigned_at: DateTime<Utc>,
    /// 任命人员工号
    pub assigned_by: Option<String>,
    /// 任命状态：1=有效，0=已解除
    pub status: u8,
    /// 备注信息
    pub remarks: Option<String>,
}

/// 班组成员模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TeamMember {
    /// 班组ID
    pub team_id: i32,
    /// 成员人员编号
    pub member_psn_num: String,
    /// 加入时间
    #[serde(with = "crate::utils::local_timestamp")]
    pub joined_at: DateTime<Utc>,
    /// 分配人员工号
    pub joined_by: Option<String>,
    /// 成员状态：1=有效，0=已移除
    pub status: u8,
    /// 备注信息
    pub remarks: Option<String>,
}

/// 班组响应DTO - 包含基本信息和关联数据
#[derive(Debug, Serialize, Deserialize)]
pub struct TeamResponse {
    /// 班组ID
    pub team_id: i32,
    /// 班组名称
    pub team_name: String,
    /// 工作中心部门代码
    pub workcenter_dept_code: String,
    /// 工作中心名称 (关联查询获得)
    pub workcenter_name: Option<String>,
    /// 班组描述
    pub description: Option<String>,
    /// 班组状态：1=启用，0=禁用
    pub status: u8,
    /// 班长数量
    pub leader_count: i32,
    /// 成员数量
    pub member_count: i32,
    /// 班长信息数组
    pub leaders: Vec<TeamLeaderResponse>,
    /// 创建时间
    #[serde(with = "crate::utils::local_timestamp")]
    pub created_at: DateTime<Utc>,
    /// 更新时间
    #[serde(with = "crate::utils::local_timestamp")]
    pub updated_at: DateTime<Utc>,
    /// 创建人员工号
    pub created_by: Option<String>,
}

/// 班组详情响应DTO - 包含班长和成员列表
#[derive(Debug, Serialize, Deserialize)]
pub struct TeamDetailResponse {
    /// 班组基本信息
    #[serde(flatten)]
    pub team: TeamResponse,
    /// 班长列表
    pub leaders: Vec<TeamLeaderResponse>,
    /// 成员列表
    pub members: Vec<TeamMemberResponse>,
}

/// 班长响应DTO
#[derive(Debug, Serialize, Deserialize)]
pub struct TeamLeaderResponse {
    /// 班组ID
    pub team_id: i32,
    /// 班长人员编号
    pub leader_psn_num: String,
    /// 班长姓名 (关联查询获得)
    pub leader_name: Option<String>,
    /// 任命时间
    #[serde(with = "crate::utils::local_timestamp")]
    pub assigned_at: DateTime<Utc>,
    /// 任命状态：1=有效，0=已解除
    pub status: i16,
}

/// 班组成员响应DTO
#[derive(Debug, Serialize, Deserialize)]
pub struct TeamMemberResponse {
    /// 班组ID
    pub team_id: i32,
    /// 成员人员编号
    pub member_psn_num: String,
    /// 成员姓名 (关联查询获得)
    pub member_name: Option<String>,
    /// 加入时间
    #[serde(with = "crate::utils::local_timestamp")]
    pub joined_at: DateTime<Utc>,
    /// 分配人员工号
    pub joined_by: Option<String>,
    /// 分配人姓名 (关联查询获得)
    pub joined_by_name: Option<String>,
    /// 成员状态：1=有效，0=已移除
    pub status: i16,
}

/// 简化的班组成员响应DTO（用于零活录入指派）
#[derive(Debug, Serialize, Deserialize)]
pub struct SimpleTeamMemberResponse {
    /// 成员人员编号
    pub member_psn_num: String,
    /// 成员姓名
    pub member_name: Option<String>,
    /// 班组ID
    pub team_id: i32,
    /// 班组名称
    pub team_name: Option<String>,
    /// 工作中心名称
    pub work_center_name: Option<String>,
}

/// 用户班组信息响应DTO
#[derive(Debug, Serialize, Deserialize)]
pub struct UserTeamInfoResponse {
    /// 用户人员编号
    pub psn_num: String,
    /// 用户姓名
    pub person_name: String,
    /// 所在班组信息 (如果是成员)
    pub member_of_team: Option<TeamResponse>,
    /// 管理的班组列表 (如果是班长)
    pub leader_of_teams: Vec<TeamResponse>,
    /// 工作中心信息
    pub workcenter_dept_code: Option<String>,
    /// 工作中心名称
    pub workcenter_name: Option<String>,
}

/// 用户角色和班组协同状态响应DTO
#[derive(Debug, Serialize, Deserialize)]
pub struct UserRoleTeamStatusResponse {
    /// 用户人员编号
    pub psn_num: String,
    /// 用户姓名
    pub person_name: String,
    /// 用户ID
    pub user_id: i32,
    /// 是否拥有 team_leader 角色
    pub has_team_leader_role: bool,
    /// 管理的班组列表
    pub leader_of_teams: Vec<TeamResponse>,
    /// 所在班组信息 (如果是成员)
    pub member_of_team: Option<TeamResponse>,
    /// 用户拥有的所有角色
    pub roles: Vec<String>,
}

/// 创建班组请求DTO
#[derive(Debug, Deserialize)]
pub struct CreateTeamRequest {
    /// 班组名称
    pub team_name: String,
    /// 工作中心部门代码
    pub workcenter_dept_code: String,
    /// 班组描述
    pub description: Option<String>,
}

/// 更新班组请求DTO
#[derive(Debug, Deserialize)]
pub struct UpdateTeamRequest {
    /// 班组名称
    pub team_name: Option<String>,
    /// 班组描述
    pub description: Option<String>,
    /// 班组状态
    pub status: Option<i16>,
}

/// 任命班长请求DTO
#[derive(Debug, Deserialize)]
pub struct AssignLeaderRequest {
    /// 班长人员编号
    pub leader_psn_num: String,
    /// 备注信息
    pub remarks: Option<String>,
}

/// 统一任命班组长请求DTO（新版本）
#[derive(Debug, Deserialize)]
pub struct UnifiedAssignLeaderRequest {
    /// 班组ID
    pub team_id: i32,
    /// 班长人员编号
    pub leader_psn_num: String,
    /// 工作中心部门代码
    pub workcenter_dept_code: String,
    /// 备注信息
    pub remarks: Option<String>,
}

/// 添加班组成员请求DTO
#[derive(Debug, Deserialize)]
pub struct AddMemberRequest {
    /// 成员人员编号
    pub member_psn_num: String,
    /// 备注信息
    pub remarks: Option<String>,
}

/// 批量操作请求DTO
#[derive(Debug, Deserialize)]
pub struct BatchAssignLeadersRequest {
    /// 班长人员编号列表
    pub leader_psn_nums: Vec<String>,
    /// 备注信息
    pub remarks: Option<String>,
}

/// 批量添加成员请求DTO
#[derive(Debug, Deserialize)]
pub struct BatchAddMembersRequest {
    /// 成员人员编号列表
    pub member_psn_nums: Vec<String>,
    /// 备注信息
    pub remarks: Option<String>,
}

/// 班组列表查询参数
#[derive(Debug, Deserialize)]
pub struct TeamListQuery {
    /// 页码 (从1开始)
    pub page: Option<u32>,
    /// 每页大小
    pub page_size: Option<u32>,
    /// 工作中心筛选
    pub workcenter_dept_code: Option<String>,
    /// 班组名称关键词搜索
    pub keyword: Option<String>,
    /// 状态筛选
    pub status: Option<i16>,
}

/// 班组成员查询参数
#[derive(Debug, Deserialize)]
pub struct TeamMemberQuery {
    /// 成员姓名模糊查询
    pub member_name: Option<String>,
}

/// 班级可用成员查询请求
#[derive(Debug, Deserialize)]
pub struct AvailableTeamMemberQuery {
    /// 页码
    pub page: Option<u32>,
    /// 每页大小
    pub page_size: Option<u32>,
    /// 班组ID（必填）
    pub team_id: Option<i32>,
    /// 工作中心筛选（可选）
    pub work_center: Option<String>,
    /// 成员姓名模糊查询（可选）
    pub member_name: Option<String>,
    /// 姓名和id模糊
    pub keyword: Option<String>,
}

/// 可用班组成员响应DTO
#[derive(Debug, Serialize)]
pub struct AvailableTeamMemberResponse {
    /// 人员编号
    pub cpsn_num: String,
    /// 人员姓名
    pub name: String,
    /// 班组ID
    pub team_id: i32,
    /// 班组名称
    pub team_name: String,
    /// 工作中心名称
    pub work_center_name: Option<String>,
    /// 加入班组时间
    #[serde(with = "crate::utils::local_timestamp")]
    pub joined_at: DateTime<Utc>,
}

/// 可用班组成员分页响应
#[derive(Debug, Serialize)]
pub struct AvailableTeamMemberPageResponse {
    pub items: Vec<AvailableTeamMemberResponse>,
    pub total: u32,
    pub page: u32,
    pub page_size: u32,
    pub total_pages: u32,
}

/// 分页响应DTO
#[derive(Debug, Serialize)]
pub struct TeamListResponse {
    /// 班组列表
    pub teams: Vec<TeamResponse>,
    /// 总记录数
    pub total: i64,
    /// 当前页码
    pub page: u32,
    /// 每页大小
    pub page_size: u32,
    /// 总页数
    pub total_pages: u32,
}

/// 班组统计信息DTO
#[derive(Debug, Serialize)]
pub struct TeamStatistics {
    /// 总班组数
    pub total_teams: i32,
    /// 启用班组数
    pub active_teams: i32,
    /// 总班长数
    pub total_leaders: i32,
    /// 总成员数
    pub total_members: i32,
    /// 按工作中心分组的统计
    pub by_workcenter: Vec<WorkcenterTeamStats>,
}

/// 工作中心班组统计
#[derive(Debug, Serialize)]
pub struct WorkcenterTeamStats {
    /// 工作中心代码
    pub workcenter_dept_code: String,
    /// 工作中心名称
    pub workcenter_name: Option<String>,
    /// 班组数量
    pub team_count: i32,
    /// 班长数量
    pub leader_count: i32,
    /// 成员数量
    pub member_count: i32,
}
