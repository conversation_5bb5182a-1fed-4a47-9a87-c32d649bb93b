use serde::{Deserialize, Serialize};

/// 产品模型 - 对应 inventory 表
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Product {
    /// 物料编码
    pub cinvcode: String,
    /// 物料名称
    pub cinvname: Option<String>,
    /// 规格
    pub cinv_std: Option<String>,
}

/// 产品查询请求
#[derive(Debug, Deserialize)]
pub struct ProductQueryRequest {
    /// 页码
    pub page: Option<u32>,
    /// 每页大小
    pub page_size: Option<u32>,
    /// 物料编码（模糊查询）
    pub cinvcode: Option<String>,
    /// 物料名称（模糊查询）
    pub cinvname: Option<String>,
    /// 规格（模糊查询）
    pub cinv_std: Option<String>,
    /// 全字段模糊查询关键词
    pub keyword: Option<String>,
}

/// 产品分页响应
#[derive(Debug, Serialize)]
pub struct ProductPageResponse {
    pub items: Vec<Product>,
    pub total: u32,
    pub page: u32,
    pub page_size: u32,
    pub total_pages: u32,
}

impl ProductQueryRequest {
    /// 获取页码，默认为1
    pub fn get_page(&self) -> u32 {
        self.page.unwrap_or(1).max(1)
    }

    /// 获取每页大小，默认为20，最大100
    pub fn get_page_size(&self) -> u32 {
        self.page_size.unwrap_or(20).min(200).max(1)
    }

    /// 获取偏移量
    pub fn get_offset(&self) -> u32 {
        (self.get_page() - 1) * self.get_page_size()
    }

    /// 检查是否有查询条件
    pub fn has_filters(&self) -> bool {
        self.cinvcode.is_some() 
            || self.cinvname.is_some() 
            || self.cinv_std.is_some() 
            || self.keyword.is_some()
    }
}
