use serde::{Deserialize, Serialize};

/// 工作中心模型 - 对应 workcenter 表
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Workcenter {
    /// 工作中心代码
    pub dept_code: Option<String>,
    /// 工作中心描述/名称
    pub description: Option<String>,
    /// 部门字段（用于自动审核）
    pub dep: Option<String>,
    /// 工作中心负责人信息
    pub manager: Option<WorkcenterManager>,
}

/// 工作中心负责人信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkcenterManager {
    /// 人员编号
    pub psn_num: String,
    /// 人员姓名
    pub name: String,
}

/// 工作中心查询请求
#[derive(Debug, Deserialize)]
pub struct WorkcenterQueryRequest {
    /// 页码
    pub page: Option<u32>,
    /// 每页大小
    pub page_size: Option<u32>,
    /// 工作中心代码（模糊查询）
    pub dept_code: Option<String>,
    /// 工作中心描述（模糊查询）
    pub description: Option<String>,
    /// 部门字段（模糊查询）
    pub dep: Option<String>,
    /// 全字段模糊查询关键词
    pub keyword: Option<String>,
}

/// 工作中心分页响应
#[derive(Debug, Serialize)]
pub struct WorkcenterPageResponse {
    pub items: Vec<Workcenter>,
    pub total: u32,
    pub page: u32,
    pub page_size: u32,
    pub total_pages: u32,
}

impl WorkcenterQueryRequest {
    /// 获取页码，默认为1
    pub fn get_page(&self) -> u32 {
        self.page.unwrap_or(1)
    }

    /// 获取每页大小，默认为20，最大200条
    pub fn get_page_size(&self) -> u32 {
        self.page_size.unwrap_or(20).min(200).max(1) // 最大200条，最小1条
    }

    /// 获取偏移量
    pub fn get_offset(&self) -> u32 {
        (self.get_page() - 1) * self.get_page_size()
    }

    /// 检查是否有筛选条件
    pub fn has_filters(&self) -> bool {
        self.dept_code.is_some() 
            || self.description.is_some() 
            || self.dep.is_some()
            || self.keyword.is_some()
    }
}
