use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 权限操作定义
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PermissionAction {
    pub name: String,
    pub label: String,
    pub description: String,
}

/// 权限资源定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionResource {
    pub name: String,
    pub description: String,
    pub icon: String,
    pub category: String,
    pub actions: Vec<PermissionAction>,
}

/// 权限配置项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionItem {
    pub resource: String,
    pub actions: Vec<String>,
}

/// 权限模板
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionTemplate {
    pub name: String,
    pub description: String,
    pub role_level: u8,
    pub permissions: Vec<PermissionItem>,
}

/// 完整的权限配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionConfig {
    pub permissions: Vec<PermissionItem>,
    pub role_level: u8,
}

/// 权限定义响应（给前端使用）
#[derive(Debug, Serialize, Clone)]
pub struct PermissionDefinitionResponse {
    pub resources: HashMap<String, PermissionResource>,
    pub templates: HashMap<String, PermissionTemplate>,
    pub categories: Vec<String>,
}

/// 角色权限编辑请求
#[derive(Debug, Deserialize)]
pub struct UpdateRolePermissionsRequest {
    #[allow(dead_code)]
    pub permissions: Vec<PermissionItem>,
    #[allow(dead_code)]
    pub role_level: Option<u8>,
}

// ValidatePermissionsRequest 已废弃，功能已整合到权限服务中
// #[derive(Debug, Deserialize)]
// pub struct ValidatePermissionsRequest {
//     pub permissions: Vec<PermissionItem>,
//     pub role_level: u8,
// }

/// 权限验证响应
#[derive(Debug, Serialize)]
pub struct ValidatePermissionsResponse {
    pub valid: bool,
    pub errors: Vec<String>,
    pub warnings: Vec<String>,
}

/// 用户权限检查请求
#[derive(Debug, Deserialize)]
pub struct CheckPermissionRequest {
    pub user_id: String,
    pub resource: String,
    pub action: String,
}

/// 用户权限检查响应
#[derive(Debug, Serialize)]
pub struct CheckPermissionResponse {
    pub allowed: bool,
    pub reason: Option<String>,
}

/// 权限树节点（用于前端展示）
#[derive(Debug, Serialize)]
pub struct PermissionTreeNode {
    pub key: String,
    pub title: String,
    pub description: String,
    pub icon: String,
    pub category: String,
    pub children: Vec<PermissionTreeChild>,
}

/// 权限树子节点
#[derive(Debug, Serialize)]
pub struct PermissionTreeChild {
    pub key: String,
    pub title: String,
    pub description: String,
    pub selectable: bool,
}

/// 角色权限概览
#[derive(Debug, Serialize)]
pub struct RolePermissionOverview {
    pub role_id: i64,
    pub role_name: String,
    pub role_level: u8,
    pub permissions_count: usize,
    pub resources: Vec<String>,
    pub can_assign_roles: Vec<String>,
}

impl PermissionConfig {
    // 以下方法已废弃，权限检查功能已迁移到权限服务层
    // 保留结构体但移除未使用的方法以减少警告
}
