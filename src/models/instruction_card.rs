use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};


/// 指令卡状态枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
#[repr(i32)]
pub enum InstructionCardStatus {
    /// 待处理 - 班长发布后，等待指定人员填写完成数量
    Pending = 0,
    /// 已提交 - 指定人员已填写完成数量并提交，等待班长审核
    Submitted = 1,
    /// 班长审核通过 - 班长审核完成，等待负责人审核
    LeaderApproved = 2,
    /// 已完成 - 负责人审核完成，所有流程结束
    Completed = 3,
}

impl InstructionCardStatus {
    /// 从数据库整数值转换为枚举
    pub fn from_i32(value: i32) -> Option<Self> {
        match value {
            0 => Some(Self::Pending),
            1 => Some(Self::Submitted),
            2 => Some(Self::LeaderApproved),
            3 => Some(Self::Completed),
            _ => None,
        }
    }

    /// 转换为数据库整数值
    pub fn to_i32(self) -> i32 {
        self as i32
    }

    /// 获取状态描述
    pub fn description(&self) -> &'static str {
        match self {
            Self::Pending => "班长发布后，等待指定人员填写完成数量",
            Self::Submitted => "指定人员已填写完成数量并提交，等待班长审核",
            Self::LeaderApproved => "班长审核完成，等待负责人审核",
            Self::Completed => "负责人审核完成，所有流程结束",
        }
    }

    /// 获取状态名称
    pub fn name(&self) -> &'static str {
        match self {
            Self::Pending => "待处理",
            Self::Submitted => "已提交",
            Self::LeaderApproved => "班长审核通过",
            Self::Completed => "已完成",
        }
    }
}

/// 指令卡主模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstructionCard {
    /// 主键ID
    pub id: i64,
    /// 指令卡编号
    pub card_number: String,
    
    // 关联信息
    /// 产品ID
    pub inventory_id: String,
    /// 工序ID
    pub operation_id: String,
    /// 设备ID
    pub equipment_id: String,
    /// 班组ID
    pub team_id: i32,
    /// 送检单位（工作中心名称）
    pub workcenter_name: Option<String>,
    
    // 人员信息
    /// 指定人员
    pub assigned_person: String,
    /// 发布人
    pub publisher_psn_num: String,
    /// 提交人
    pub submitter_psn_num: Option<String>,
    
    // 数量信息
    /// 班产指令预制数量
    pub production_instruction_count: i32,
    /// 指定人员完成数量
    pub completed_count: Option<i32>,
    /// 班长审核自定义班产指令数量
    pub leader_custom_count: Option<i32>,
    /// 负责人审核自定义班产指令数量
    pub manager_custom_count: Option<i32>,
    
    // 时间信息
    /// 发布时间
    #[serde(with = "crate::utils::local_timestamp")]
    pub publish_time: DateTime<Utc>,
    /// 提交时间
    #[serde(with = "crate::utils::local_timestamp_option")]
    pub submit_time: Option<DateTime<Utc>>,
    /// 完成时间
    #[serde(with = "crate::utils::local_timestamp_option")]
    pub finish_time: Option<DateTime<Utc>>,
    
    // 状态信息
    /// 指令卡状态
    pub status: InstructionCardStatus,
    
    // 班长审核信息
    /// 班长审核人
    pub leader_reviewer: Option<String>,
    /// 班长审核时间
    #[serde(with = "crate::utils::local_timestamp_option")]
    pub leader_review_time: Option<DateTime<Utc>>,
    /// 班长审核备注
    pub leader_review_remarks: Option<String>,
    
    // 负责人审核信息
    /// 负责人审核人
    pub manager_reviewer: Option<String>,
    /// 负责人审核时间
    #[serde(with = "crate::utils::local_timestamp_option")]
    pub manager_review_time: Option<DateTime<Utc>>,
    /// 负责人审核备注
    pub manager_review_remarks: Option<String>,
    
    // 配置信息
    /// 是否自动提交：false 手动提交, true 自动提交
    pub auto_submit: bool,
    /// 是否班长自动审核：false 否, true 是
    pub auto_leader_approve: bool,
    /// 是否负责人自动审核：false 否, true 是
    pub auto_manager_approve: bool,
    
    // 系统信息
    /// 创建时间
    #[serde(with = "crate::utils::local_timestamp")]
    pub created_at: DateTime<Utc>,
    /// 更新时间
    #[serde(with = "crate::utils::local_timestamp")]
    pub updated_at: DateTime<Utc>,
}

/// 指令卡审核日志模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InstructionCardAuditLog {
    /// 主键ID
    pub id: i64,
    /// 指令卡ID
    pub card_id: i64,
    /// 操作类型
    pub action_type: String,
    /// 操作人
    pub operator_psn_num: String,
    /// 操作前状态
    pub old_status: Option<i32>,
    /// 操作后状态
    pub new_status: Option<i32>,
    /// 操作前数量
    pub old_count: Option<i32>,
    /// 操作后数量
    pub new_count: Option<i32>,
    /// 操作备注
    pub remarks: Option<String>,
    /// 操作时间
    #[serde(with = "crate::utils::local_timestamp")]
    pub operation_time: DateTime<Utc>,
}

/// 指令卡响应DTO - 包含关联数据
#[derive(Debug, Serialize, Deserialize)]
pub struct InstructionCardResponse {
    /// 指令卡基本信息
    #[serde(flatten)]
    pub card: InstructionCard,
    
    // 关联数据
    /// 产品名称
    pub cinvname: Option<String>,
    /// 产品规格
    #[serde(rename = "cInvStd")]
    pub cinv_std: Option<String>,
    /// 工序名称
    pub operation_name: Option<String>,
    /// 设备名称
    pub equipment_name: Option<String>,
    /// 班组名称
    pub team_name: Option<String>,
    /// 指定人员姓名
    pub assigned_person_name: Option<String>,
    /// 发布人姓名
    pub publisher_name: Option<String>,
    /// 提交人姓名
    pub submitter_name: Option<String>,
    /// 班长审核人姓名
    pub leader_reviewer_name: Option<String>,
    /// 负责人审核人姓名
    pub manager_reviewer_name: Option<String>,
}

/// 创建指令卡请求DTO
#[derive(Debug, Deserialize)]
pub struct CreateInstructionCardRequest {
    /// 产品ID
    pub inventory_id: String,
    /// 工序ID
    pub operation_id: String,
    /// 设备ID
    pub equipment_id: String,
    /// 班组ID
    pub team_id: i32,
    /// 送检单位（工作中心名称）
    pub workcenter_name: Option<String>,
    /// 指定人员
    pub assigned_person: String,
    /// 班产指令预制数量 (允许0值，如果不提供则默认为0)
    pub production_instruction_count: Option<i32>,
    /// 是否自动提交
    pub auto_submit: Option<bool>,
    /// 是否班长自动审核
    pub auto_leader_approve: Option<bool>,
    /// 是否负责人自动审核
    pub auto_manager_approve: Option<bool>,
}

/// 批量创建指令卡请求DTO
#[derive(Debug, Deserialize)]
pub struct BatchCreateInstructionCardRequest {
    /// 指令卡列表
    pub cards: Vec<CreateInstructionCardRequest>,
}

/// 提交指令卡请求DTO
#[derive(Debug, Deserialize)]
pub struct SubmitInstructionCardRequest {
    /// 指定人员完成数量
    pub completed_count: i32,
}

/// 审核指令卡请求DTO
#[derive(Debug, Deserialize)]
pub struct ReviewInstructionCardRequest {
    /// 审核自定义班产指令数量
    pub custom_count: Option<i32>,
    /// 审核备注
    pub remarks: Option<String>,
}

/// 修改班产数量请求DTO
#[derive(Debug, Deserialize)]
pub struct UpdateProductionCountRequest {
    /// 新的班产数量
    pub count: i32,
}

/// 批量审核指令卡请求DTO
#[derive(Debug, Deserialize)]
pub struct BatchReviewInstructionCardRequest {
    /// 指令卡ID列表
    pub card_ids: Vec<i64>,
    /// 审核备注
    pub remarks: Option<String>,
}

/// 指令卡查询请求DTO
#[derive(Debug, Deserialize)]
pub struct InstructionCardQueryRequest {
    /// 页码
    pub page: Option<u32>,
    /// 每页大小
    pub page_size: Option<u32>,
    /// 班组ID
    pub team_id: Option<i32>,
    /// 指定人员
    pub assigned_person: Option<String>,
    /// 发布人
    pub publisher_psn_num: Option<String>,
    /// 状态
    pub status: Option<i32>,
    /// 开始时间（时间戳）
    #[serde(default, with = "crate::utils::local_timestamp_option")]
    pub start_time: Option<DateTime<Utc>>,
    /// 结束时间（时间戳）
    #[serde(default, with = "crate::utils::local_timestamp_option")]
    pub end_time: Option<DateTime<Utc>>,
    /// 产品ID
    pub inventory_id: Option<String>,
    /// 工序ID
    pub operation_id: Option<String>,
    /// 设备ID
    pub equipment_id: Option<String>,
}

/// 指令卡分页响应DTO
#[derive(Debug, Serialize)]
pub struct InstructionCardPageResponse {
    /// 指令卡列表
    pub items: Vec<InstructionCardResponse>,
    /// 总记录数
    pub total: u32,
    /// 当前页码
    pub page: u32,
    /// 每页大小
    pub page_size: u32,
    /// 总页数
    pub total_pages: u32,
}

/// 指令卡统计信息DTO
#[derive(Debug, Serialize)]
pub struct InstructionCardStatistics {
    /// 总指令卡数
    pub total_cards: i32,
    /// 待处理数量
    pub pending_count: i32,
    /// 已提交数量
    pub submitted_count: i32,
    /// 班长审核通过数量
    pub leader_approved_count: i32,
    /// 已完成数量
    pub completed_count: i32,
}

/// 班组成员信息DTO（用于指令卡人员选择）
#[derive(Debug, Serialize, Deserialize)]
pub struct TeamMemberInfo {
    /// 人员编号
    pub psn_num: String,
    /// 人员姓名
    pub name: String,
    /// 成员类型（原班组成员/借用人员）
    pub member_type: String,
}
