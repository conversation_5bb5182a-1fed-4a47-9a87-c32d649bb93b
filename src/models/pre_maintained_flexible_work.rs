use serde::{Deserialize, Serialize};
use crate::utils::PaginatedResponse;

/// 预维护零活模型
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PreMaintainedFlexibleWork {
    pub opcode: String,
    pub description: String,
    pub workcenter_id: String,
}

/// 预维护零活响应模型
#[derive(Debug, Serialize)]
pub struct PreMaintainedFlexibleWorkResponse {
    pub opcode: String,
    pub description: String,
    pub workcenter_id: String,
    pub workcenter_name: Option<String>,
}

impl From<PreMaintainedFlexibleWork> for PreMaintainedFlexibleWorkResponse {
    fn from(work: PreMaintainedFlexibleWork) -> Self {
        Self {
            opcode: work.opcode,
            description: work.description,
            workcenter_id: work.workcenter_id,
            workcenter_name: None, // 需要通过JOIN查询获取
        }
    }
}

/// 创建预维护零活请求
#[derive(Debug, Deserialize)]
pub struct CreatePreMaintainedFlexibleWorkRequest {
    pub description: String,
}

/// 更新预维护零活请求
#[derive(Debug, Deserialize)]
pub struct UpdatePreMaintainedFlexibleWorkRequest {
    pub description: String,
}

/// 预维护零活查询请求
#[derive(Debug, Deserialize)]
pub struct PreMaintainedFlexibleWorkQueryRequest {
    pub page: Option<i32>,
    pub page_size: Option<i32>,
    pub keyword: Option<String>, // 支持opcode或description模糊查询
}

impl PreMaintainedFlexibleWorkQueryRequest {
    pub fn get_page(&self) -> i32 {
        self.page.unwrap_or(1).max(1)
    }

    pub fn get_page_size(&self) -> i32 {
        self.page_size.unwrap_or(10).clamp(1, 100)
    }

    pub fn get_offset(&self) -> i32 {
        (self.get_page() - 1) * self.get_page_size()
    }
}

/// 预维护零活分页响应
pub type PreMaintainedFlexibleWorkPaginatedResponse = PaginatedResponse<PreMaintainedFlexibleWorkResponse>;
