use serde::{Deserialize, Serialize};

/// 工序模型 - 对应 operation 表
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Operation {
    /// 工序号
    pub opcode: String,
    /// 工序名称
    pub description: Option<String>,
}

/// 工序查询请求
#[derive(Debug, Deserialize)]
pub struct OperationQueryRequest {
    /// 页码
    pub page: Option<u32>,
    /// 每页大小
    pub page_size: Option<u32>,
    /// 工序号（模糊查询）
    pub opcode: Option<String>,
    /// 工序名称（模糊查询）
    pub description: Option<String>,
    /// 全字段模糊查询关键词
    pub keyword: Option<String>,
}

/// 工序分页响应
#[derive(Debug, Serialize)]
pub struct OperationPageResponse {
    pub items: Vec<Operation>,
    pub total: u32,
    pub page: u32,
    pub page_size: u32,
    pub total_pages: u32,
}

impl OperationQueryRequest {
    /// 获取页码，默认为1
    pub fn get_page(&self) -> u32 {
        self.page.unwrap_or(1).max(1)
    }

    /// 获取每页大小，默认为20，最大100
    pub fn get_page_size(&self) -> u32 {
        self.page_size.unwrap_or(20).min(200).max(1)
    }

    /// 获取偏移量
    pub fn get_offset(&self) -> u32 {
        (self.get_page() - 1) * self.get_page_size()
    }

    /// 检查是否有查询条件
    pub fn has_filters(&self) -> bool {
        self.opcode.is_some() 
            || self.description.is_some() 
            || self.keyword.is_some()
    }
}
