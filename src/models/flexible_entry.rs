use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

/// 零活录入状态枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
#[repr(i32)]
pub enum FlexibleEntryStatus {
    /// 待审核 - 班长录入后，等待负责人审核
    Pending = 0,
    /// 已审核 - 负责人审核完成
    Approved = 1,
}

impl FlexibleEntryStatus {
    /// 从数据库值转换
    pub fn from_db_value(value: i32) -> Self {
        match value {
            0 => FlexibleEntryStatus::Pending,
            1 => FlexibleEntryStatus::Approved,
            _ => FlexibleEntryStatus::Pending, // 默认为待审核
        }
    }

    /// 转换为数据库值
    pub fn to_db_value(&self) -> i32 {
        *self as i32
    }

    /// 获取状态描述
    pub fn description(&self) -> &'static str {
        match self {
            FlexibleEntryStatus::Pending => "待审核",
            FlexibleEntryStatus::Approved => "已审核",
        }
    }
}

/// 零活录入主模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FlexibleEntry {
    /// 主键ID
    pub id: i64,
    
    // 关联信息
    /// 产品ID
    pub inventory_id: String,
    /// 工序ID
    pub operation_id: String,
    
    // 数量信息
    /// 零活数量（班长录入）
    pub flexible_quantity: i32,
    /// 审核通过的零活数量（负责人审核时填写）
    pub approved_quantity: Option<i32>,
    
    // 人员信息
    /// 录入人（班长）
    pub creator_psn_num: String,
    /// 审核人（负责人）
    pub approver_psn_num: Option<String>,
    /// 指派的班组成员
    pub assigned_member_psn_num: Option<String>,
    
    // 时间信息
    /// 录入时间
    #[serde(with = "crate::utils::local_timestamp")]
    pub created_time: DateTime<Utc>,
    /// 审核时间
    #[serde(with = "crate::utils::local_timestamp_option")]
    pub approved_time: Option<DateTime<Utc>>,
    
    // 审核信息
    /// 审核备注
    pub approval_remarks: Option<String>,
    
    // 状态信息
    /// 零活录入状态
    pub status: FlexibleEntryStatus,
    
    // 系统信息
    /// 创建时间
    #[serde(with = "crate::utils::local_timestamp")]
    pub created_at: DateTime<Utc>,
        /// 更新时间
    #[serde(with = "crate::utils::local_timestamp")]
    pub updated_at: DateTime<Utc>,
}

/// 零活录入响应DTO - 包含关联数据
#[derive(Debug, Serialize, Deserialize)]
pub struct FlexibleEntryResponse {
    /// 零活录入基本信息
    #[serde(flatten)]
    pub entry: FlexibleEntry,
    
    // 关联数据
    /// 产品名称
    pub cinvname: Option<String>,
    /// 产品规格
    #[serde(rename = "cInvStd")]
    pub cinv_std: Option<String>,
    /// 工序名称
    pub operation_name: Option<String>,
    /// 录入人姓名
    pub creator_name: Option<String>,
    /// 审核人姓名
    pub approver_name: Option<String>,
    /// 指派班组成员姓名
    pub assigned_member_name: Option<String>,
    /// 工作中心信息
    pub work_center_name: Option<String>,
}

/// 创建零活录入请求DTO
#[derive(Debug, Deserialize)]
pub struct CreateFlexibleEntryRequest {
    /// 产品ID
    pub inventory_id: Option<String>,
    /// 工序ID
    pub operation_id: String,
    /// 零活数量 (1-10000)
    pub flexible_quantity: i32,
    /// 指派的班组成员（可选）
    pub assigned_member_psn_num: Option<String>,
}

/// 审核零活录入请求DTO
#[derive(Debug, Deserialize)]
pub struct ApproveFlexibleEntryRequest {
    /// 审核通过的零活数量
    pub approved_quantity: i32,
    /// 审核备注
    pub approval_remarks: Option<String>,
}

/// 批量审核零活录入请求DTO
#[derive(Debug, Deserialize)]
pub struct BatchApproveFlexibleEntryRequest {
    /// 零活录入ID列表
    pub entry_ids: Vec<i64>,
    /// 审核备注
    pub approval_remarks: Option<String>,
}

/// 批量审核结果DTO
#[derive(Debug, Serialize)]
pub struct BatchApproveResult {
    /// 总数量
    pub total_count: usize,
    /// 成功数量
    pub success_count: usize,
    /// 失败数量
    pub failed_count: usize,
    /// 结果消息
    pub message: String,
    /// 失败的条目详情 (ID, 错误信息)
    pub failed_entries: Vec<(i64, String)>,
}

/// 零活录入查询请求DTO
#[derive(Debug, Deserialize)]
pub struct FlexibleEntryQueryRequest {
    /// 页码
    pub page: Option<u32>,
    /// 每页大小
    pub page_size: Option<u32>,
    /// 产品ID（模糊查询）
    pub inventory_id: Option<String>,
    /// 工序ID（模糊查询）
    pub operation_id: Option<String>,
    /// 状态筛选
    pub status: Option<i32>,
    /// 创建时间开始（时间戳）
    pub created_time_start: Option<i64>,
    /// 创建时间结束（时间戳）
    pub created_time_end: Option<i64>,
    /// 录入人筛选
    pub creator_psn_num: Option<String>,
    /// 审核人筛选
    pub approver_psn_num: Option<String>,
    /// 指派班组成员筛选
    pub assigned_member_psn_num: Option<String>,
    /// 指派班组成员姓名筛选（模糊查询）
    pub assigned_member_name: Option<String>,
}

/// 零活录入分页响应
#[derive(Debug, Serialize)]
pub struct FlexibleEntryPageResponse {
    pub items: Vec<FlexibleEntryResponse>,
    pub total: u32,
    pub page: u32,
    pub page_size: u32,
    pub total_pages: u32,
}

impl FlexibleEntryQueryRequest {
    /// 获取页码，默认为1
    pub fn get_page(&self) -> u32 {
        self.page.unwrap_or(1).max(1)
    }

    /// 获取每页大小，默认为20，最大100
    pub fn get_page_size(&self) -> u32 {
        self.page_size.unwrap_or(20).min(200).max(1)
    }

    /// 获取偏移量
    pub fn get_offset(&self) -> u32 {
        (self.get_page() - 1) * self.get_page_size()
    }

    /// 检查是否有查询条件
    pub fn has_filters(&self) -> bool {
        self.inventory_id.is_some() 
            || self.operation_id.is_some() 
            || self.status.is_some()
            || self.created_time_start.is_some()
            || self.created_time_end.is_some()
            || self.creator_psn_num.is_some()
            || self.approver_psn_num.is_some()
    }
}

impl CreateFlexibleEntryRequest {
    /// 验证请求数据
    pub fn validate(&self) -> Result<(), String> {
        if self.operation_id.trim().is_empty() {
            return Err("工序ID不能为空".to_string());
        }

        if self.flexible_quantity <= 0 || self.flexible_quantity > 10000 {
            return Err("零活数量必须在1-10000之间".to_string());
        }

        // 验证指派班组成员（如果提供）
        if let Some(ref member_psn_num) = self.assigned_member_psn_num {
            if member_psn_num.trim().is_empty() {
                return Err("指派班组成员不能为空字符串".to_string());
            }
        }

        Ok(())
    }
}

impl ApproveFlexibleEntryRequest {
    /// 验证请求数据
    pub fn validate(&self) -> Result<(), String> {
        if self.approved_quantity <= 0 || self.approved_quantity > 10000 {
            return Err("审核数量必须在1-10000之间".to_string());
        }

        if let Some(remarks) = &self.approval_remarks {
            if remarks.len() > 500 {
                return Err("审核备注不能超过500个字符".to_string());
            }
        }

        Ok(())
    }
}

impl BatchApproveFlexibleEntryRequest {
}
