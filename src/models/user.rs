use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use crate::models::role::RoleResponse;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct User {
    /// 主键：人员编号 (对应 cpsn_num)
    pub cpsn_num: String,
    /// 人员姓名 (对应 cPsn_Name)
    pub name: String,
    /// 雇佣状态 (对应 rEmployState)
    pub employ_state: String,
    /// 性别 (对应 rSex)
    pub sex: Option<String>,
    /// 部门编号 (对应 cDept_num)
    pub dept_num: Option<String>,
    /// 用户名 (新增字段) - 必填，如果数据库中为空则使用cpsn_num
    pub username: String,
    /// 密码哈希 (新增字段)
    pub password_hash: Option<String>,
    /// 状态：1启用，0禁用 (新增字段)
    pub status: i32,
    /// 是否管理员 (新增字段)
    pub is_admin: bool,
    /// 最后登录时间 (新增字段) - 时间戳格式
    #[serde(with = "crate::utils::local_timestamp_option")]
    pub last_login_at: Option<DateTime<Utc>>,
    /// 创建时间 (新增字段) - 时间戳格式
    #[serde(with = "crate::utils::local_timestamp_option")]
    pub created_at: Option<DateTime<Utc>>,
    /// 更新时间 (新增字段) - 时间戳格式
    #[serde(with = "crate::utils::local_timestamp_option")]
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserResponse {
    /// 人员编号 (主键)
    pub cpsn_num: String,
    /// 人员姓名
    pub name: String,
    /// 用户名
    pub username: String,
    /// 雇佣状态
    pub employ_state: String,
    /// 性别
    pub sex: Option<String>,
    /// 部门编号
    pub dept_num: Option<String>,
    /// 工作中心名称
    pub work_center_name: Option<String>,
    /// 状态：1启用，0禁用
    pub status: i32,
    /// 是否管理员
    pub is_admin: bool,
    /// 最后登录时间 - 时间戳格式
    #[serde(with = "crate::utils::local_timestamp_option")]
    pub last_login_at: Option<DateTime<Utc>>,
    /// 创建时间 - 时间戳格式
    #[serde(with = "crate::utils::local_timestamp_option")]
    pub created_at: Option<DateTime<Utc>>,
    /// 更新时间 - 时间戳格式
    #[serde(with = "crate::utils::local_timestamp_option")]
    pub updated_at: Option<DateTime<Utc>>,
    /// 角色列表
    pub roles: Option<Vec<RoleResponse>>,
    /// 班组ID
    pub team_id: Option<i32>,
    /// 班组名称
    pub team_name: Option<String>,
}

impl From<User> for UserResponse {
    fn from(user: User) -> Self {
        Self {
            cpsn_num: user.cpsn_num,
            name: user.name,
            username: user.username,
            employ_state: user.employ_state,
            sex: user.sex,
            dept_num: user.dept_num,
            work_center_name: None, // 需要单独查询
            status: user.status,
            is_admin: user.is_admin,
            last_login_at: user.last_login_at,
            created_at: user.created_at,
            updated_at: user.updated_at,
            roles: None, // 默认为None，需要单独查询
            team_id: None, // 默认为None，需要单独查询
            team_name: None, // 默认为None，需要单独查询
        }
    }
}

#[derive(Debug, Deserialize)]
pub struct LoginRequest {
    pub username: String,
    pub password: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LoginResponse {
    pub token: String,
    pub user: UserResponse,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EnhancedLoginResponse {
    pub token: String,
    pub user: EnhancedUserResponse,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EnhancedUserResponse {
    /// 人员编号 (主键)
    pub cpsn_num: String,
    /// 人员姓名
    pub name: String,
    /// 用户名
    pub username: Option<String>,
    /// 雇佣状态
    pub employ_state: String,
    /// 性别
    pub sex: Option<String>,
    /// 部门编号
    pub dept_num: Option<String>,
    /// 状态：1启用，0禁用
    pub status: i32,
    /// 是否管理员
    pub is_admin: bool,
    /// 最后登录时间 - 时间戳格式
    #[serde(with = "crate::utils::local_timestamp_option")]
    pub last_login_at: Option<DateTime<Utc>>,
    /// 创建时间 - 时间戳格式
    #[serde(with = "crate::utils::local_timestamp_option")]
    pub created_at: Option<DateTime<Utc>>,
    /// 更新时间 - 时间戳格式
    #[serde(with = "crate::utils::local_timestamp_option")]
    pub updated_at: Option<DateTime<Utc>>,
    /// 角色列表
    pub roles: Option<Vec<RoleResponse>>,
    /// 简化的权限格式，如 ["*:*", "users:read", "finance:read"]
    pub permissions: Vec<String>,
    /// 用户的权限树（根据用户权限过滤）
    pub permission_tree: Vec<serde_json::Value>,
}

#[derive(Debug, Deserialize)]
pub struct CreateUserRequest {
    /// 人员编号 (主键)
    pub cpsn_num: String,
    /// 人员姓名
    pub name: String,
    /// 用户名
    pub username: String,
    /// 密码
    pub password: String,
    /// 雇佣状态
    pub employ_state: Option<String>,
    /// 性别
    pub sex: Option<String>,
    /// 部门编号
    pub dept_num: Option<String>,
    /// 是否管理员
    pub is_admin: Option<bool>,
}

#[derive(Debug, Deserialize)]
pub struct UpdateUserRequest {
    /// 人员姓名
    pub name: Option<String>,
    /// 用户名
    pub username: Option<String>,
    /// 密码 (如果提供则更新密码)
    pub password: Option<String>,
    /// 雇佣状态
    pub employ_state: Option<String>,
    /// 性别
    pub sex: Option<String>,
    /// 部门编号
    pub dept_num: Option<String>,
}

#[derive(Debug, Deserialize, Default)]
pub struct UserQuery {
    pub page: Option<i32>,
    pub page_size: Option<i32>,
    pub username: Option<String>,
    pub cpsn_num: Option<String>,
    pub name: Option<String>,
    pub status: Option<i32>,
    pub dept_num: Option<String>,
    /// 班组ID筛选
    pub team_id: Option<i32>,
    /// 班组名称筛选（模糊查询）
    pub team_name: Option<String>,
    /// 全字段模糊查询关键词（用于cpsn_num和name字段）
    pub keyword: Option<String>,
}



// 用户权限响应结构体
#[derive(Debug, Serialize, Deserialize)]
pub struct UserPermissionsResponse {
    pub cpsn_num: String,
    pub username: Option<String>,
    pub roles: Vec<RoleResponse>,
    pub permissions: Vec<PermissionItem>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PermissionItem {
    pub resource: String,
    pub actions: Vec<String>,
    pub source_role: String, // 权限来源角色
}


