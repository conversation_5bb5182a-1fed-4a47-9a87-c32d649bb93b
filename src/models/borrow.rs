// 借用功能相关的数据模型
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

/// 借用状态枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum BorrowStatus {
    /// 待同意
    Pending = 0,
    /// 已同意（借用中）
    Approved = 1,
    /// 已拒绝
    Rejected = 2,
    /// 已归还（包含过期自动归还和主动归还）
    Returned = 3,
}

impl From<i32> for BorrowStatus {
    fn from(value: i32) -> Self {
        match value {
            0 => BorrowStatus::Pending,
            1 => BorrowStatus::Approved,
            2 => BorrowStatus::Rejected,
            3 | 4 => BorrowStatus::Returned, // 3和4都映射为已归还
            _ => BorrowStatus::Pending,
        }
    }
}

impl From<BorrowStatus> for i32 {
    fn from(status: BorrowStatus) -> Self {
        status as i32
    }
}

impl BorrowStatus {
    /// 获取状态描述
    pub fn description(&self) -> &'static str {
        match self {
            BorrowStatus::Pending => "待同意",
            BorrowStatus::Approved => "借用中",
            BorrowStatus::Rejected => "已拒绝",
            BorrowStatus::Returned => "已归还",
        }
    }
}

/// 借用申请响应DTO
#[derive(Debug, Serialize, Deserialize)]
pub struct BorrowRequestResponse {
    /// 借用ID
    pub borrow_id: i32,
    /// 被借用人员编号
    pub member_psn_num: String,
    /// 被借用人员姓名
    pub member_name: Option<String>,
    /// 原班组ID
    pub original_team_id: i32,
    /// 原班组名称
    pub original_team_name: String,
    /// 借用班组ID
    pub borrow_team_id: i32,
    /// 借用班组名称
    pub borrow_team_name: String,
    /// 借用状态
    pub borrow_status: BorrowStatus,
    /// 借用开始时间
    #[serde(with = "crate::utils::local_timestamp")]
    pub start_date: DateTime<Utc>,
    /// 借用结束时间
    #[serde(with = "crate::utils::local_timestamp")]
    pub end_date: DateTime<Utc>,
    /// 申请人
    pub requested_by: String,
    /// 申请人姓名
    pub requested_by_name: Option<String>,
    /// 审批人
    pub approved_by: Option<String>,
    /// 审批人姓名
    pub approved_by_name: Option<String>,
    /// 申请时间
    #[serde(with = "crate::utils::local_timestamp")]
    pub requested_at: DateTime<Utc>,
    /// 审批时间
    #[serde(with = "crate::utils::local_timestamp_option")]
    #[serde(skip_serializing_if = "Option::is_none")]
    pub approved_at: Option<DateTime<Utc>>,
    /// 备注
    pub remarks: Option<String>,
}

/// 创建借用申请请求DTO
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateBorrowRequest {
    /// 被借用人员编号
    pub member_psn_num: String,
    /// 借用结束时间
    #[serde(with = "crate::utils::local_timestamp")]
    pub end_date: DateTime<Utc>,
    /// 备注
    pub remarks: Option<String>,
}

/// 审批借用申请请求DTO
#[derive(Debug, Serialize, Deserialize)]
pub struct ApproveBorrowRequest {
    /// 操作类型：approve | reject
    pub action: String,
    /// 审批备注
    pub remarks: Option<String>,
}

/// 借用状态信息
#[derive(Debug, Serialize, Deserialize)]
pub struct BorrowStatusInfo {
    /// 是否被借出
    pub is_borrowed_out: bool,
    /// 是否借用来的
    pub is_borrowed_in: bool,
    /// 借用状态
    pub borrow_status: BorrowStatus,
    /// 状态描述
    pub status_description: String,
    /// 借用班组ID（如果被借出）
    pub borrow_team_id: Option<i32>,
    /// 借用班组名称（如果被借出）
    pub borrow_team_name: Option<String>,
    /// 原班组ID（如果借用来的）
    pub original_team_id: Option<i32>,
    /// 原班组名称（如果借用来的）
    pub original_team_name: Option<String>,
    /// 借用结束时间（时间戳）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub borrow_end_date: Option<i64>,
}

/// 包含借用状态的班组成员响应DTO
#[derive(Debug, Serialize, Deserialize)]
pub struct TeamMemberWithBorrowStatus {
    /// 班组ID
    pub team_id: i32,
    /// 成员人员编号
    pub member_psn_num: String,
    /// 成员姓名
    pub member_name: Option<String>,
    /// 加入时间（时间戳）
    pub joined_at: i64,
    /// 成员状态：1=有效，0=已移除
    pub status: i16,
    /// 借用状态信息
    pub borrow_status: Option<BorrowStatusInfo>,
}

/// 借用申请查询参数
#[derive(Debug, Serialize, Deserialize)]
pub struct BorrowRequestQuery {
    /// 查询类型：incoming（收到的申请）| outgoing（发出的申请）
    pub request_type: Option<String>,
    /// 借用状态过滤
    pub status: Option<i32>,
    /// 申请开始时间（时间戳）
    #[serde(default, with = "crate::utils::local_timestamp_option")]
    pub start_time: Option<DateTime<Utc>>,
    /// 申请结束时间（时间戳）
    #[serde(default, with = "crate::utils::local_timestamp_option")]
    pub end_time: Option<DateTime<Utc>>,
    /// 页码
    pub page: Option<u32>,
    /// 每页大小
    pub page_size: Option<u32>,
}

/// 借用申请列表响应DTO
#[derive(Debug, Serialize, Deserialize)]
pub struct BorrowRequestListResponse {
    /// 借用申请列表
    pub requests: Vec<BorrowRequestResponse>,
    /// 总数
    pub total: u32,
    /// 当前页
    pub page: u32,
    /// 每页大小
    pub page_size: u32,
    /// 总页数
    pub total_pages: u32,
}

/// 修改借用状态请求DTO
#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateBorrowStatusRequest {
    /// 操作类型：return（归还）
    pub action: String,
    /// 操作备注
    pub remarks: Option<String>,
}
