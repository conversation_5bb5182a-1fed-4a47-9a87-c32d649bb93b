use crate::config::database::DatabaseConfig;
use crate::models::equipment::{Equipment, EquipmentQueryRequest, EquipmentPageResponse};
use crate::utils::{AppError, AppResult};
use std::sync::Arc;
use tiberius::Query;

pub struct EquipmentService {
    db_config: Arc<DatabaseConfig>,
}

impl EquipmentService {
    pub fn new(db_config: Arc<DatabaseConfig>) -> Self {
        Self { db_config }
    }

    /// 分页查询设备列表
    pub async fn get_equipments(&self, request: EquipmentQueryRequest) -> AppResult<EquipmentPageResponse> {
        let mut client = self.db_config.get_source_connection().await?;

        // 构建WHERE条件
        let (where_clause, params) = self.build_where_clause(&request);
        
        // 查询总数
        let count_sql = format!(
            "SELECT COUNT(*) as total FROM EQ_QEQDataSel {}",
            where_clause
        );
        
        let mut count_query = Query::new(&count_sql);
        self.bind_parameters(&mut count_query, &params);
        
        let count_stream = count_query.query(&mut *client).await?;
        let count_rows: Vec<_> = count_stream.into_first_result().await?;
        let total = count_rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0) as u32;

        // 查询数据
        let data_sql = format!(
            "SELECT Ceqcode, Ceqname FROM EQ_QEQDataSel {} ORDER BY Ceqcode OFFSET {} ROWS FETCH NEXT {} ROWS ONLY",
            where_clause,
            request.get_offset(),
            request.get_page_size()
        );

        let mut data_query = Query::new(&data_sql);
        self.bind_parameters(&mut data_query, &params);
        
        let data_stream = data_query.query(&mut *client).await?;
        let data_rows: Vec<_> = data_stream.into_first_result().await?;

        let mut equipments = Vec::new();
        for row in data_rows {
            let equipment = Equipment {
                ceqcode: row.get::<&str, _>(0).unwrap_or("").to_string(),
                ceqname: row.get::<&str, _>(1).map(|s| s.to_string()),
            };
            equipments.push(equipment);
        }

        let page = request.get_page();
        let page_size = request.get_page_size();
        let total_pages = if total == 0 { 0 } else { (total + page_size - 1) / page_size };

        Ok(EquipmentPageResponse {
            items: equipments,
            total,
            page,
            page_size,
            total_pages,
        })
    }

    /// 根据设备编码获取单个设备
    pub async fn get_equipment_by_code(&self, ceqcode: &str) -> AppResult<Equipment> {
        let mut client = self.db_config.get_source_connection().await?;

        let mut query = Query::new(
            "SELECT Ceqcode, Ceqname FROM EQ_QEQDataSel WHERE Ceqcode = @P1"
        );
        query.bind(ceqcode);

        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        if let Some(row) = rows.first() {
            Ok(Equipment {
                ceqcode: row.get::<&str, _>(0).unwrap_or("").to_string(),
                ceqname: row.get::<&str, _>(1).map(|s| s.to_string()),
            })
        } else {
            Err(AppError::Business("设备不存在".to_string()))
        }
    }

    /// 构建WHERE条件
    fn build_where_clause(&self, request: &EquipmentQueryRequest) -> (String, Vec<String>) {
        let mut conditions = Vec::new();
        let mut params = Vec::new();
        let mut param_index = 1;

        // 处理具体字段查询
        if let Some(ceqcode) = &request.ceqcode {
            conditions.push(format!("Ceqcode LIKE @P{}", param_index));
            params.push(format!("%{}%", ceqcode));
            param_index += 1;
        }

        if let Some(ceqname) = &request.ceqname {
            conditions.push(format!("Ceqname LIKE @P{}", param_index));
            params.push(format!("%{}%", ceqname));
            param_index += 1;
        }

        // 处理全字段模糊查询
        if let Some(keyword) = &request.keyword {
            let keyword_conditions = vec![
                format!("Ceqcode LIKE @P{}", param_index),
                format!("Ceqname LIKE @P{}", param_index + 1),
            ];
            conditions.push(format!("({})", keyword_conditions.join(" OR ")));
            
            // 为每个字段添加相同的关键词参数
            for _ in 0..2 {
                params.push(format!("%{}%", keyword));
            }
        }

        let where_clause = if conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", conditions.join(" AND "))
        };

        (where_clause, params)
    }

    /// 绑定查询参数
    fn bind_parameters<'a>(&self, query: &mut Query<'a>, params: &'a [String]) {
        for param in params {
            query.bind(param);
        }
    }
}
