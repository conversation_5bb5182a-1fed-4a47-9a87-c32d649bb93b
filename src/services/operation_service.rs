use crate::config::database::DatabaseConfig;
use crate::models::operation::{Operation, OperationQueryRequest, OperationPageResponse};
use crate::utils::{AppError, AppResult};
use std::sync::Arc;
use tiberius::Query;

pub struct OperationService {
    db_config: Arc<DatabaseConfig>,
}

impl OperationService {
    pub fn new(db_config: Arc<DatabaseConfig>) -> Self {
        Self { db_config }
    }

    /// 分页查询工序列表
    pub async fn get_operations(&self, request: OperationQueryRequest) -> AppResult<OperationPageResponse> {
        let mut client = self.db_config.get_source_connection().await?;

        // 构建WHERE条件
        let (where_clause, params) = self.build_where_clause(&request);
        
        // 查询总数
        let count_sql = format!(
            "SELECT COUNT(*) as total FROM operation {}",
            where_clause
        );
        
        let mut count_query = Query::new(&count_sql);
        self.bind_parameters(&mut count_query, &params);
        
        let count_stream = count_query.query(&mut *client).await?;
        let count_rows: Vec<_> = count_stream.into_first_result().await?;
        let total = count_rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0) as u32;

        // 查询数据
        let data_sql = format!(
            "SELECT Opcode, Description FROM operation {} ORDER BY Opcode OFFSET {} ROWS FETCH NEXT {} ROWS ONLY",
            where_clause,
            request.get_offset(),
            request.get_page_size()
        );

        let mut data_query = Query::new(&data_sql);
        self.bind_parameters(&mut data_query, &params);
        
        let data_stream = data_query.query(&mut *client).await?;
        let data_rows: Vec<_> = data_stream.into_first_result().await?;

        let mut operations = Vec::new();
        for row in data_rows {
            let operation = Operation {
                opcode: row.get::<&str, _>(0).unwrap_or("").to_string(),
                description: row.get::<&str, _>(1).map(|s| s.to_string()),
            };
            operations.push(operation);
        }

        let page = request.get_page();
        let page_size = request.get_page_size();
        let total_pages = if total == 0 { 0 } else { (total + page_size - 1) / page_size };

        Ok(OperationPageResponse {
            items: operations,
            total,
            page,
            page_size,
            total_pages,
        })
    }

    /// 根据工序号获取单个工序
    pub async fn get_operation_by_code(&self, opcode: &str) -> AppResult<Operation> {
        let mut client = self.db_config.get_source_connection().await?;

        let mut query = Query::new(
            "SELECT Opcode, Description FROM operation WHERE Opcode = @P1"
        );
        query.bind(opcode);

        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        if let Some(row) = rows.first() {
            Ok(Operation {
                opcode: row.get::<&str, _>(0).unwrap_or("").to_string(),
                description: row.get::<&str, _>(1).map(|s| s.to_string()),
            })
        } else {
            Err(AppError::Business("工序不存在".to_string()))
        }
    }

    /// 构建WHERE条件
    fn build_where_clause(&self, request: &OperationQueryRequest) -> (String, Vec<String>) {
        let mut conditions = Vec::new();
        let mut params = Vec::new();
        let mut param_index = 1;

        // 处理具体字段查询
        if let Some(opcode) = &request.opcode {
            conditions.push(format!("Opcode LIKE @P{}", param_index));
            params.push(format!("%{}%", opcode));
            param_index += 1;
        }

        if let Some(description) = &request.description {
            conditions.push(format!("Description LIKE @P{}", param_index));
            params.push(format!("%{}%", description));
            param_index += 1;
        }

        // 处理全字段模糊查询
        if let Some(keyword) = &request.keyword {
            let keyword_conditions = vec![
                format!("Opcode LIKE @P{}", param_index),
                format!("Description LIKE @P{}", param_index + 1),
            ];
            conditions.push(format!("({})", keyword_conditions.join(" OR ")));
            
            // 为每个字段添加相同的关键词参数
            for _ in 0..2 {
                params.push(format!("%{}%", keyword));
            }
        }

        let where_clause = if conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", conditions.join(" AND "))
        };

        (where_clause, params)
    }

    /// 绑定查询参数
    fn bind_parameters<'a>(&self, query: &mut Query<'a>, params: &'a [String]) {
        for param in params {
            query.bind(param);
        }
    }
}
