use tiberius::Query;
use crate::{
    config::DatabaseConfig,
    models::workcenter::{Workcenter, WorkcenterManager, WorkcenterQueryRequest, WorkcenterPageResponse},
    utils::AppResult,
};

/// 工作中心服务
pub struct WorkcenterService {
    db_config: DatabaseConfig,
}

impl WorkcenterService {
    /// 创建新的工作中心服务实例
    pub fn new(db_config: DatabaseConfig) -> Self {
        Self { db_config }
    }

    /// 查询所有工作中心（分页）
    pub async fn query_workcenters(&self, request: WorkcenterQueryRequest) -> AppResult<WorkcenterPageResponse> {
        let mut client = self.db_config.get_app_connection().await?;
        let source_db = self.db_config.get_source_database_name();

        // 构建WHERE条件
        let (where_clause, params) = self.build_where_clause(&request);

        // 查询总数
        let count_sql = format!(
            "SELECT COUNT(*) as total FROM {}.dbo.workcenter {}",
            source_db, where_clause
        );

        let mut count_query = Query::new(&count_sql);
        self.bind_parameters(&mut count_query, &params);

        let count_stream = count_query.query(&mut *client).await?;
        let count_rows: Vec<_> = count_stream.into_first_result().await?;

        let total = count_rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0) as u32;

        // 查询数据
        let offset = request.get_offset();
        let data_sql = format!(
            r#"SELECT DeptCode, Description, dep
            FROM {}.dbo.workcenter
            {} 
            ORDER BY DeptCode
            OFFSET {} ROWS FETCH NEXT {} ROWS ONLY"#,
            source_db,
            where_clause,
            offset,
            request.get_page_size()
        );

        let mut data_query = Query::new(&data_sql);
        self.bind_parameters(&mut data_query, &params);

        let data_stream = data_query.query(&mut *client).await?;
        let data_rows: Vec<_> = data_stream.into_first_result().await?;

        let mut workcenters = Vec::new();
        for row in data_rows {
            let dept_code = row.get::<&str, _>(0).map(|s| s.to_string());
            let description = row.get::<&str, _>(1).map(|s| s.to_string());
            let dep = row.get::<&str, _>(2).map(|s| s.to_string());

            // 获取工作中心负责人（manager角色的用户）
            let manager = if let Some(ref code) = dept_code {
                self.get_workcenter_manager(code).await.unwrap_or(None)
            } else {
                None
            };

            let workcenter = Workcenter {
                dept_code,
                description,
                dep,
                manager,
            };
            workcenters.push(workcenter);
        }

        let total_pages = if total == 0 {
            0
        } else {
            (total + request.get_page_size() - 1) / request.get_page_size()
        };

        Ok(WorkcenterPageResponse {
            items: workcenters,
            total,
            page: request.get_page(),
            page_size: request.get_page_size(),
            total_pages,
        })
    }

    /// 获取所有工作中心（不分页）
    pub async fn get_all_workcenters(&self) -> AppResult<Vec<Workcenter>> {
        let mut client = self.db_config.get_app_connection().await?;
        let source_db = self.db_config.get_source_database_name();

        let sql = format!(
            "SELECT DeptCode, Description, dep FROM {}.dbo.workcenter ORDER BY DeptCode",
            source_db
        );

        let query = Query::new(&sql);
        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        let mut workcenters = Vec::new();
        for row in rows {
            let dept_code = row.get::<&str, _>(0).map(|s| s.to_string());
            let description = row.get::<&str, _>(1).map(|s| s.to_string());
            let dep = row.get::<&str, _>(2).map(|s| s.to_string());

            // 获取工作中心负责人（manager角色的用户）
            let manager = if let Some(ref code) = dept_code {
                self.get_workcenter_manager(code).await.unwrap_or(None)
            } else {
                None
            };

            let workcenter = Workcenter {
                dept_code,
                description,
                dep,
                manager,
            };
            workcenters.push(workcenter);
        }

        Ok(workcenters)
    }

    /// 根据工作中心代码获取工作中心信息
    pub async fn get_workcenter_by_code(&self, dept_code: &str) -> AppResult<Option<Workcenter>> {
        let mut client = self.db_config.get_app_connection().await?;
        let source_db = self.db_config.get_source_database_name();

        let sql = format!(
            "SELECT DeptCode, Description, dep FROM {}.dbo.workcenter WHERE DeptCode = @P1",
            source_db
        );

        let mut query = Query::new(&sql);
        query.bind(dept_code);

        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        if let Some(row) = rows.first() {
            let dept_code = row.get::<&str, _>(0).map(|s| s.to_string());
            let description = row.get::<&str, _>(1).map(|s| s.to_string());
            let dep = row.get::<&str, _>(2).map(|s| s.to_string());

            // 获取工作中心负责人（manager角色的用户）
            let manager = if let Some(ref code) = dept_code {
                self.get_workcenter_manager(code).await.unwrap_or(None)
            } else {
                None
            };

            let workcenter = Workcenter {
                dept_code,
                description,
                dep,
                manager,
            };
            Ok(Some(workcenter))
        } else {
            Ok(None)
        }
    }

    /// 构建WHERE条件
    fn build_where_clause(&self, request: &WorkcenterQueryRequest) -> (String, Vec<String>) {
        let mut conditions = Vec::new();
        let mut params = Vec::new();
        let mut param_index = 1;

        if let Some(dept_code) = &request.dept_code {
            conditions.push(format!("DeptCode LIKE @P{}", param_index));
            params.push(format!("%{}%", dept_code));
            param_index += 1;
        }

        if let Some(description) = &request.description {
            conditions.push(format!("Description LIKE @P{}", param_index));
            params.push(format!("%{}%", description));
            param_index += 1;
        }

        if let Some(dep) = &request.dep {
            conditions.push(format!("dep LIKE @P{}", param_index));
            params.push(format!("%{}%", dep));
            param_index += 1;
        }

        if let Some(keyword) = &request.keyword {
            conditions.push(format!(
                "(DeptCode LIKE @P{} OR Description LIKE @P{} OR dep LIKE @P{})",
                param_index, param_index, param_index
            ));
            params.push(format!("%{}%", keyword));
        }

        let where_clause = if conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", conditions.join(" AND "))
        };

        (where_clause, params)
    }

    /// 获取工作中心负责人（manager角色的用户）
    async fn get_workcenter_manager(&self, dept_code: &str) -> AppResult<Option<WorkcenterManager>> {
        let mut client = self.db_config.get_app_connection().await?;
        let source_db = self.db_config.get_source_database_name();

        let sql = format!(
            r#"
            SELECT DISTINCT p.cpsn_num, p.cPsn_Name
            FROM person p
            INNER JOIN user_roles ur ON p.cpsn_num = ur.user_id
            INNER JOIN roles r ON ur.role_id = r.id
            WHERE r.name = 'manager'
            AND p.cDept_num = @P1
            "#
        );

        let mut query = Query::new(&sql);
        query.bind(dept_code);

        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        if let Some(row) = rows.first() {
            let manager = WorkcenterManager {
                psn_num: row.get::<&str, _>(0).unwrap_or("").to_string(),
                name: row.get::<&str, _>(1).unwrap_or("").to_string(),
            };
            Ok(Some(manager))
        } else {
            Ok(None)
        }
    }

    /// 绑定查询参数
    fn bind_parameters<'a>(&self, query: &mut Query<'a>, params: &'a [String]) {
        for param in params {
            query.bind(param);
        }
    }
}
