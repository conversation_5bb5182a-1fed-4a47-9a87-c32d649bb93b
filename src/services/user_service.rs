// 用户服务
use crate::config::DatabaseConfig;
use crate::models::role::RoleResponse;
use crate::models::{CreateUserRequest, UpdateUserRequest, User, UserQuery, UserResponse};
use crate::services::role_service::RoleService;
use crate::utils::{AppError, AppResult, PaginatedResponse};
use chrono::Utc;
use tiberius::Query;

pub struct UserService {
    db_config: DatabaseConfig,
}

impl UserService {
    pub fn new(db_config: DatabaseConfig) -> Self {
        Self { db_config }
    }

    /// 从数据库行中获取username，如果为空则使用cpsn_num作为默认值
    fn get_username_from_row(row: &tiberius::Row) -> String {
        let username_from_db = row.get::<&str, _>(5).map(|s| s.to_string());
        let cpsn_num_fallback = row.get::<&str, _>(0).unwrap_or("").to_string();
        username_from_db.unwrap_or(cpsn_num_fallback)
    }

    /// 标准化性别字段值
    /// 1 = 男性, 2 = 女性
    fn normalize_gender(sex_value: Option<&str>) -> Option<String> {
        match sex_value {
            Some(value) => {
                match value.to_lowercase().trim() {
                    "1" | "男" | "male" | "m" => Some("1".to_string()),
                    "2" | "女" | "female" | "f" => Some("2".to_string()),
                    _ => Some("1".to_string()), // 默认为男性
                }
            }
            None => Some("1".to_string()), // 默认为男性
        }
    }

    // 获取用户列表 - 带角色过滤
    pub async fn get_users_with_role_filter(
        &self,
        query: &UserQuery,
        current_user_role: Option<&str>,
    ) -> AppResult<PaginatedResponse<UserResponse>> {
        let mut client = self.db_config.get_app_connection().await?;

        let page = query.page.unwrap_or(1);
        let page_size = query.page_size.unwrap_or(10);
        let offset = (page - 1) * page_size;

        // 构建查询条件
        let mut where_conditions = Vec::new();
        let mut params = Vec::new();
        let mut param_index = 1;

        if let Some(username) = &query.username {
            where_conditions.push(format!("u.username LIKE @P{}", param_index));
            params.push(format!("%{}%", username));
            param_index += 1;
        }

        if let Some(cpsn_num) = &query.cpsn_num {
            where_conditions.push(format!("u.cpsn_num = @P{}", param_index));
            params.push(cpsn_num.clone());
            param_index += 1;
        }

        if let Some(name) = &query.name {
            where_conditions.push(format!("u.cPsn_Name LIKE @P{}", param_index));
            params.push(format!("%{}%", name));
            param_index += 1;
        }

        if let Some(status) = query.status {
            where_conditions.push(format!("u.status = @P{}", param_index));
            params.push(status.to_string());
            param_index += 1;
        }

        if let Some(dept_num) = &query.dept_num {
            where_conditions.push(format!(
                "u.cDept_num COLLATE SQL_Latin1_General_CP1_CI_AS = @P{}",
                param_index
            ));
            params.push(dept_num.clone());
            param_index += 1;
        }

        if let Some(team_id) = &query.team_id {
            where_conditions.push(format!("tm.TeamID = @P{}", param_index));
            params.push(team_id.to_string());
            param_index += 1;
        }

        if let Some(team_name) = &query.team_name {
            where_conditions.push(format!("t.TeamName LIKE @P{}", param_index));
            params.push(format!("%{}%", team_name));
            param_index += 1;
        }

        // 根据当前用户角色添加访问控制过滤
        if let Some(role) = current_user_role {
            match role {
                "admin" => {
                    // admin可以看到所有用户，不添加额外过滤
                }
                "manager" => {
                    // manager只能看到manager、team_leader、user角色的用户
                    where_conditions.push(format!(
                        "u.cpsn_num IN (SELECT DISTINCT u2.cpsn_num FROM person u2 LEFT JOIN user_roles ur2 ON u2.cpsn_num = ur2.user_id LEFT JOIN roles r2 ON ur2.role_id = r2.id WHERE r2.name IN ('manager', 'team_leader', 'user') OR r2.name IS NULL)"
                    ));
                }
                "team_leader" => {
                    // team_leader只能看到user角色的用户
                    where_conditions.push(format!(
                        "u.cpsn_num IN (SELECT DISTINCT u2.cpsn_num FROM person u2 LEFT JOIN user_roles ur2 ON u2.cpsn_num = ur2.user_id LEFT JOIN roles r2 ON ur2.role_id = r2.id WHERE r2.name = 'user' OR r2.name IS NULL)"
                    ));
                }
                "user" => {
                    // user无法访问用户列表，返回空结果
                    return Ok(PaginatedResponse::new(Vec::new(), 0, page, page_size));
                }
                _ => {
                    // 未知角色，拒绝访问
                    return Err(AppError::Permission("无权限访问用户列表".to_string()));
                }
            }
        }

        // 查询用户表并关联工作中心表和班组表
        let source_db = &self.db_config.get_source_database_name();
        let base_from_clause = format!(
            "person u
            LEFT JOIN {}.dbo.workcenter w ON u.cDept_num COLLATE SQL_Latin1_General_CP1_CI_AS = w.DeptCode COLLATE SQL_Latin1_General_CP1_CI_AS
            LEFT JOIN TeamMembers tm ON u.cpsn_num = tm.Member_psn_num AND tm.IsActive = 1
            LEFT JOIN Teams t ON tm.TeamID = t.TeamID",
            source_db
        );
        let base_select_clause = "u.cpsn_num, CONVERT(NVARCHAR(100), u.cPsn_Name) as cPsn_Name, u.rEmployState, u.rSex, u.cDept_num, u.username, u.password_hash, u.status, u.is_admin, u.last_login_at, u.created_at, u.updated_at, CONVERT(NVARCHAR(100), w.Description) as Description, tm.TeamID, t.TeamName";

        let where_clause = if where_conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_conditions.join(" AND "))
        };

        // 查询总数
        let count_sql = format!(
            "SELECT COUNT(*) as total FROM {} {}",
            base_from_clause, where_clause
        );

        let mut count_query = Query::new(&count_sql);
        for param in &params {
            count_query.bind(param);
        }

        let count_stream = count_query.query(&mut *client).await?;
        let count_rows: Vec<_> = count_stream.into_first_result().await?;
        let total = count_rows
            .first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0) as i64;

        // 查询数据
        let data_sql = format!(
            "SELECT {}
                FROM {}
                {}
                ORDER BY u.created_at DESC
                OFFSET {} ROWS FETCH NEXT {} ROWS ONLY",
            base_select_clause, base_from_clause, where_clause, offset, page_size
        );

        let mut data_query = Query::new(&data_sql);
        for param in &params {
            data_query.bind(param);
        }

        let data_stream = data_query.query(&mut *client).await?;
        let data_rows: Vec<_> = data_stream.into_first_result().await?;

        // 创建用户响应列表，并为每个用户查询角色信息
        let mut users: Vec<UserResponse> = Vec::new();
        let role_service = crate::services::RoleService::new(self.db_config.clone());

        for row in data_rows.iter() {
            let user = User {
                cpsn_num: row.get::<&str, _>(0).unwrap_or("").to_string(),
                name: row.get::<&str, _>(1).unwrap_or("").to_string(),
                employ_state: row.get::<&str, _>(2).unwrap_or("").to_string(),
                sex: Self::normalize_gender(row.get::<&str, _>(3)),
                dept_num: row.get::<&str, _>(4).map(|s| s.to_string()),
                username: Self::get_username_from_row(&row),
                password_hash: None, // 不返回密码哈希
                status: row.get::<i32, _>(7).unwrap_or(1),
                is_admin: row.get::<bool, _>(8).unwrap_or(false),
                last_login_at: row.get::<chrono::DateTime<Utc>, _>(9),
                created_at: row.get::<chrono::DateTime<Utc>, _>(10),
                updated_at: row.get::<chrono::DateTime<Utc>, _>(11),
            };

            let mut user_response = UserResponse::from(user);
            // 设置工作中心名称
            user_response.work_center_name = row.get::<&str, _>(12).map(|s| s.to_string());
            // 设置班组信息
            user_response.team_id = row.get::<i32, _>(13);
            user_response.team_name = row.get::<&str, _>(14).map(|s| s.to_string());

            // 为每个用户查询角色信息
            match role_service
                .get_user_roles(user_response.cpsn_num.clone())
                .await
            {
                Ok(user_roles) => {
                    if user_roles.is_empty() {
                        // 如果用户没有角色，自动分配默认User角色
                        if let Ok(default_role_id) = self.get_default_user_role_id().await {
                            if let Ok(_) = role_service
                                .assign_role_to_user(&user_response.cpsn_num, default_role_id)
                                .await
                            {
                                // 重新获取用户角色
                                if let Ok(updated_roles) = role_service
                                    .get_user_roles(user_response.cpsn_num.clone())
                                    .await
                                {
                                    let role_responses: Vec<crate::models::role::RoleResponse> =
                                        updated_roles
                                            .into_iter()
                                            .map(crate::models::role::RoleResponse::from)
                                            .collect();
                                    user_response.roles = Some(role_responses);
                                } else {
                                    // 如果重新获取失败，创建默认User角色响应
                                    user_response.roles =
                                        Some(vec![self.create_default_user_role_response()]);
                                }
                            } else {
                                // 如果分配失败，创建默认User角色响应
                                user_response.roles =
                                    Some(vec![self.create_default_user_role_response()]);
                            }
                        } else {
                            // 如果获取默认角色ID失败，创建默认User角色响应
                            user_response.roles =
                                Some(vec![self.create_default_user_role_response()]);
                        }
                    } else {
                        let role_responses: Vec<crate::models::role::RoleResponse> = user_roles
                            .into_iter()
                            .map(crate::models::role::RoleResponse::from)
                            .collect();
                        user_response.roles = Some(role_responses);
                    }
                }
                Err(_) => {
                    // 如果查询失败，返回默认User角色
                    user_response.roles = Some(vec![self.create_default_user_role_response()]);
                }
            }

            users.push(user_response);
        }

        Ok(PaginatedResponse::new(users, total, page, page_size))
    }

    // 获取默认User角色的ID（从数据库查询）
    async fn get_default_user_role_id(&self) -> AppResult<i64> {
        let mut client = self.db_config.get_app_connection().await?;

        let mut query = Query::new("SELECT id FROM roles WHERE name = @P1");
        query.bind("user");
        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        if let Some(row) = rows.first() {
            Ok(row.get::<i64, _>(0).unwrap_or(0))
        } else {
            Err(AppError::Database("默认User角色不存在".to_string()))
        }
    }

    // 创建默认User角色响应
    fn create_default_user_role_response(&self) -> crate::models::role::RoleResponse {
        use chrono::Utc;

        crate::models::role::RoleResponse {
            id: 4, // 假设user角色的ID是4
            name: "user".to_string(),
            description: Some("普通用户".to_string()),
            permissions: Some(serde_json::json!({"level": 4, "name": "user"})),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }

    // 创建新用户
    pub async fn create_user(&self, request: &CreateUserRequest) -> AppResult<UserResponse> {
        let mut client = self.db_config.get_app_connection().await?;

        // 检查人员编号是否已存在
        let check_query = Query::new("SELECT COUNT(*) FROM person WHERE cpsn_num = @P1");
        let mut check_query = check_query;
        check_query.bind(&request.cpsn_num);
        let check_stream = check_query.query(&mut *client).await?;
        let check_rows: Vec<_> = check_stream.into_first_result().await?;
        let exists = check_rows
            .first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0)
            > 0;

        if exists {
            return Err(AppError::Validation("人员编号已存在".to_string()));
        }

        // 检查用户名是否已存在（如果提供）
        if !request.username.is_empty() {
            let check_username_query =
                Query::new("SELECT COUNT(*) FROM person WHERE username = @P1");
            let mut check_username_query = check_username_query;
            check_username_query.bind(&request.username);
            let check_username_stream = check_username_query.query(&mut *client).await?;
            let check_username_rows: Vec<_> = check_username_stream.into_first_result().await?;
            let username_exists = check_username_rows
                .first()
                .and_then(|row| row.get::<i32, _>(0))
                .unwrap_or(0)
                > 0;

            if username_exists {
                return Err(AppError::Validation("用户名已存在".to_string()));
            }
        }

        // 生成密码哈希
        let password_hash = crate::utils::hash_password(&request.password)?;

        // 插入新用户（已移除废弃字段isfzr和isbanzhang）
        let insert_query = Query::new(
            "INSERT INTO person (cpsn_num, cPsn_Name, rEmployState, rSex, cDept_num, username, password_hash, status, is_admin, created_at, updated_at)
             VALUES (@P1, @P2, @P3, @P4, @P5, @P6, @P7, 1, @P8, GETDATE(), GETDATE())"
        );
        let mut insert_query = insert_query;
        insert_query.bind(&request.cpsn_num);
        insert_query.bind(&request.name);
        insert_query.bind(request.employ_state.as_deref().unwrap_or("1"));
        // 标准化性别字段
        let normalized_sex =
            Self::normalize_gender(request.sex.as_deref()).unwrap_or("1".to_string());
        insert_query.bind(&normalized_sex);
        insert_query.bind(request.dept_num.as_deref().unwrap_or(""));
        insert_query.bind(&request.username);
        insert_query.bind(&password_hash);
        insert_query.bind(request.is_admin.unwrap_or(false));

        insert_query.execute(&mut *client).await?;

        tracing::info!(
            "创建用户成功: {} (人员编号: {})",
            request.username,
            request.cpsn_num
        );

        // 返回创建的用户信息
        self.get_user_by_id(&request.cpsn_num).await
    }

    // 根据人员编号获取用户
    pub async fn get_user_by_id(&self, cpsn_num: &str) -> AppResult<UserResponse> {
        let mut client = self.db_config.get_app_connection().await?;

        let source_db = &self.db_config.get_source_database_name();
        let sql = format!(
            "SELECT u.cpsn_num, CONVERT(NVARCHAR(100), u.cPsn_Name) as cPsn_Name, u.rEmployState, u.rSex, u.cDept_num, u.username, u.password_hash, u.status, u.is_admin, u.last_login_at, u.created_at, u.updated_at, CONVERT(NVARCHAR(100), w.Description) as Description, tm.TeamID, t.TeamName
             FROM person u
             LEFT JOIN {}.dbo.workcenter w ON u.cDept_num COLLATE SQL_Latin1_General_CP1_CI_AS = w.DeptCode COLLATE SQL_Latin1_General_CP1_CI_AS
             LEFT JOIN TeamMembers tm ON u.cpsn_num = tm.Member_psn_num AND tm.IsActive = 1
             LEFT JOIN Teams t ON tm.TeamID = t.TeamID
             WHERE u.cpsn_num = @P1", source_db
        );
        let mut query = Query::new(&sql);
        query.bind(cpsn_num);

        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        if let Some(row) = rows.first() {
            let user = User {
                cpsn_num: row.get::<&str, _>(0).unwrap_or("").to_string(),
                name: row.get::<&str, _>(1).unwrap_or("").to_string(),
                employ_state: row.get::<&str, _>(2).unwrap_or("").to_string(),
                sex: Self::normalize_gender(row.get::<&str, _>(3)),
                dept_num: row.get::<&str, _>(4).map(|s| s.to_string()),
                username: Self::get_username_from_row(&row),
                password_hash: None, // 不返回密码哈希
                status: row.get::<i32, _>(7).unwrap_or(1),
                is_admin: row.get::<bool, _>(8).unwrap_or(false),
                last_login_at: row.get::<chrono::DateTime<Utc>, _>(9),
                created_at: row.get::<chrono::DateTime<Utc>, _>(10),
                updated_at: row.get::<chrono::DateTime<Utc>, _>(11),
            };

            // 获取用户的角色信息
            let role_service = RoleService::new(self.db_config.clone());
            let user_roles = role_service
                .get_user_roles(cpsn_num.to_string())
                .await
                .unwrap_or_else(|_| Vec::new());

            let mut user_response = UserResponse::from(user);
            // 设置工作中心名称
            user_response.work_center_name = row.get::<&str, _>(12).map(|s| s.to_string());
            // 设置班组信息
            user_response.team_id = row.get::<i32, _>(13);
            user_response.team_name = row.get::<&str, _>(14).map(|s| s.to_string());

            if user_roles.is_empty() {
                // 如果用户没有角色，自动分配默认User角色
                if let Ok(default_role_id) = self.get_default_user_role_id().await {
                    if let Ok(_) = role_service
                        .assign_role_to_user(cpsn_num, default_role_id)
                        .await
                    {
                        // 重新获取用户角色
                        if let Ok(updated_roles) =
                            role_service.get_user_roles(cpsn_num.to_string()).await
                        {
                            let role_responses: Vec<RoleResponse> =
                                updated_roles.into_iter().map(RoleResponse::from).collect();
                            user_response.roles = Some(role_responses);
                        } else {
                            // 如果重新获取失败，创建默认User角色响应
                            user_response.roles =
                                Some(vec![self.create_default_user_role_response()]);
                        }
                    } else {
                        // 如果分配失败，创建默认User角色响应
                        user_response.roles = Some(vec![self.create_default_user_role_response()]);
                    }
                } else {
                    // 如果获取默认角色ID失败，创建默认User角色响应
                    user_response.roles = Some(vec![self.create_default_user_role_response()]);
                }
            } else {
                let role_responses: Vec<RoleResponse> =
                    user_roles.into_iter().map(RoleResponse::from).collect();
                user_response.roles = Some(role_responses);
            }

            Ok(user_response)
        } else {
            Err(AppError::Business("用户不存在".to_string()))
        }
    }

    // 更新用户信息
    pub async fn update_user(
        &self,
        cpsn_num: &str,
        request: &UpdateUserRequest,
    ) -> AppResult<UserResponse> {
        let mut client = self.db_config.get_app_connection().await?;

        let mut update_fields = Vec::new();
        let mut params = Vec::new();
        let mut param_index = 1;

        // 优化：只有非空且有意义的字段才进行更新
        if let Some(name) = &request.name {
            if !name.trim().is_empty() {
                update_fields.push(format!("cPsn_Name = @P{}", param_index));
                params.push(name.trim().to_string());
                param_index += 1;
            }
        }

        if let Some(username) = &request.username {
            if !username.trim().is_empty() {
                update_fields.push(format!("username = @P{}", param_index));
                params.push(username.trim().to_string());
                param_index += 1;
            }
        }

        // 密码更新处理
        if let Some(password) = &request.password {
            if !password.trim().is_empty() {
                // 生成密码哈希
                let password_hash = crate::utils::hash_password(password.trim())?;
                update_fields.push(format!("password_hash = @P{}", param_index));
                params.push(password_hash);
                param_index += 1;
                tracing::info!("用户 {} 密码已更新", cpsn_num);
            }
        }

        if let Some(employ_state) = &request.employ_state {
            if !employ_state.trim().is_empty() {
                update_fields.push(format!("rEmployState = @P{}", param_index));
                params.push(employ_state.trim().to_string());
                param_index += 1;
            }
        }

        if let Some(sex) = &request.sex {
            if !sex.trim().is_empty() {
                // 标准化性别字段
                let normalized_sex = Self::normalize_gender(Some(sex)).unwrap_or("1".to_string());
                update_fields.push(format!("rSex = @P{}", param_index));
                params.push(normalized_sex);
                param_index += 1;
            }
        }

        if let Some(dept_num) = &request.dept_num {
            if !dept_num.trim().is_empty() {
                update_fields.push(format!("cDept_num = @P{}", param_index));
                params.push(dept_num.trim().to_string());
                param_index += 1;
            }
        }

        if update_fields.is_empty() {
            return Err(AppError::Validation("没有要更新的字段".to_string()));
        }

        update_fields.push("updated_at = GETDATE()".to_string());

        let update_sql = format!(
            "UPDATE person SET {} WHERE cpsn_num = @P{}",
            update_fields.join(", "),
            param_index
        );

        let mut update_query = Query::new(&update_sql);
        for param in &params {
            update_query.bind(param);
        }
        update_query.bind(cpsn_num);

        update_query.execute(&mut *client).await?;

        // 返回更新后的用户信息
        self.get_user_by_id(cpsn_num).await
    }

    // 删除用户（级联删除角色关联）
    pub async fn delete_user(&self, user_id: &str, current_user_id: &str) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        // 安全检查1：用户不能删除自己
        if user_id == current_user_id {
            return Err(AppError::Business("不能删除自己的账户".to_string()));
        }

        // 安全检查2：admin账户不可删除
        if user_id == "admin" {
            return Err(AppError::Business("admin账户不可删除".to_string()));
        }

        // 首先检查用户是否存在
        let check_query = Query::new("SELECT COUNT(*) FROM person WHERE cpsn_num = @P1");
        let mut check_query = check_query;
        check_query.bind(user_id);
        let check_stream = check_query.query(&mut *client).await?;
        let check_rows: Vec<_> = check_stream.into_first_result().await?;
        let exists = check_rows
            .first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0)
            > 0;

        if !exists {
            return Err(AppError::Business("用户不存在".to_string()));
        }

        // 安全检查3：检查目标用户和当前用户的管理员状态
        let admin_check_query = Query::new(
            "SELECT
                (SELECT is_admin FROM person WHERE cpsn_num = @P1) as target_is_admin,
                (SELECT is_admin FROM person WHERE cpsn_num = @P2) as current_is_admin",
        );
        let mut admin_check_query = admin_check_query;
        admin_check_query.bind(user_id);
        admin_check_query.bind(current_user_id);
        let admin_stream = admin_check_query.query(&mut *client).await?;
        let admin_rows: Vec<_> = admin_stream.into_first_result().await?;

        if let Some(row) = admin_rows.first() {
            let target_is_admin: bool = row.get(0).unwrap_or(false);
            let current_is_admin: bool = row.get(1).unwrap_or(false);

            // 如果目标用户是管理员，只有超级管理员才能删除
            if target_is_admin && !current_is_admin {
                return Err(AppError::Business(
                    "只有管理员才能删除管理员账户".to_string(),
                ));
            }
        }

        // 开始事务：先删除用户角色关联，再删除用户
        // 1. 删除用户角色关联 (直接使用cpsn_num，因为user_roles.user_id是nvarchar类型)
        let delete_roles_query = Query::new("DELETE FROM user_roles WHERE user_id = @P1");
        let mut delete_roles_query = delete_roles_query;
        delete_roles_query.bind(user_id);
        let roles_result = delete_roles_query.execute(&mut *client).await?;

        let deleted_roles_count = if roles_result.rows_affected().len() > 0 {
            roles_result.rows_affected()[0]
        } else {
            0
        };

        // 2. 删除用户
        let delete_user_query = Query::new("DELETE FROM person WHERE cpsn_num = @P1");
        let mut delete_user_query = delete_user_query;
        delete_user_query.bind(user_id);
        let user_result = delete_user_query.execute(&mut *client).await?;

        if user_result.rows_affected().len() == 0 || user_result.rows_affected()[0] == 0 {
            return Err(AppError::Database("删除用户失败".to_string()));
        }

        tracing::info!(
            "删除用户成功: ID {} (同时删除了 {} 个角色关联)",
            user_id,
            deleted_roles_count
        );
        Ok(())
    }

    /// 获取没有角色关联的用户列表（不在班组成员表内，也不在role_user表内的用户）
    pub async fn get_unassigned_users(
        &self,
        query: &UserQuery,
    ) -> AppResult<PaginatedResponse<UserResponse>> {
        let mut client = self.db_config.get_app_connection().await?;

        let page = query.page.unwrap_or(1);
        let page_size = query.page_size.unwrap_or(10);
        let offset = (page - 1) * page_size;

        // 构建查询条件（复用现有的查询条件逻辑，但排除班组相关条件）
        let mut where_conditions = Vec::new();
        let mut params = Vec::new();
        let mut param_index = 1;

        if let Some(username) = &query.username {
            where_conditions.push(format!("u.username LIKE @P{}", param_index));
            params.push(format!("%{}%", username));
            param_index += 1;
        }

        if let Some(cpsn_num) = &query.cpsn_num {
            where_conditions.push(format!("u.cpsn_num LIKE @P{}", param_index));
            params.push(format!("%{}%", cpsn_num));
            param_index += 1;
        }

        if let Some(name) = &query.name {
            where_conditions.push(format!("u.cPsn_Name LIKE @P{}", param_index));
            params.push(format!("%{}%", name));
            param_index += 1;
        }

        if let Some(status) = &query.status {
            where_conditions.push(format!("u.status = @P{}", param_index));
            params.push(status.to_string());
            param_index += 1;
        }

        if let Some(dept_num) = &query.dept_num {
            where_conditions.push(format!(
                "u.cDept_num COLLATE SQL_Latin1_General_CP1_CI_AS = @P{}",
                param_index
            ));
            params.push(dept_num.clone());
            param_index += 1;
        }

        // 添加keyword模糊查询：支持cpsn_num和name字段
        if let Some(keyword) = &query.keyword {
            if !keyword.trim().is_empty() {
                where_conditions.push(format!(
                    "(u.cpsn_num LIKE @P{} OR u.cPsn_Name LIKE @P{})",
                    param_index, param_index
                ));
                params.push(format!("%{}%", keyword.trim()));
                param_index += 1;
            }
        }

        // 核心条件：排除有实际角色（manager、team_leader）和班组绑定的用户
        // 1. 排除有manager或team_leader角色的用户
        where_conditions.push("NOT EXISTS (SELECT 1 FROM user_roles ur INNER JOIN roles r ON ur.role_id = r.id WHERE ur.user_id = u.cpsn_num AND r.name IN ('manager', 'team_leader'))".to_string());
        // 2. 排除在班组成员表中的用户
        where_conditions.push("NOT EXISTS (SELECT 1 FROM TeamMembers tm WHERE tm.Member_psn_num = u.cpsn_num AND tm.IsActive = 1)".to_string());

        let where_clause = if where_conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_conditions.join(" AND "))
        };

        // 查询总数
        let count_sql = format!("SELECT COUNT(*) FROM person u {}", where_clause);
        let mut count_query = Query::new(&count_sql);
        for param in &params {
            count_query.bind(param);
        }

        let count_stream = count_query.query(&mut *client).await?;
        let count_rows: Vec<_> = count_stream.into_first_result().await?;
        let total = count_rows
            .first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0);

        // 查询数据
        let source_db = &self.db_config.get_source_database_name();
        let data_sql = format!(
            "SELECT u.cpsn_num, CONVERT(NVARCHAR(100), u.cPsn_Name) as cPsn_Name, u.rEmployState, u.rSex, u.cDept_num, u.username, u.password_hash, u.status, u.is_admin, u.last_login_at, u.created_at, u.updated_at, CONVERT(NVARCHAR(100), w.Description) as Description
             FROM person u
             LEFT JOIN {}.dbo.workcenter w ON u.cDept_num COLLATE SQL_Latin1_General_CP1_CI_AS = w.DeptCode COLLATE SQL_Latin1_General_CP1_CI_AS
             {}
             ORDER BY u.created_at DESC
             OFFSET {} ROWS FETCH NEXT {} ROWS ONLY",
            source_db, where_clause, offset, page_size
        );

        let mut data_query = Query::new(&data_sql);
        for param in &params {
            data_query.bind(param);
        }

        let data_stream = data_query.query(&mut *client).await?;
        let data_rows: Vec<_> = data_stream.into_first_result().await?;

        // 创建用户响应列表
        let mut users: Vec<UserResponse> = Vec::new();

        for row in data_rows.iter() {
            let user = User {
                cpsn_num: row.get::<&str, _>(0).unwrap_or("").to_string(),
                name: row.get::<&str, _>(1).unwrap_or("").to_string(),
                employ_state: row.get::<&str, _>(2).unwrap_or("").to_string(),
                sex: Self::normalize_gender(row.get::<&str, _>(3)),
                dept_num: row.get::<&str, _>(4).map(|s| s.to_string()),
                username: Self::get_username_from_row(&row),
                password_hash: None, // 不返回密码哈希
                status: row.get::<i32, _>(7).unwrap_or(1),
                is_admin: row.get::<bool, _>(8).unwrap_or(false),
                last_login_at: row.get::<chrono::DateTime<Utc>, _>(9),
                created_at: row.get::<chrono::DateTime<Utc>, _>(10),
                updated_at: row.get::<chrono::DateTime<Utc>, _>(11),
            };

            let mut user_response = UserResponse::from(user);
            // 设置工作中心名称
            user_response.work_center_name = row.get::<&str, _>(12).map(|s| s.to_string());
            // 这些用户没有班组信息，保持为None
            user_response.team_id = None;
            user_response.team_name = None;
            // 这些用户没有角色，保持为None
            user_response.roles = None;

            users.push(user_response);
        }

        Ok(PaginatedResponse::new(users, total as i64, page, page_size))
    }
}
