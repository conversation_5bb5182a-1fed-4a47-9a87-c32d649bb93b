// 优化后的权限服务 - 高性能、线程安全、可测试
use std::sync::Arc;
use tokio::sync::RwLock;
use std::collections::HashMap;
use chrono::{DateTime, Utc, Duration};
// serde::Serialize 已废弃，不再需要序列化缓存统计
use crate::config::DatabaseConfig;
use crate::utils::{AppResult, AppError};
use crate::models::permission::{
    PermissionDefinitionResponse, PermissionResource, PermissionAction
    // CheckPermissionRequest, CheckPermissionResponse, ValidatePermissionsRequest 已废弃
};

// 权限缓存项
#[derive(Debug, Clone)]
struct PermissionCacheItem {
    data: PermissionDefinitionResponse,
    #[allow(dead_code)]
    cached_at: DateTime<Utc>,
    expires_at: DateTime<Utc>,
}

impl PermissionCacheItem {
    fn new(data: PermissionDefinitionResponse, ttl: Duration) -> Self {
        let now = Utc::now();
        Self {
            data,
            cached_at: now,
            expires_at: now + ttl,
        }
    }

    fn is_expired(&self) -> bool {
        Utc::now() > self.expires_at
    }
}

// 用户权限缓存
#[derive(Debug, Clone)]
struct UserPermissionCache {
    permissions: HashMap<String, bool>, // "resource:action" -> allowed
    #[allow(dead_code)]
    cached_at: DateTime<Utc>,
    expires_at: DateTime<Utc>,
}

impl UserPermissionCache {
    fn new(ttl: Duration) -> Self {
        let now = Utc::now();
        Self {
            permissions: HashMap::new(),
            cached_at: now,
            expires_at: now + ttl,
        }
    }

    fn is_expired(&self) -> bool {
        Utc::now() > self.expires_at
    }

    fn get_permission(&self, resource: &str, action: &str) -> Option<bool> {
        let key = format!("{}:{}", resource, action);
        self.permissions.get(&key).copied()
    }

    fn set_permission(&mut self, resource: &str, action: &str, allowed: bool) {
        let key = format!("{}:{}", resource, action);
        self.permissions.insert(key, allowed);
    }
}

// 权限服务配置
#[derive(Debug, Clone)]
pub struct PermissionServiceConfig {
    pub cache_ttl: Duration,
    pub user_permission_cache_ttl: Duration,
    pub max_cache_size: usize,
}

impl Default for PermissionServiceConfig {
    fn default() -> Self {
        Self {
            cache_ttl: Duration::minutes(30),
            user_permission_cache_ttl: Duration::minutes(5),
            max_cache_size: 1000,
        }
    }
}

// 权限服务特征已废弃，直接使用 OptimizedPermissionService 的方法

// 优化后的权限服务
pub struct OptimizedPermissionService {
    db_config: DatabaseConfig,
    config: PermissionServiceConfig,
    // 权限定义缓存
    permission_cache: Arc<RwLock<Option<PermissionCacheItem>>>,
    // 用户权限缓存
    user_permission_cache: Arc<RwLock<HashMap<String, UserPermissionCache>>>,
}

impl OptimizedPermissionService {
    pub fn new(db_config: DatabaseConfig) -> Self {
        Self::with_config(db_config, PermissionServiceConfig::default())
    }

    pub fn with_config(db_config: DatabaseConfig, config: PermissionServiceConfig) -> Self {
        Self {
            db_config,
            config,
            permission_cache: Arc::new(RwLock::new(None)),
            user_permission_cache: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    // 获取权限定义（带缓存）
    async fn get_permission_definitions_cached(&self) -> AppResult<PermissionDefinitionResponse> {
        // 检查缓存
        {
            let cache = self.permission_cache.read().await;
            if let Some(ref item) = *cache {
                if !item.is_expired() {
                    return Ok(item.data.clone());
                }
            }
        }

        // 缓存未命中或已过期，从数据库加载
        let definitions = self.load_permission_definitions_from_db().await?;
        
        // 更新缓存
        {
            let mut cache = self.permission_cache.write().await;
            *cache = Some(PermissionCacheItem::new(definitions.clone(), self.config.cache_ttl));
        }

        Ok(definitions)
    }

    // 从数据库加载权限定义 - 由于权限表结构不完整，直接使用基础权限
    async fn load_permission_definitions_from_db(&self) -> AppResult<PermissionDefinitionResponse> {
        // 由于权限表结构不完整，直接使用基础权限定义
        tracing::warn!("权限定义表结构不完整，使用基础权限定义");
        self.get_fallback_permissions().await
    }

    // 获取基础权限（当数据库表不存在时的后备方案）
    async fn get_fallback_permissions(&self) -> AppResult<PermissionDefinitionResponse> {
        let mut resources = HashMap::new();
        let categories = vec!["系统管理".to_string()];
        let templates = HashMap::new();

        // 只包含系统实际存在的功能
        let core_resources = vec![
            ("users", "用户管理", "管理系统用户账户"),
            ("roles", "角色管理", "管理系统角色和权限"),
            ("auth", "认证管理", "用户认证和会话管理"),
            ("teams", "班组管理", "管理班组和成员"),
            ("instruction_cards", "指令卡管理", "管理生产指令卡"),
            ("flexible_entry", "零活录入", "管理零活录入和审核"),
            ("team_bindings", "班组绑定", "管理班组产品设备绑定"),
            ("borrowing", "人员借调", "管理人员借调申请"),
            ("permissions", "权限管理", "管理系统权限配置"),
        ];

        for (resource_name, resource_label, description) in core_resources {
            let actions = vec![
                PermissionAction {
                    name: "read".to_string(),
                    label: "查看".to_string(),
                    description: format!("查看{}", resource_label),
                },
                PermissionAction {
                    name: "create".to_string(),
                    label: "创建".to_string(),
                    description: format!("创建{}", resource_label),
                },
                PermissionAction {
                    name: "update".to_string(),
                    label: "更新".to_string(),
                    description: format!("更新{}", resource_label),
                },
                PermissionAction {
                    name: "delete".to_string(),
                    label: "删除".to_string(),
                    description: format!("删除{}", resource_label),
                },
            ];

            resources.insert(resource_name.to_string(), PermissionResource {
                name: resource_name.to_string(),
                description: description.to_string(),
                icon: "".to_string(),
                category: "系统管理".to_string(),
                actions,
            });
        }

        Ok(PermissionDefinitionResponse {
            resources,
            templates,
            categories,
        })
    }

    // 检查用户权限（带缓存）
    pub async fn check_user_permission_cached(
        &self,
        user_id: &str,
        resource: &str,
        action: &str,
    ) -> AppResult<bool> {
        tracing::info!("权限检查开始 - 用户ID: {}, 资源: {}, 操作: {}", user_id, resource, action);

        // 检查用户权限缓存
        {
            let cache = self.user_permission_cache.read().await;
            if let Some(user_cache) = cache.get(user_id) {
                if !user_cache.is_expired() {
                    if let Some(allowed) = user_cache.get_permission(resource, action) {
                        tracing::info!("权限检查 - 缓存命中，用户ID: {}, 结果: {}", user_id, allowed);
                        return Ok(allowed);
                    }
                }
            }
        }

        tracing::info!("权限检查 - 缓存未命中，执行数据库查询");
        // 缓存未命中，执行实际权限检查
        let allowed = self.perform_permission_check(user_id, resource, action).await?;
        tracing::info!("权限检查 - 数据库查询结果: {}", allowed);

        // 更新用户权限缓存
        {
            let mut cache = self.user_permission_cache.write().await;
            let user_cache = cache
                .entry(user_id.to_string())
                .or_insert_with(|| UserPermissionCache::new(self.config.user_permission_cache_ttl));
            
            user_cache.set_permission(resource, action, allowed);

            // 清理过期缓存
            if cache.len() > self.config.max_cache_size {
                cache.retain(|_, user_cache| !user_cache.is_expired());
            }
        }

        Ok(allowed)
    }

    // 执行实际的权限检查
    async fn perform_permission_check(
        &self,
        user_id: &str,
        resource: &str,
        action: &str,
    ) -> AppResult<bool> {
        // 记录权限检查开始时间（性能监控）
        let start_time = std::time::Instant::now();

        tracing::debug!(
            "开始数据库权限检查: 用户ID={}, 资源={}, 操作={}",
            user_id, resource, action
        );

        // 检查用户是否为管理员（通过数据库查询is_admin字段）
        let is_admin = self.check_user_is_admin_internal(user_id).await?;
        if is_admin {
            let duration = start_time.elapsed();
            tracing::info!(
                "权限检查完成(管理员): 用户ID={}, 资源={}, 操作={}, 允许=true, 耗时={:?}",
                user_id, resource, action, duration
            );
            return Ok(true);
        }

        // 超级权限检查
        if resource == "*" || action == "*" {
            return Ok(true);
        }

        // 获取数据库连接
        let mut client = self.db_config.get_app_connection().await
            .map_err(|e| AppError::Database(format!("获取数据库连接失败: {}", e)))?;

        // 1. 获取用户的角色ID - 直接查询user_roles表，避免person表关联问题
        // 使用SQL处理不同格式的用户ID匹配（支持数字和带前导零的字符串格式）
        // let user_id_str = user_id.to_string();
        // tracing::info!("权限检查 - 查询用户角色，用户ID: {}", user_id_str);
        let mut user_role_query = tiberius::Query::new(
            "SELECT ur.role_id FROM person u INNER JOIN user_roles ur ON u.cpsn_num = ur.user_id WHERE (u.cpsn_num = @P1 OR u.cpsn_num = @P2) AND u.status = 1"
        );
        user_role_query.bind(user_id);
        // 同时尝试6位格式（补前导零）
        // let user_id_padded = format!("{:06}", user_id);
        user_role_query.bind(user_id);
        // tracing::info!("权限检查 - 尝试用户ID格式: {} 和 {}", user_id, user_id_padded);

        let user_stream = user_role_query.query(&mut *client).await
            .map_err(|e| AppError::Database(format!("查询用户角色失败: {}", e)))?;
        let user_rows: Vec<tiberius::Row> = user_stream.into_first_result().await
            .map_err(|e| AppError::Database(format!("获取用户角色结果失败: {}", e)))?;

        tracing::info!("权限检查 - 用户角色查询结果，行数: {}", user_rows.len());

        let role_id = if let Some(row) = user_rows.first() {
            let role_id = row.get::<i64, _>("role_id").unwrap_or(0);
            tracing::info!("权限检查 - 找到用户角色ID: {}", role_id);
            role_id
        } else {
            tracing::info!("权限检查 - 用户不存在或未激活，返回false");
            return Ok(false); // 用户不存在或未激活
        };

        if role_id == 0 {
            return Ok(false); // 用户没有分配角色
        }

        // 2. 检查角色权限配置
        let mut permission_query = tiberius::Query::new(r#"
            SELECT permissions FROM roles WHERE id = @P1
        "#);
        permission_query.bind(role_id);

        let permission_stream = permission_query.query(&mut *client).await
            .map_err(|e| AppError::Database(format!("查询角色权限失败: {}", e)))?;
        let permission_rows: Vec<tiberius::Row> = permission_stream.into_first_result().await
            .map_err(|e| AppError::Database(format!("获取权限检查结果失败: {}", e)))?;

        if let Some(row) = permission_rows.first() {
            let permissions_json = row.get::<&str, _>("permissions").unwrap_or("{}");
            tracing::info!("权限检查 - 用户: {}, 角色ID: {}, 权限JSON: {}", user_id, role_id, permissions_json);

            // 解析权限JSON并检查是否包含指定的资源和操作
            if let Ok(permissions_config) = serde_json::from_str::<serde_json::Value>(permissions_json) {
                tracing::info!("权限检查 - 解析权限配置成功: {:?}", permissions_config);
                if let Some(permissions_array) = permissions_config.get("permissions").and_then(|p| p.as_array()) {
                    tracing::info!("权限检查 - 找到权限数组，长度: {}", permissions_array.len());
                    for permission in permissions_array {
                        if let (Some(perm_resource), Some(perm_actions)) = (
                            permission.get("resource").and_then(|r| r.as_str()),
                            permission.get("actions").and_then(|a| a.as_array())
                        ) {
                            // 检查超级权限
                            if perm_resource == "*" || (perm_actions.len() > 0 && perm_actions[0].as_str() == Some("*")) {
                                let duration = start_time.elapsed();
                                tracing::info!(
                                    "权限检查通过(超级权限): 用户ID={}, 资源={}, 操作={}, 允许=true, 耗时={:?}",
                                    user_id, resource, action, duration
                                );
                                return Ok(true);
                            }

                            // 检查具体权限
                            if perm_resource == resource {
                                for action_val in perm_actions {
                                    if let Some(action_str) = action_val.as_str() {
                                        if action_str == action || action_str == "*" {
                                            let duration = start_time.elapsed();
                                            tracing::info!(
                                                "权限检查通过: 用户ID={}, 资源={}, 操作={}, 允许=true, 耗时={:?}",
                                                user_id, resource, action, duration
                                            );
                                            return Ok(true);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        let duration = start_time.elapsed();
        tracing::warn!(
            "权限检查拒绝: 用户ID={}, 资源={}, 操作={}, 允许=false, 耗时={:?}",
            user_id, resource, action, duration
        );
        Ok(false)
    }

    /// 获取权限定义（公共方法，供外部调用）
    pub async fn get_permission_definitions(&self) -> AppResult<PermissionDefinitionResponse> {
        self.get_permission_definitions_cached().await
    }

    // 以下方法已废弃，缓存管理功能已简化
    // pub async fn cleanup_expired_caches(&self) { ... }
    // pub async fn get_cache_stats(&self) -> CacheStats { ... }
}

// PermissionServiceTrait 实现已废弃，直接使用 OptimizedPermissionService 的方法

// CacheStats 结构体已废弃，缓存统计功能已简化

// #[cfg(test)]
// mod tests {
//     use super::*;
//     use tokio::time::{sleep, Duration as TokioDuration};

//     #[tokio::test]
//     async fn test_permission_cache_expiry() {
//         let config = PermissionServiceConfig {
//             cache_ttl: Duration::milliseconds(100),
//             ..Default::default()
//         };
        
//         let db_config = DatabaseConfig::default();
//         let service = OptimizedPermissionService::with_config(db_config, config);

//         // 第一次调用应该缓存结果
//         let _result1 = service.get_permission_definitions_cached().await.unwrap();
        
//         // 等待缓存过期
//         sleep(TokioDuration::from_millis(150)).await;
        
//         // 第二次调用应该重新加载
//         let _result2 = service.get_permission_definitions_cached().await.unwrap();
//     }

//     #[tokio::test]
//     async fn test_user_permission_cache() {
//         let service = OptimizedPermissionService::new(DatabaseConfig::default());
        
//         // 第一次检查权限
//         let allowed1 = service.check_user_permission_cached(1, "users", "read").await.unwrap();
        
//         // 第二次检查相同权限应该使用缓存
//         let allowed2 = service.check_user_permission_cached(1, "users", "read").await.unwrap();
        
//         assert_eq!(allowed1, allowed2);
//     }

//     #[tokio::test]
//     async fn test_cache_cleanup() {
//         let service = OptimizedPermissionService::new(DatabaseConfig::default());
        
//         // 添加一些缓存项
//         let _ = service.check_user_permission_cached(1, "users", "read").await;
//         let _ = service.check_user_permission_cached(2, "roles", "write").await;
        
//         let stats_before = service.get_cache_stats().await;
//         assert!(stats_before.user_cache_size > 0);
        
//         // 清理缓存
//         service.cleanup_expired_caches().await;
        
//         // 注意：由于缓存还没过期，这里不会清理
//         let stats_after = service.get_cache_stats().await;
//         assert_eq!(stats_before.user_cache_size, stats_after.user_cache_size);
//     }
// }

impl OptimizedPermissionService {
    /// 检查用户是否为管理员（公共方法）
    pub async fn check_user_is_admin(&self, user_id: &str) -> AppResult<bool> {
        self.check_user_is_admin_internal(user_id).await
    }

    /// 检查用户是否为管理员（内部方法）
    ///
    /// 安全策略：
    /// 1. 首先检查数据库中的is_admin字段
    /// 2. 同时验证用户是否拥有admin角色
    /// 3. 两者必须一致才认为是管理员
    async fn check_user_is_admin_internal(&self, user_id: &str) -> AppResult<bool> {
        let mut client = self.db_config.get_app_connection().await?;

        // 查询用户的is_admin字段和角色信息
        let mut query = tiberius::Query::new(
            r#"
            SELECT
                p.is_admin,
                r.level
            FROM person p
            LEFT JOIN user_roles ur ON p.cpsn_num = ur.user_id
            LEFT JOIN roles r ON ur.role_id = r.id
            WHERE p.cpsn_num = @P1
            "#
        );

        let user_id_str = user_id.to_string();
        query.bind(&user_id_str);

        let result = query.query(&mut client).await?;
        let rows: Vec<tiberius::Row> = result.into_first_result().await?;

        if rows.is_empty() {
            return Ok(false);
        }

        // 检查is_admin字段
        let is_admin_flag: bool = rows[0].get(0).unwrap_or(false);

        // 检查是否有admin角色（level = 4）
        let has_admin_role = rows.iter().any(|row| {
            row.get::<i32, _>(1).unwrap_or(0) == 4
        });

        // 安全检查：is_admin字段和admin角色必须一致
        if is_admin_flag && has_admin_role {
            Ok(true)
        } else if !is_admin_flag && !has_admin_role {
            Ok(false)
        } else {
            // 不一致的情况，记录警告并返回false
            tracing::warn!(
                "用户 {} 的is_admin字段({})与admin角色({})不一致，拒绝管理员权限",
                user_id, is_admin_flag, has_admin_role
            );
            Ok(false)
        }
    }
}
