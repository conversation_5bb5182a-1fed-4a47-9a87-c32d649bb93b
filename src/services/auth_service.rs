// 认证服务
use tiberius::Query;
use chrono::{Duration, Utc};
use crate::config::DatabaseConfig;
use crate::models::{User, LoginRequest, LoginResponse, UserResponse};
use crate::utils::{AppResult, AppError, verify_password, create_jwt, verify_jwt, Claims};

pub struct AuthService {
    db_config: DatabaseConfig,
    jwt_secret: String,
    jwt_expires_in: Duration,
    // refresh_expires_in 已废弃，刷新令牌功能暂未完全实现
}

impl AuthService {
    pub fn new(db_config: DatabaseConfig, jwt_secret: String, jwt_expires_in: Duration, _refresh_expires_in: Duration) -> Self {
        Self {
            db_config,
            jwt_secret,
            jwt_expires_in,
        }
    }

    /// 从数据库行中获取username，如果为空则使用cpsn_num作为默认值
    fn get_username_from_row(row: &tiberius::Row) -> String {
        let username_from_db = row.get::<&str, _>(5).map(|s| s.to_string());
        let cpsn_num_fallback = row.get::<&str, _>(0).unwrap_or("").to_string();
        username_from_db.unwrap_or(cpsn_num_fallback)
    }

    /// 登录时查找用户 - 支持多种登录方式
    /// 1. 优先通过username查找
    /// 2. 如果失败，尝试通过cpsn_num查找
    /// 3. 处理空字符串和特殊情况
    async fn find_user_for_login(&self, login_identifier: &str) -> AppResult<User> {
        // 处理空字符串或空白字符串
        let identifier = login_identifier.trim();
        if identifier.is_empty() {
            return Err(AppError::Authentication("用户名不能为空".to_string()));
        }

        // 方式1: 尝试通过username查找
        match self.find_user_by_username(identifier).await {
            Ok(user) => return Ok(user),
            Err(_) => {
                // username查找失败，继续尝试其他方式
                tracing::debug!("通过username查找失败，尝试cpsn_num: {}", identifier);
            }
        }

        // 方式2: 尝试通过cpsn_num查找
        match self.find_user_by_id(identifier).await {
            Ok(user) => return Ok(user),
            Err(_) => {
                tracing::debug!("通过cpsn_num查找也失败: {}", identifier);
            }
        }

        // 所有方式都失败
        Err(AppError::Authentication("用户不存在".to_string()))
    }

    // 用户登录
    pub async fn login(&self, request: &LoginRequest) -> AppResult<LoginResponse> {
        // 查询用户 - 支持多种登录方式
        let user = self.find_user_for_login(&request.username).await?;

        // 验证密码
        match &user.password_hash {
            Some(password_hash) if !password_hash.is_empty() => {
                // 用户有密码哈希，进行正常验证
                if !verify_password(&request.password, password_hash)? {
                    return Err(AppError::Authentication("用户名或密码错误".to_string()));
                }
            }
            _ => {
                // 用户密码为空或null，需要设置密码
                if request.password.is_empty() {
                    return Err(AppError::Authentication("用户密码未设置，请联系管理员设置密码".to_string()));
                }

                // 为用户设置新密码
                tracing::info!("为用户 {} 设置新密码", user.cpsn_num);
                let new_password_hash = crate::utils::hash_password(&request.password)?;

                // 更新数据库中的密码
                let mut client = self.db_config.get_app_connection().await?;
                let mut update_query = tiberius::Query::new(
                    "UPDATE person SET password_hash = @P1, updated_at = GETDATE() WHERE cpsn_num = @P2"
                );
                update_query.bind(&new_password_hash);
                update_query.bind(&user.cpsn_num);

                update_query.execute(&mut *client).await.map_err(|e| {
                    tracing::error!("更新用户密码失败: {}", e);
                    AppError::Database(format!("更新密码失败: {}", e))
                })?;

                tracing::info!("用户 {} 密码设置成功", user.cpsn_num);
            }
        }

        // 检查用户状态
        if user.status != 1 {
            return Err(AppError::Authentication("用户已被禁用".to_string()));
        }

        // 检查用户角色，如果没有角色则分配默认User角色
        let role_service = crate::services::RoleService::new(self.db_config.clone());
        let mut user_roles = role_service.get_user_roles(user.cpsn_num.clone()).await?;

        if user_roles.is_empty() {
            tracing::info!("用户 {} 没有角色，分配默认User角色", user.cpsn_num);

            // 查找默认User角色的ID（从数据库查询而不是硬编码）
            let default_role_id = match self.get_default_user_role_id().await {
                Ok(role_id) => role_id,
                Err(e) => {
                    tracing::error!("获取默认User角色ID失败: {:?}", e);
                    return Err(AppError::Authentication("系统配置错误：无法获取默认角色".to_string()));
                }
            };

            match role_service.assign_role_to_user(&user.cpsn_num, default_role_id).await {
                Ok(_) => {
                    tracing::info!("成功为用户 {} 分配默认User角色", user.cpsn_num);
                    // 重新获取用户角色
                    user_roles = role_service.get_user_roles(user.cpsn_num.clone()).await?;
                }
                Err(e) => {
                    tracing::error!("为用户 {} 分配默认角色失败: {:?}", user.cpsn_num, e);
                    return Err(AppError::Authentication("用户角色分配失败".to_string()));
                }
            }
        }

        // 所有角色都可以登录（包括User角色）
        tracing::info!("用户 {} 登录成功，角色: {:?}", user.cpsn_num, user_roles.iter().map(|r| &r.name).collect::<Vec<_>>());

        // 更新最后登录时间
        self.update_last_login(&user.cpsn_num).await?;

        // 生成JWT Token
        let claims = Claims::new(
            &user.cpsn_num,
            &user.username,
            user.is_admin,
            self.get_user_role_id(&user.cpsn_num).await?,
            self.jwt_expires_in,
        );

        let token = create_jwt(&claims, &self.jwt_secret)?;

        // 获取用户角色信息
        let role_service = crate::services::RoleService::new(self.db_config.clone());
        let user_roles = role_service.get_user_roles(user.cpsn_num.clone()).await.unwrap_or_else(|_| Vec::new());
        let role_responses: Vec<crate::models::role::RoleResponse> = user_roles.into_iter().map(crate::models::role::RoleResponse::from).collect();

        let mut user_response = UserResponse::from(user);
        user_response.roles = Some(role_responses);

        Ok(LoginResponse {
            token,
            user: user_response,
        })
    }

    // 验证Token
    pub async fn verify_token(&self, token: &str) -> AppResult<Claims> {
        let claims = verify_jwt(token, &self.jwt_secret)?;

        // 验证用户是否仍然有效
        let user = self.find_user_by_id(&claims.sub).await?;

        if user.status != 1 {
            return Err(AppError::Authentication("用户已被禁用".to_string()));
        }

        Ok(claims)
    }

    // 刷新Token
    pub async fn refresh_token(&self, refresh_token: &str) -> AppResult<LoginResponse> {
        use crate::utils::verify_refresh_token;

        let refresh_claims = verify_refresh_token(refresh_token, &self.jwt_secret)?;
        let user = self.find_user_by_id(&refresh_claims.sub).await?;

        if user.status != 1 {
            return Err(AppError::Authentication("用户已被禁用".to_string()));
        }

        // 生成新的访问Token
        let claims = Claims::new(
            &user.cpsn_num,
            &user.username,
            user.is_admin,
            self.get_user_role_id(&user.cpsn_num).await?,
            self.jwt_expires_in,
        );

        let token = create_jwt(&claims, &self.jwt_secret)?;

        Ok(LoginResponse {
            token,
            user: UserResponse::from(user),
        })
    }



    // 根据用户名查找用户
    async fn find_user_by_username(&self, username: &str) -> AppResult<User> {
        let mut client = self.db_config.get_app_connection().await?;

        let mut query = Query::new("SELECT cpsn_num, cPsn_Name, rEmployState, rSex, cDept_num, username, password_hash, status, is_admin, last_login_at, created_at, updated_at FROM person WHERE username = @P1");
        query.bind(username);
        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        if let Some(row) = rows.first() {
            Ok(User {
                cpsn_num: row.get::<&str, _>(0).unwrap_or("").to_string(),
                name: row.get::<&str, _>(1).unwrap_or("").to_string(),
                employ_state: row.get::<&str, _>(2).unwrap_or("").to_string(),
                sex: row.get::<&str, _>(3).map(|s| s.to_string()),
                dept_num: row.get::<&str, _>(4).map(|s| s.to_string()),
                username: Self::get_username_from_row(&row),
                password_hash: row.get::<&str, _>(6).map(|s| s.to_string()),
                status: row.get::<i32, _>(7).unwrap_or(1),
                is_admin: row.get::<bool, _>(8).unwrap_or(false),
                last_login_at: row.get::<chrono::DateTime<Utc>, _>(9),
                created_at: row.get::<chrono::DateTime<Utc>, _>(10),
                updated_at: row.get::<chrono::DateTime<Utc>, _>(11),
            })
        } else {
            Err(AppError::Authentication("用户名或密码错误".to_string()))
        }
    }

    // 根据人员编号查找用户
    async fn find_user_by_id(&self, cpsn_num: &str) -> AppResult<User> {
        let mut client = self.db_config.get_app_connection().await?;

        let mut query = Query::new("SELECT cpsn_num, cPsn_Name, rEmployState, rSex, cDept_num, username, password_hash, status, is_admin, last_login_at, created_at, updated_at FROM person WHERE cpsn_num = @P1");
        query.bind(cpsn_num);
        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        if let Some(row) = rows.first() {
            Ok(User {
                cpsn_num: row.get::<&str, _>(0).unwrap_or("").to_string(),
                name: row.get::<&str, _>(1).unwrap_or("").to_string(),
                employ_state: row.get::<&str, _>(2).unwrap_or("").to_string(),
                sex: row.get::<&str, _>(3).map(|s| s.to_string()),
                dept_num: row.get::<&str, _>(4).map(|s| s.to_string()),
                username: Self::get_username_from_row(&row),
                password_hash: row.get::<&str, _>(6).map(|s| s.to_string()),
                status: row.get::<i32, _>(7).unwrap_or(1),
                is_admin: row.get::<u8, _>(8).map(|v| v != 0).unwrap_or(false),
                last_login_at: row.get::<chrono::DateTime<Utc>, _>(9),
                created_at: row.get::<chrono::DateTime<Utc>, _>(10),
                updated_at: row.get::<chrono::DateTime<Utc>, _>(11),
            })
        } else {
            Err(AppError::Authentication("用户不存在".to_string()))
        }
    }

    // 更新最后登录时间
    async fn update_last_login(&self, cpsn_num: &str) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        let mut query = Query::new("UPDATE person SET last_login_at = GETDATE() WHERE cpsn_num = @P1");
        query.bind(cpsn_num);
        query.execute(&mut *client).await?;

        Ok(())
    }

    // 获取用户角色ID
    async fn get_user_role_id(&self, cpsn_num: &str) -> AppResult<Option<i64>> {
        let mut client = self.db_config.get_app_connection().await?;

        let mut query = Query::new("SELECT role_id FROM user_roles WHERE user_id = @P1");
        query.bind(cpsn_num);
        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        if let Some(row) = rows.first() {
            Ok(Some(row.get::<i64, _>(0).unwrap_or(0)))
        } else {
            Ok(None)
        }
    }

    // 获取默认User角色的ID（从数据库查询）
    async fn get_default_user_role_id(&self) -> AppResult<i64> {
        let mut client = self.db_config.get_app_connection().await?;

        let mut query = Query::new("SELECT id FROM roles WHERE name = @P1");
        query.bind("user");
        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        if let Some(row) = rows.first() {
            Ok(row.get::<i64, _>(0).unwrap_or(0))
        } else {
            Err(AppError::Database("默认User角色不存在".to_string()))
        }
    }
}
