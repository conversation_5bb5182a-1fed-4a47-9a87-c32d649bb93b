use crate::config::DatabaseConfig;
use crate::models::team_binding::*;
use crate::utils::response::{AppError, AppResult};
use tiberius::Query;
use std::sync::Arc;

/// 班组绑定服务
#[derive(Clone)]
pub struct TeamBindingService {
    db_config: Arc<DatabaseConfig>,
}

impl TeamBindingService {
    /// 创建新的班组绑定服务实例
    pub fn new(db_config: Arc<DatabaseConfig>) -> Self {
        Self { db_config }
    }

    // ========================================
    // 班组产品绑定相关方法
    // ========================================

    /// 创建班组产品绑定
    pub async fn create_team_product(
        &self,
        request: &CreateTeamProductRequest,
        created_by: &str,
    ) -> AppResult<i64> {
        let mut client = self.db_config.get_app_connection().await?;

        // 检查是否已存在相同绑定（包括已删除的）
        let mut exists_query = Query::new(
            "SELECT id, status FROM TeamProducts WHERE team_id = @P1 AND inventory_id = @P2"
        );
        exists_query.bind(request.team_id);
        exists_query.bind(&request.inventory_id);

        let exists_stream = exists_query.query(&mut *client).await
            .map_err(|e| AppError::Database(format!("检查产品绑定失败: {}", e)))?;
        let exists_rows: Vec<tiberius::Row> = exists_stream.into_first_result().await
            .map_err(|e| AppError::Database(format!("获取检查结果失败: {}", e)))?;

        if let Some(row) = exists_rows.first() {
            let existing_id: i64 = row.get("id").unwrap_or(0);
            let status: u8 = row.get("status").unwrap_or(0);

            if status == 1 {
                // 已存在且状态为激活，返回错误
                return Err(AppError::BadRequest("该班组已绑定此产品".to_string()));
            } else {
                // 已存在但状态为删除，更新状态为激活
                let mut update_query = Query::new(
                    "UPDATE TeamProducts SET status = 1, updated_at = GETDATE(), created_by = @P1 WHERE id = @P2"
                );
                update_query.bind(created_by);
                update_query.bind(existing_id);

                update_query.execute(&mut *client).await
                    .map_err(|e| AppError::Database(format!("更新产品绑定状态失败: {}", e)))?;

                return Ok(existing_id);
            }
        }

        // 不存在，插入新绑定
        let mut insert_query = Query::new(
            "INSERT INTO TeamProducts (team_id, inventory_id, created_by, status, created_at, updated_at) 
             VALUES (@P1, @P2, @P3, 1, GETDATE(), GETDATE());
             SELECT CAST(SCOPE_IDENTITY() AS BIGINT) as id;"
        );
        insert_query.bind(request.team_id);
        insert_query.bind(&request.inventory_id);
        insert_query.bind(created_by);

        let insert_stream = insert_query.query(&mut *client).await
            .map_err(|e| AppError::Database(format!("创建产品绑定失败: {}", e)))?;
        let insert_rows: Vec<tiberius::Row> = insert_stream.into_first_result().await
            .map_err(|e| AppError::Database(format!("获取插入结果失败: {}", e)))?;

        if let Some(row) = insert_rows.first() {
            let id: i64 = row.get("id").unwrap_or(0);
            Ok(id)
        } else {
            Err(AppError::Database("创建产品绑定失败，未返回ID".to_string()))
        }
    }

    /// 批量创建班组产品绑定
    pub async fn batch_create_team_products(
        &self,
        request: &BatchCreateTeamProductRequest,
        created_by: &str,
    ) -> AppResult<Vec<i64>> {
        let mut client = self.db_config.get_app_connection().await?;
        let mut created_ids = Vec::new();

        for inventory_id in &request.inventory_ids {
            // 检查是否已存在相同绑定（包括已删除的）
            let mut exists_query = Query::new(
                "SELECT id, status FROM TeamProducts WHERE team_id = @P1 AND inventory_id = @P2"
            );
            exists_query.bind(request.team_id);
            exists_query.bind(inventory_id);

            let exists_stream = exists_query.query(&mut *client).await
                .map_err(|e| AppError::Database(format!("检查产品绑定失败: {}", e)))?;
            let exists_rows: Vec<tiberius::Row> = exists_stream.into_first_result().await
                .map_err(|e| AppError::Database(format!("获取检查结果失败: {}", e)))?;

            if let Some(row) = exists_rows.first() {
                let existing_id: i64 = row.get("id").unwrap_or(0);
                let status: u8 = row.get("status").unwrap_or(0);

                if status == 1 {
                    // 已存在且状态为激活，跳过
                    continue;
                } else {    
                    // 已存在但状态为删除，更新状态为激活
                    let mut update_query = Query::new(
                        "UPDATE TeamProducts SET status = 1, updated_at = GETDATE(), created_by = @P1 WHERE id = @P2"
                    );
                    update_query.bind(created_by);
                    update_query.bind(existing_id);

                    update_query.execute(&mut *client).await
                        .map_err(|e| AppError::Database(format!("更新产品绑定状态失败: {}", e)))?;

                    created_ids.push(existing_id);
                    continue;
                }
            }

            // 不存在，插入新绑定
            let mut insert_query = Query::new(
                "INSERT INTO TeamProducts (team_id, inventory_id, created_by, status, created_at, updated_at) 
                 VALUES (@P1, @P2, @P3, 1, GETDATE(), GETDATE());
                 SELECT CAST(SCOPE_IDENTITY() AS BIGINT) as id;"
            );
            insert_query.bind(request.team_id);
            insert_query.bind(inventory_id);
            insert_query.bind(created_by);

            let insert_stream = insert_query.query(&mut *client).await
                .map_err(|e| AppError::Database(format!("创建产品绑定失败: {}", e)))?;
            let insert_rows: Vec<tiberius::Row> = insert_stream.into_first_result().await
                .map_err(|e| AppError::Database(format!("获取插入结果失败: {}", e)))?;

            if let Some(row) = insert_rows.first() {
                let id: i64 = row.get("id").unwrap_or(0);
                created_ids.push(id);
            }
        }

        Ok(created_ids)
    }

    /// 获取班组产品绑定列表（分页）
    pub async fn get_team_products(
        &self,
        query: &TeamBindingQueryRequest,
    ) -> AppResult<TeamProductPageResponse> {
        let mut client = self.db_config.get_app_connection().await?;

        let page = query.page.unwrap_or(1);
        let page_size = query.page_size.unwrap_or(20).min(100);
        let offset = (page - 1) * page_size;

        // 构建WHERE条件
        let mut where_conditions = Vec::new();
        let mut params = Vec::new();

        if let Some(team_id) = query.team_id {
            where_conditions.push("tp.team_id = @P1".to_string());
            params.push(team_id.to_string());
        }

        if let Some(status) = query.status {
            let param_index = params.len() + 1;
            where_conditions.push(format!("tp.status = @P{}", param_index));
            params.push(status.to_string());
        }

        if let Some(keyword) = &query.keyword {
            if !keyword.trim().is_empty() {
                let param_index = params.len() + 1;
                where_conditions.push(format!(
                    "(t.TeamName LIKE @P{} OR inv.cinvcode LIKE @P{} OR inv.cinvname LIKE @P{} OR inv.cInvStd LIKE @P{})",
                    param_index, param_index, param_index, param_index
                ));
                params.push(format!("%{}%", keyword.trim()));
            }
        }

        let where_clause = if where_conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_conditions.join(" AND "))
        };

        // 查询总数
        let count_sql = format!(
            "SELECT COUNT(*) as total
             FROM TeamProducts tp
             LEFT JOIN Teams t ON tp.team_id = t.TeamID
             LEFT JOIN inventory inv ON tp.inventory_id = inv.cinvcode
             {}",
            where_clause
        );

        let mut count_query = Query::new(&count_sql);
        for param in &params {
            count_query.bind(param);
        }

        let count_stream = count_query.query(&mut *client).await
            .map_err(|e| AppError::Database(format!("查询产品绑定总数失败: {}", e)))?;
        let count_rows: Vec<tiberius::Row> = count_stream.into_first_result().await
            .map_err(|e| AppError::Database(format!("获取总数结果失败: {}", e)))?;

        let total = count_rows.first()
            .and_then(|row| row.get::<i32, _>("total"))
            .unwrap_or(0) as u32;

        // 查询数据
        let data_sql = format!(
            "SELECT 
                tp.id, tp.team_id, tp.inventory_id, tp.created_by, tp.status,
                tp.created_at, tp.updated_at,
                t.TeamName as team_name,
                inv.cinvname, inv.cInvStd as cinv_std,
                p.cPsn_Name as created_by_name
             FROM TeamProducts tp
             LEFT JOIN Teams t ON tp.team_id = t.TeamID
             LEFT JOIN inventory inv ON tp.inventory_id = inv.cinvcode
             LEFT JOIN person p ON tp.created_by = p.cpsn_num
             {}
             ORDER BY tp.created_at DESC
             OFFSET {} ROWS FETCH NEXT {} ROWS ONLY",
            where_clause, offset, page_size
        );

        let mut data_query = Query::new(&data_sql);
        for param in &params {
            data_query.bind(param);
        }

        let data_stream = data_query.query(&mut *client).await
            .map_err(|e| AppError::Database(format!("查询产品绑定数据失败: {}", e)))?;
        let data_rows: Vec<tiberius::Row> = data_stream.into_first_result().await
            .map_err(|e| AppError::Database(format!("获取数据结果失败: {}", e)))?;

        let items: Vec<TeamProductResponse> = data_rows
            .into_iter()
            .map(|row| TeamProductResponse {
                id: row.get("id").unwrap_or(0),
                team_id: row.get("team_id").unwrap_or(0),
                team_name: row.get::<&str, _>("team_name").map(|s| s.to_string()),
                inventory_id: row.get::<&str, _>("inventory_id").unwrap_or("").to_string(),
                cinvname: row.get::<&str, _>("cinvname").map(|s| s.to_string()),
                cinv_std: row.get::<&str, _>("cinv_std").map(|s| s.to_string()),
                created_by: row.get::<&str, _>("created_by").map(|s| s.to_string()),
                created_by_name: row.get::<&str, _>("created_by_name").map(|s| s.to_string()),
                status: row.get::<u8, _>("status").unwrap_or(1),
                created_at: row.get("created_at").unwrap_or_default(),
                updated_at: row.get("updated_at").unwrap_or_default(),
            })
            .collect();

        let total_pages = ((total) + page_size - 1) / page_size;

        Ok(TeamProductPageResponse {
            items,
            total,
            page,
            page_size,
            total_pages,
        })
    }

    /// 删除班组产品绑定（软删除）
    pub async fn delete_team_product(&self, id: i64) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        let mut update_query = Query::new(
            "UPDATE TeamProducts SET status = 0, updated_at = GETDATE() WHERE id = @P1"
        );
        update_query.bind(id);

        let result = update_query.execute(&mut *client).await
            .map_err(|e| AppError::Database(format!("删除产品绑定失败: {}", e)))?;

        if result.rows_affected().first().unwrap_or(&0) == &0 {
            return Err(AppError::NotFound("班组产品绑定不存在".to_string()));
        }

        Ok(())
    }

    /// 更新班组产品绑定状态
    pub async fn update_team_product_status(
        &self,
        id: i64,
        status: u8,
    ) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        let mut update_query = Query::new(
            "UPDATE TeamProducts SET status = @P1, updated_at = GETDATE() WHERE id = @P2"
        );
        update_query.bind(status);
        update_query.bind(id);

        let result = update_query.execute(&mut *client).await
            .map_err(|e| AppError::Database(format!("更新产品绑定状态失败: {}", e)))?;

        if result.rows_affected().first().unwrap_or(&0) == &0 {
            return Err(AppError::NotFound("班组产品绑定不存在".to_string()));
        }

        Ok(())
    }

    /// 获取班组的可用产品列表（用于指令卡创建）
    pub async fn get_team_available_products(&self, team_id: i32) -> AppResult<Vec<TeamProductResponse>> {
        let mut client = self.db_config.get_app_connection().await?;

        let mut query = Query::new(
            "SELECT 
                tp.id, tp.team_id, tp.inventory_id, tp.created_by, tp.status,
                tp.created_at, tp.updated_at,
                t.TeamName as team_name,
                inv.cinvname, inv.cInvStd as cinv_std,
                p.cPsn_Name as created_by_name
             FROM TeamProducts tp
             LEFT JOIN Teams t ON tp.team_id = t.TeamID
             LEFT JOIN inventory inv ON tp.inventory_id = inv.cinvcode
             LEFT JOIN person p ON tp.created_by = p.cpsn_num
             WHERE tp.team_id = @P1 AND tp.status = 1
             ORDER BY tp.created_at DESC"
        );
        query.bind(team_id);

        let stream = query.query(&mut *client).await
            .map_err(|e| AppError::Database(format!("查询可用产品失败: {}", e)))?;
        let rows: Vec<tiberius::Row> = stream.into_first_result().await
            .map_err(|e| AppError::Database(format!("获取可用产品结果失败: {}", e)))?;

        let products: Vec<TeamProductResponse> = rows
            .into_iter()
            .map(|row| TeamProductResponse {
                id: row.get("id").unwrap_or(0),
                team_id: row.get("team_id").unwrap_or(0),
                team_name: row.get::<&str, _>("team_name").map(|s| s.to_string()),
                inventory_id: row.get::<&str, _>("inventory_id").unwrap_or("").to_string(),
                cinvname: row.get::<&str, _>("cinvname").map(|s| s.to_string()),
                cinv_std: row.get::<&str, _>("cinv_std").map(|s| s.to_string()),
                created_by: row.get::<&str, _>("created_by").map(|s| s.to_string()),
                created_by_name: row.get::<&str, _>("created_by_name").map(|s| s.to_string()),
                status: row.get::<u8, _>("status").unwrap_or(1),
                created_at: row.get("created_at").unwrap_or_default(),
                updated_at: row.get("updated_at").unwrap_or_default(),
            })
            .collect();

        Ok(products)
    }

    // ========================================
    // 班组设备绑定相关方法
    // ========================================

    /// 创建班组设备绑定
    pub async fn create_team_equipment(
        &self,
        request: &CreateTeamEquipmentRequest,
        created_by: &str,
    ) -> AppResult<i64> {
        let mut client = self.db_config.get_app_connection().await?;

        // 检查是否已存在相同绑定（包括已删除的）
        let mut exists_query = Query::new(
            "SELECT id, status FROM TeamEquipments WHERE team_id = @P1 AND equipment_id = @P2"
        );
        exists_query.bind(request.team_id);
        exists_query.bind(&request.equipment_id);

        let exists_stream = exists_query.query(&mut *client).await
            .map_err(|e| AppError::Database(format!("检查设备绑定失败: {}", e)))?;
        let exists_rows: Vec<tiberius::Row> = exists_stream.into_first_result().await
            .map_err(|e| AppError::Database(format!("获取检查结果失败: {}", e)))?;

        if let Some(row) = exists_rows.first() {
            let existing_id: i64 = row.get("id").unwrap_or(0);
            let status: u8 = row.get("status").unwrap_or(0);

            if status == 1 {
                // 已存在且状态为激活，返回错误
                return Err(AppError::BadRequest("该班组已绑定此设备".to_string()));
            } else {
                // 已存在但状态为删除，更新状态为激活
                let mut update_query = Query::new(
                    "UPDATE TeamEquipments SET status = 1, updated_at = GETDATE(), created_by = @P1 WHERE id = @P2"
                );
                update_query.bind(created_by);
                update_query.bind(existing_id);

                update_query.execute(&mut *client).await
                    .map_err(|e| AppError::Database(format!("更新设备绑定状态失败: {}", e)))?;

                return Ok(existing_id);
            }
        }

        // 不存在，插入新绑定
        let mut insert_query = Query::new(
            "INSERT INTO TeamEquipments (team_id, equipment_id, created_by, status, created_at, updated_at)
             VALUES (@P1, @P2, @P3, 1, GETDATE(), GETDATE());
             SELECT CAST(SCOPE_IDENTITY() AS BIGINT) as id;"
        );
        insert_query.bind(request.team_id);
        insert_query.bind(&request.equipment_id);
        insert_query.bind(created_by);

        let insert_stream = insert_query.query(&mut *client).await
            .map_err(|e| AppError::Database(format!("创建设备绑定失败: {}", e)))?;
        let insert_rows: Vec<tiberius::Row> = insert_stream.into_first_result().await
            .map_err(|e| AppError::Database(format!("获取插入结果失败: {}", e)))?;

        if let Some(row) = insert_rows.first() {
            let id: i64 = row.get("id").unwrap_or(0);
            Ok(id)
        } else {
            Err(AppError::Database("创建设备绑定失败，未返回ID".to_string()))
        }
    }

    /// 批量创建班组设备绑定
    pub async fn batch_create_team_equipments(
        &self,
        request: &BatchCreateTeamEquipmentRequest,
        created_by: &str,
    ) -> AppResult<Vec<i64>> {
        let mut client = self.db_config.get_app_connection().await?;
        let mut created_ids = Vec::new();

        for equipment_id in &request.equipment_ids {
            // 检查是否已存在相同绑定（包括已删除的）
            let mut exists_query = Query::new(
                "SELECT id, status FROM TeamEquipments WHERE team_id = @P1 AND equipment_id = @P2"
            );
            exists_query.bind(request.team_id);
            exists_query.bind(equipment_id);

            let exists_stream = exists_query.query(&mut *client).await
                .map_err(|e| AppError::Database(format!("检查设备绑定失败: {}", e)))?;
            let exists_rows: Vec<tiberius::Row> = exists_stream.into_first_result().await
                .map_err(|e| AppError::Database(format!("获取检查结果失败: {}", e)))?;

            if let Some(row) = exists_rows.first() {
                let existing_id: i64 = row.get("id").unwrap_or(0);
                let status: u8 = row.get("status").unwrap_or(0);

                if status == 1 {
                    // 已存在且状态为激活，跳过
                    continue;
                } else {
                    // 已存在但状态为删除，更新状态为激活
                    let mut update_query = Query::new(
                        "UPDATE TeamEquipments SET status = 1, updated_at = GETDATE(), created_by = @P1 WHERE id = @P2"
                    );
                    update_query.bind(created_by);
                    update_query.bind(existing_id);

                    update_query.execute(&mut *client).await
                        .map_err(|e| AppError::Database(format!("更新设备绑定状态失败: {}", e)))?;

                    created_ids.push(existing_id);
                    continue;
                }
            }

            // 不存在，插入新绑定
            let mut insert_query = Query::new(
                "INSERT INTO TeamEquipments (team_id, equipment_id, created_by, status, created_at, updated_at)
                 VALUES (@P1, @P2, @P3, 1, GETDATE(), GETDATE());
                 SELECT CAST(SCOPE_IDENTITY() AS BIGINT) as id;"
            );
            insert_query.bind(request.team_id);
            insert_query.bind(equipment_id);
            insert_query.bind(created_by);

            let insert_stream = insert_query.query(&mut *client).await
                .map_err(|e| AppError::Database(format!("创建设备绑定失败: {}", e)))?;
            let insert_rows: Vec<tiberius::Row> = insert_stream.into_first_result().await
                .map_err(|e| AppError::Database(format!("获取插入结果失败: {}", e)))?;

            if let Some(row) = insert_rows.first() {
                let id: i64 = row.get("id").unwrap_or(0);
                created_ids.push(id);
            }
        }

        Ok(created_ids)
    }

    /// 获取班组设备绑定列表（分页）
    pub async fn get_team_equipments(
        &self,
        query: &TeamBindingQueryRequest,
    ) -> AppResult<TeamEquipmentPageResponse> {
        let mut client = self.db_config.get_app_connection().await?;

        let page = query.page.unwrap_or(1);
        let page_size = query.page_size.unwrap_or(20).min(100);
        let offset = (page - 1) * page_size;

        // 构建WHERE条件
        let mut where_conditions = Vec::new();
        let mut params = Vec::new();

        if let Some(team_id) = query.team_id {
            where_conditions.push("te.team_id = @P1".to_string());
            params.push(team_id.to_string());
        }

        if let Some(status) = query.status {
            let param_index = params.len() + 1;
            where_conditions.push(format!("te.status = @P{}", param_index));
            params.push(status.to_string());
        }

        if let Some(keyword) = &query.keyword {
            if !keyword.trim().is_empty() {
                let param_index = params.len() + 1;
                where_conditions.push(format!(
                    "(t.TeamName LIKE @P{} OR eq.ceqcode LIKE @P{} OR eq.ceqname LIKE @P{})",
                    param_index, param_index, param_index
                ));
                params.push(format!("%{}%", keyword.trim()));
            }
        }

        let where_clause = if where_conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_conditions.join(" AND "))
        };

        // 查询总数
        let count_sql = format!(
            "SELECT COUNT(*) as total
             FROM TeamEquipments te
             LEFT JOIN Teams t ON te.team_id = t.TeamID
             LEFT JOIN EQ_QEQDataSel eq ON te.equipment_id = eq.ceqcode
             {}",
            where_clause
        );

        let mut count_query = Query::new(&count_sql);
        for param in &params {
            count_query.bind(param);
        }

        let count_stream = count_query.query(&mut *client).await
            .map_err(|e| AppError::Database(format!("查询设备绑定总数失败: {}", e)))?;
        let count_rows: Vec<tiberius::Row> = count_stream.into_first_result().await
            .map_err(|e| AppError::Database(format!("获取总数结果失败: {}", e)))?;

        let total = count_rows.first()
            .and_then(|row| row.get::<i32, _>("total"))
            .unwrap_or(0) as u32;

        // 查询数据
        let data_sql = format!(
            "SELECT
                te.id, te.team_id, te.equipment_id, te.created_by, te.status,
                te.created_at, te.updated_at,
                t.TeamName as team_name,
                eq.ceqname,
                p.cPsn_Name as created_by_name
             FROM TeamEquipments te
             LEFT JOIN Teams t ON te.team_id = t.TeamID
             LEFT JOIN EQ_QEQDataSel eq ON te.equipment_id = eq.ceqcode
             LEFT JOIN person p ON te.created_by = p.cpsn_num
             {}
             ORDER BY te.created_at DESC
             OFFSET {} ROWS FETCH NEXT {} ROWS ONLY",
            where_clause, offset, page_size
        );

        let mut data_query = Query::new(&data_sql);
        for param in &params {
            data_query.bind(param);
        }

        let data_stream = data_query.query(&mut *client).await
            .map_err(|e| AppError::Database(format!("查询设备绑定数据失败: {}", e)))?;
        let data_rows: Vec<tiberius::Row> = data_stream.into_first_result().await
            .map_err(|e| AppError::Database(format!("获取数据结果失败: {}", e)))?;

        let items: Vec<TeamEquipmentResponse> = data_rows
            .into_iter()
            .map(|row| TeamEquipmentResponse {
                id: row.get("id").unwrap_or(0),
                team_id: row.get("team_id").unwrap_or(0),
                team_name: row.get::<&str, _>("team_name").map(|s| s.to_string()),
                equipment_id: row.get::<&str, _>("equipment_id").unwrap_or("").to_string(),
                ceqname: row.get::<&str, _>("ceqname").map(|s| s.to_string()),
                created_by: row.get::<&str, _>("created_by").map(|s| s.to_string()),
                created_by_name: row.get::<&str, _>("created_by_name").map(|s| s.to_string()),
                status: row.get::<u8, _>("status").unwrap_or(1),
                created_at: row.get("created_at").unwrap_or_default(),
                updated_at: row.get("updated_at").unwrap_or_default(),
            })
            .collect();

        let total_pages = ((total) + page_size - 1) / page_size;

        Ok(TeamEquipmentPageResponse {
            items,
            total,
            page,
            page_size,
            total_pages,
        })
    }

    /// 删除班组设备绑定（软删除）
    pub async fn delete_team_equipment(&self, id: i64) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        let mut update_query = Query::new(
            "UPDATE TeamEquipments SET status = 0, updated_at = GETDATE() WHERE id = @P1"
        );
        update_query.bind(id);

        let result = update_query.execute(&mut *client).await
            .map_err(|e| AppError::Database(format!("删除设备绑定失败: {}", e)))?;

        if result.rows_affected().first().unwrap_or(&0) == &0 {
            return Err(AppError::NotFound("班组设备绑定不存在".to_string()));
        }

        Ok(())
    }

    /// 更新班组设备绑定状态
    pub async fn update_team_equipment_status(
        &self,
        id: i64,
        status: u8,
    ) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        let mut update_query = Query::new(
            "UPDATE TeamEquipments SET status = @P1, updated_at = GETDATE() WHERE id = @P2"
        );
        update_query.bind(status);
        update_query.bind(id);

        let result = update_query.execute(&mut *client).await
            .map_err(|e| AppError::Database(format!("更新设备绑定状态失败: {}", e)))?;

        if result.rows_affected().first().unwrap_or(&0) == &0 {
            return Err(AppError::NotFound("班组设备绑定不存在".to_string()));
        }

        Ok(())
    }

    /// 获取班组的可用设备列表（用于指令卡创建）
    pub async fn get_team_available_equipments(&self, team_id: i32) -> AppResult<Vec<TeamEquipmentResponse>> {
        let mut client = self.db_config.get_app_connection().await?;

        let mut query = Query::new(
            "SELECT
                te.id, te.team_id, te.equipment_id, te.created_by, te.status,
                te.created_at, te.updated_at,
                t.TeamName as team_name,
                eq.ceqname,
                p.cPsn_Name as created_by_name
             FROM TeamEquipments te
             LEFT JOIN Teams t ON te.team_id = t.TeamID
             LEFT JOIN EQ_QEQDataSel eq ON te.equipment_id = eq.ceqcode
             LEFT JOIN person p ON te.created_by = p.cpsn_num
             WHERE te.team_id = @P1 AND te.status = 1
             ORDER BY te.created_at DESC"
        );
        query.bind(team_id);

        let stream = query.query(&mut *client).await
            .map_err(|e| AppError::Database(format!("查询可用设备失败: {}", e)))?;
        let rows: Vec<tiberius::Row> = stream.into_first_result().await
            .map_err(|e| AppError::Database(format!("获取可用设备结果失败: {}", e)))?;

        let equipments: Vec<TeamEquipmentResponse> = rows
            .into_iter()
            .map(|row| TeamEquipmentResponse {
                id: row.get("id").unwrap_or(0),
                team_id: row.get("team_id").unwrap_or(0),
                team_name: row.get::<&str, _>("team_name").map(|s| s.to_string()),
                equipment_id: row.get::<&str, _>("equipment_id").unwrap_or("").to_string(),
                ceqname: row.get::<&str, _>("ceqname").map(|s| s.to_string()),
                created_by: row.get::<&str, _>("created_by").map(|s| s.to_string()),
                created_by_name: row.get::<&str, _>("created_by_name").map(|s| s.to_string()),
                status: row.get::<u8, _>("status").unwrap_or(1),
                created_at: row.get("created_at").unwrap_or_default(),
                updated_at: row.get("updated_at").unwrap_or_default(),
            })
            .collect();

        Ok(equipments)
    }

    // ========================================
    // 班组工序绑定相关方法
    // ========================================

    /// 创建班组工序绑定
    pub async fn create_team_operation(
        &self,
        request: &CreateTeamOperationRequest,
        created_by: &str,
    ) -> AppResult<i64> {
        let mut client = self.db_config.get_app_connection().await?;

        // 检查是否已存在相同绑定（包括已删除的）
        let mut exists_query = Query::new(
            "SELECT id, status FROM TeamOperations WHERE team_id = @P1 AND operation_id = @P2"
        );
        exists_query.bind(request.team_id);
        exists_query.bind(&request.operation_id);

        let exists_stream = exists_query.query(&mut *client).await
            .map_err(|e| AppError::Database(format!("检查工序绑定失败: {}", e)))?;
        let exists_rows: Vec<tiberius::Row> = exists_stream.into_first_result().await
            .map_err(|e| AppError::Database(format!("获取检查结果失败: {}", e)))?;

        if let Some(row) = exists_rows.first() {
            let existing_id: i64 = row.get("id").unwrap_or(0);
            let status: u8 = row.get("status").unwrap_or(0);

            if status == 1 {
                // 已存在且状态为激活，返回错误
                return Err(AppError::BadRequest("该班组已绑定此工序".to_string()));
            } else {
                // 已存在但状态为删除，更新状态为激活
                let mut update_query = Query::new(
                    "UPDATE TeamOperations SET status = 1, updated_at = GETDATE(), created_by = @P1 WHERE id = @P2"
                );
                update_query.bind(created_by);
                update_query.bind(existing_id);

                update_query.execute(&mut *client).await
                    .map_err(|e| AppError::Database(format!("更新工序绑定状态失败: {}", e)))?;

                return Ok(existing_id);
            }
        }

        // 不存在，插入新绑定
        let mut insert_query = Query::new(
            "INSERT INTO TeamOperations (team_id, operation_id, created_by, status, created_at, updated_at)
             VALUES (@P1, @P2, @P3, 1, GETDATE(), GETDATE());
             SELECT CAST(SCOPE_IDENTITY() AS BIGINT) as id;"
        );
        insert_query.bind(request.team_id);
        insert_query.bind(&request.operation_id);
        insert_query.bind(created_by);

        let insert_stream = insert_query.query(&mut *client).await
            .map_err(|e| AppError::Database(format!("创建工序绑定失败: {}", e)))?;
        let insert_rows: Vec<tiberius::Row> = insert_stream.into_first_result().await
            .map_err(|e| AppError::Database(format!("获取插入结果失败: {}", e)))?;

        if let Some(row) = insert_rows.first() {
            let id: i64 = row.get("id").unwrap_or(0);
            Ok(id)
        } else {
            Err(AppError::Database("创建工序绑定失败，未返回ID".to_string()))
        }
    }

    /// 批量创建班组工序绑定
    pub async fn batch_create_team_operations(
        &self,
        request: &BatchCreateTeamOperationRequest,
        created_by: &str,
    ) -> AppResult<Vec<i64>> {
        let mut client = self.db_config.get_app_connection().await?;
        let mut created_ids = Vec::new();

        for operation_id in &request.operation_ids {
            // 检查是否已存在相同绑定（包括已删除的）
            let mut exists_query = Query::new(
                "SELECT id, status FROM TeamOperations WHERE team_id = @P1 AND operation_id = @P2"
            );
            exists_query.bind(request.team_id);
            exists_query.bind(operation_id);

            let exists_stream = exists_query.query(&mut *client).await
                .map_err(|e| AppError::Database(format!("检查工序绑定失败: {}", e)))?;
            let exists_rows: Vec<tiberius::Row> = exists_stream.into_first_result().await
                .map_err(|e| AppError::Database(format!("获取检查结果失败: {}", e)))?;

            if let Some(row) = exists_rows.first() {
                let existing_id: i64 = row.get("id").unwrap_or(0);
                let status: u8 = row.get("status").unwrap_or(0);

                if status == 1 {
                    // 已存在且状态为激活，跳过
                    continue;
                } else {
                    // 已存在但状态为删除，更新状态为激活
                    let mut update_query = Query::new(
                        "UPDATE TeamOperations SET status = 1, updated_at = GETDATE(), created_by = @P1 WHERE id = @P2"
                    );
                    update_query.bind(created_by);
                    update_query.bind(existing_id);

                    update_query.execute(&mut *client).await
                        .map_err(|e| AppError::Database(format!("更新工序绑定状态失败: {}", e)))?;

                    created_ids.push(existing_id);
                    continue;
                }
            }

            // 不存在，插入新绑定
            let mut insert_query = Query::new(
                "INSERT INTO TeamOperations (team_id, operation_id, created_by, status, created_at, updated_at)
                 VALUES (@P1, @P2, @P3, 1, GETDATE(), GETDATE());
                 SELECT CAST(SCOPE_IDENTITY() AS BIGINT) as id;"
            );
            insert_query.bind(request.team_id);
            insert_query.bind(operation_id);
            insert_query.bind(created_by);

            let insert_stream = insert_query.query(&mut *client).await
                .map_err(|e| AppError::Database(format!("创建工序绑定失败: {}", e)))?;
            let insert_rows: Vec<tiberius::Row> = insert_stream.into_first_result().await
                .map_err(|e| AppError::Database(format!("获取插入结果失败: {}", e)))?;

            if let Some(row) = insert_rows.first() {
                let id: i64 = row.get("id").unwrap_or(0);
                created_ids.push(id);
            }
        }

        Ok(created_ids)
    }

    /// 获取班组工序绑定列表（分页）
    pub async fn get_team_operations(
        &self,
        query: &TeamBindingQueryRequest,
    ) -> AppResult<TeamOperationPageResponse> {
        let mut client = self.db_config.get_app_connection().await?;

        let page = query.page.unwrap_or(1);
        let page_size = query.page_size.unwrap_or(20).min(100);
        let offset = (page - 1) * page_size;

        // 构建WHERE条件
        let mut where_conditions = Vec::new();
        let mut params = Vec::new();

        if let Some(team_id) = query.team_id {
            where_conditions.push("team_op.team_id = @P1".to_string());
            params.push(team_id.to_string());
        }

        if let Some(status) = query.status {
            let param_index = params.len() + 1;
            where_conditions.push(format!("team_op.status = @P{}", param_index));
            params.push(status.to_string());
        }

        if let Some(keyword) = &query.keyword {
            if !keyword.trim().is_empty() {
                let param_index = params.len() + 1;
                where_conditions.push(format!(
                    "(t.TeamName LIKE @P{} OR op.opcode LIKE @P{} OR op.Description LIKE @P{})",
                    param_index, param_index, param_index
                ));
                params.push(format!("%{}%", keyword.trim()));
            }
        }

        let where_clause = if where_conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_conditions.join(" AND "))
        };

        // 查询总数
        let count_sql = format!(
            "SELECT COUNT(*) as total
             FROM TeamOperations team_op
             LEFT JOIN Teams t ON team_op.team_id = t.TeamID
             LEFT JOIN operation op ON team_op.operation_id = op.opcode
             {}",
            where_clause
        );

        let mut count_query = Query::new(&count_sql);
        for param in &params {
            count_query.bind(param);
        }

        let count_stream = count_query.query(&mut *client).await
            .map_err(|e| AppError::Database(format!("查询工序绑定总数失败: {}", e)))?;
        let count_rows: Vec<tiberius::Row> = count_stream.into_first_result().await
            .map_err(|e| AppError::Database(format!("获取总数结果失败: {}", e)))?;

        let total = count_rows.first()
            .and_then(|row| row.get::<i32, _>("total"))
            .unwrap_or(0) as u32;

        // 查询数据
        let data_sql = format!(
            "SELECT
                team_op.id, team_op.team_id, team_op.operation_id, team_op.created_by, team_op.status,
                team_op.created_at, team_op.updated_at,
                t.TeamName as team_name,
                op.Description as description,
                p.cPsn_Name as created_by_name
             FROM TeamOperations team_op
             LEFT JOIN Teams t ON team_op.team_id = t.TeamID
             LEFT JOIN operation op ON team_op.operation_id = op.opcode
             LEFT JOIN person p ON team_op.created_by = p.cpsn_num
             {}
             ORDER BY team_op.created_at DESC
             OFFSET {} ROWS FETCH NEXT {} ROWS ONLY",
            where_clause, offset, page_size
        );

        let mut data_query = Query::new(&data_sql);
        for param in &params {
            data_query.bind(param);
        }

        let data_stream = data_query.query(&mut *client).await
            .map_err(|e| AppError::Database(format!("查询工序绑定数据失败: {}", e)))?;
        let data_rows: Vec<tiberius::Row> = data_stream.into_first_result().await
            .map_err(|e| AppError::Database(format!("获取数据结果失败: {}", e)))?;

        let items: Vec<TeamOperationResponse> = data_rows
            .into_iter()
            .map(|row| TeamOperationResponse {
                id: row.get("id").unwrap_or(0),
                team_id: row.get("team_id").unwrap_or(0),
                team_name: row.get::<&str, _>("team_name").map(|s| s.to_string()),
                operation_id: row.get::<&str, _>("operation_id").unwrap_or("").to_string(),
                description: row.get::<&str, _>("description").map(|s| s.to_string()),
                created_by: row.get::<&str, _>("created_by").map(|s| s.to_string()),
                created_by_name: row.get::<&str, _>("created_by_name").map(|s| s.to_string()),
                status: row.get::<u8, _>("status").unwrap_or(1),
                created_at: row.get("created_at").unwrap_or_default(),
                updated_at: row.get("updated_at").unwrap_or_default(),
            })
            .collect();

        let total_pages = ((total) + page_size - 1) / page_size;

        Ok(TeamOperationPageResponse {
            items,
            total,
            page,
            page_size,
            total_pages,
        })
    }

    /// 删除班组工序绑定（软删除）
    pub async fn delete_team_operation(&self, id: i64) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        let mut update_query = Query::new(
            "UPDATE TeamOperations SET status = 0, updated_at = GETDATE() WHERE id = @P1"
        );
        update_query.bind(id);

        let result = update_query.execute(&mut *client).await
            .map_err(|e| AppError::Database(format!("删除工序绑定失败: {}", e)))?;

        if result.rows_affected().first().unwrap_or(&0) == &0 {
            return Err(AppError::NotFound("班组工序绑定不存在".to_string()));
        }

        Ok(())
    }

    /// 更新班组工序绑定状态
    pub async fn update_team_operation_status(
        &self,
        id: i64,
        status: u8,
    ) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        let mut update_query = Query::new(
            "UPDATE TeamOperations SET status = @P1, updated_at = GETDATE() WHERE id = @P2"
        );
        update_query.bind(status);
        update_query.bind(id);

        let result = update_query.execute(&mut *client).await
            .map_err(|e| AppError::Database(format!("更新工序绑定状态失败: {}", e)))?;

        if result.rows_affected().first().unwrap_or(&0) == &0 {
            return Err(AppError::NotFound("班组工序绑定不存在".to_string()));
        }

        Ok(())
    }

    /// 获取班组的可用工序列表（用于指令卡创建）
    pub async fn get_team_available_operations(&self, team_id: i32) -> AppResult<Vec<TeamOperationResponse>> {
        let mut client = self.db_config.get_app_connection().await?;

        let mut query = Query::new(
            "SELECT
                team_op.id, team_op.team_id, team_op.operation_id, team_op.created_by, team_op.status,
                team_op.created_at, team_op.updated_at,
                t.TeamName as team_name,
                op.Description as description,
                p.cPsn_Name as created_by_name
             FROM TeamOperations team_op
             LEFT JOIN Teams t ON team_op.team_id = t.TeamID
             LEFT JOIN operation op ON team_op.operation_id = op.opcode
             LEFT JOIN person p ON team_op.created_by = p.cpsn_num
             WHERE team_op.team_id = @P1 AND team_op.status = 1
             ORDER BY team_op.created_at DESC"
        );
        query.bind(team_id);

        let stream = query.query(&mut *client).await
            .map_err(|e| AppError::Database(format!("查询可用工序失败: {}", e)))?;
        let rows: Vec<tiberius::Row> = stream.into_first_result().await
            .map_err(|e| AppError::Database(format!("获取可用工序结果失败: {}", e)))?;

        let operations: Vec<TeamOperationResponse> = rows
            .into_iter()
            .map(|row| TeamOperationResponse {
                id: row.get("id").unwrap_or(0),
                team_id: row.get("team_id").unwrap_or(0),
                team_name: row.get::<&str, _>("team_name").map(|s| s.to_string()),
                operation_id: row.get::<&str, _>("operation_id").unwrap_or("").to_string(),
                description: row.get::<&str, _>("description").map(|s| s.to_string()),
                created_by: row.get::<&str, _>("created_by").map(|s| s.to_string()),
                created_by_name: row.get::<&str, _>("created_by_name").map(|s| s.to_string()),
                status: row.get::<u8, _>("status").unwrap_or(1),
                created_at: row.get("created_at").unwrap_or_default(),
                updated_at: row.get("updated_at").unwrap_or_default(),
            })
            .collect();

        Ok(operations)
    }

    // ========================================
    // 综合查询方法
    // ========================================

    /// 获取班组的所有可用资源（产品、设备、工序）
    pub async fn get_team_available_resources(&self, team_id: i32) -> AppResult<TeamAvailableResourcesResponse> {
        let products = self.get_team_available_products(team_id).await?;
        let equipments = self.get_team_available_equipments(team_id).await?;
        let operations = self.get_team_available_operations(team_id).await?;

        Ok(TeamAvailableResourcesResponse {
            products,
            equipments,
            operations,
        })
    }

    /// 验证班组是否可以使用指定的产品、设备、工序
    pub async fn validate_team_resources(
        &self,
        team_id: i32,
        inventory_id: &str,
        equipment_id: &str,
        operation_id: &str,
    ) -> AppResult<bool> {
        let mut client = self.db_config.get_app_connection().await?;

        let mut validate_query = Query::new(
            "SELECT
                (SELECT COUNT(*) FROM TeamProducts WHERE team_id = @P1 AND inventory_id = @P2 AND status = 1) as product_count,
                (SELECT COUNT(*) FROM TeamEquipments WHERE team_id = @P1 AND equipment_id = @P3 AND status = 1) as equipment_count,
                (SELECT COUNT(*) FROM TeamOperations WHERE team_id = @P1 AND operation_id = @P4 AND status = 1) as operation_count"
        );
        validate_query.bind(team_id);
        validate_query.bind(inventory_id);
        validate_query.bind(equipment_id);
        validate_query.bind(operation_id);

        let stream = validate_query.query(&mut *client).await
            .map_err(|e| AppError::Database(format!("验证班组资源失败: {}", e)))?;
        let rows: Vec<tiberius::Row> = stream.into_first_result().await
            .map_err(|e| AppError::Database(format!("获取验证结果失败: {}", e)))?;

        if let Some(row) = rows.first() {
            let product_count: i32 = row.get("product_count").unwrap_or(0);
            let equipment_count: i32 = row.get("equipment_count").unwrap_or(0);
            let operation_count: i32 = row.get("operation_count").unwrap_or(0);

            Ok(product_count > 0 && equipment_count > 0 && operation_count > 0)
        } else {
            Ok(false)
        }
    }

    /// 检查用户是否有权限访问指定班组的资源
    pub async fn check_user_team_permission(&self, user_id: &str, team_id: i32) -> AppResult<bool> {
        let mut client = self.db_config.get_app_connection().await?;

        let mut query = Query::new(
            "SELECT COUNT(*) as count
             FROM TeamMembers tm
             WHERE tm.Member_psn_num = @P1 AND tm.TeamID = @P2 AND tm.IsActive = 1"
        );

        query.bind(user_id);
        query.bind(team_id);

        let stream = query.query(&mut *client).await
            .map_err(|e| AppError::Database(format!("检查用户班组权限失败: {}", e)))?;
        let rows: Vec<tiberius::Row> = stream.into_first_result().await
            .map_err(|e| AppError::Database(format!("获取权限检查结果失败: {}", e)))?;

        if let Some(row) = rows.first() {
            let count: i32 = row.get("count").unwrap_or(0);
            Ok(count > 0)
        } else {
            Ok(false)
        }
    }
}
