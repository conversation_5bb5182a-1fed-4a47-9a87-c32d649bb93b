use crate::config::DatabaseConfig;
use crate::models::*;
use crate::utils::{AppResult, AppError};
use crate::services::InstructionCardBorrowRelationService;
use tiberius::Query;
use chrono::Utc;

/// 分页请求结构
#[derive(Debug)]
pub struct PaginationRequest {
    pub page: Option<u32>,
    pub page_size: Option<u32>,
}

impl PaginationRequest {
    pub fn get_page(&self) -> u32 {
        self.page.unwrap_or(1).max(1)
    }

    pub fn get_page_size(&self) -> u32 {
        self.page_size.unwrap_or(20).min(200).max(1)
    }

    pub fn get_offset(&self) -> u32 {
        (self.get_page() - 1) * self.get_page_size()
    }

    pub fn get_total_pages(&self, total: u32) -> u32 {
        if total == 0 {
            1
        } else {
            (total + self.get_page_size() - 1) / self.get_page_size()
        }
    }
}



/// 指令卡服务
pub struct InstructionCardService {
    db_config: DatabaseConfig,
}

impl InstructionCardService {
    pub fn new(db_config: DatabaseConfig) -> Self {
        Self { db_config }
    }

    /// 生成指令卡编号
    /// 格式：IC + 日期(YYYYMMDD) + 流水号(001-999)
    async fn generate_card_number(&self) -> AppResult<String> {
        let mut client = self.db_config.get_app_connection().await?;
        
        let today = Utc::now().format("%Y%m%d").to_string();
        let prefix = format!("IC{}", today);
        
        // 查询今天已有的最大流水号
        let query_sql = "SELECT MAX(card_number) FROM InstructionCards WHERE card_number LIKE @P1";
        let mut query = Query::new(query_sql);
        query.bind(format!("{}%", prefix));
        
        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;
        
        let next_seq = if let Some(row) = rows.first() {
            if let Some(max_number) = row.get::<&str, _>(0) {
                // 提取流水号并加1
                let seq_str = &max_number[10..]; // IC20250629001 -> 001
                let seq: u32 = seq_str.parse().unwrap_or(0);
                seq + 1
            } else {
                1
            }
        } else {
            1
        };
        
        Ok(format!("{}{:03}", prefix, next_seq))
    }

    /// 创建指令卡
    pub async fn create_instruction_card(
        &self,
        request: CreateInstructionCardRequest,
        publisher_psn_num: String,
    ) -> AppResult<InstructionCardResponse> {
        let mut client = self.db_config.get_app_connection().await?;

        // 验证发布人是否为班长
        let qxpd = self.is_team_leader(&publisher_psn_num, request.team_id).await?;
        if !qxpd {
            return Err(AppError::Forbidden("只有班长才能发布指令卡".to_string()));
        }

        // 验证指定人员是否属于该班组（包括班组成员、借用人员或班长）
        if !self.is_person_available_for_team(&request.assigned_person, request.team_id).await? {
            return Err(AppError::BadRequest("指定人员不属于该班组、未被借用到该班组或不是该班组的班长".to_string()));
        }
        
        // 生成指令卡编号
        let card_number = self.generate_card_number().await?;
        
        // 插入指令卡
        let insert_sql = r#"
            INSERT INTO InstructionCards (
                card_number, inventory_id, operation_id, equipment_id, team_id, workcenter_name,
                assigned_person, publisher_psn_num, production_instruction_count,
                auto_submit, auto_leader_approve, auto_manager_approve,
                status, publish_time, created_at, updated_at
            ) VALUES (
                @P1, @P2, @P3, @P4, @P5, @P6,
                @P7, @P8, @P9,
                @P10, @P11, @P12,
                @P13, GETDATE(), GETDATE(), GETDATE()
            );
            SELECT CAST(SCOPE_IDENTITY() AS BIGINT) as id;
        "#;
        
        let mut insert_query = Query::new(insert_sql);
        insert_query.bind(&card_number);
        insert_query.bind(&request.inventory_id);
        insert_query.bind(&request.operation_id);
        insert_query.bind(&request.equipment_id);
        insert_query.bind(request.team_id);
        insert_query.bind(request.workcenter_name.as_deref());
        insert_query.bind(&request.assigned_person);
        insert_query.bind(&publisher_psn_num);
        insert_query.bind(request.production_instruction_count.unwrap_or(0));
        insert_query.bind(request.auto_submit.unwrap_or(false));
        insert_query.bind(request.auto_leader_approve.unwrap_or(false));
        insert_query.bind(request.auto_manager_approve.unwrap_or(false));
        insert_query.bind(InstructionCardStatus::Pending.to_i32());

        let insert_stream = insert_query.query(&mut *client).await?;
        let insert_rows: Vec<_> = insert_stream.into_first_result().await?;

        let card_id: i64 = insert_rows.first()
            .and_then(|row| row.get::<i64, _>(0))
            .filter(|&id| id > 0) // 确保ID有效
            .ok_or_else(|| AppError::DatabaseError("获取新创建的指令卡ID失败".to_string()))?;

        // 检查指定人员是否为借用人员，如果是则创建关联
        let relation_service = InstructionCardBorrowRelationService::new(self.db_config.clone());
        if let Ok(Some(borrow_id)) = relation_service.find_latest_borrow_for_person_in_team(
            &request.assigned_person,
            request.team_id
        ).await {
            // 创建指令卡与借用记录的关联
            let relation_request = CreateInstructionCardBorrowRelationRequest {
                instruction_card_id: card_id,
                borrow_id,
            };

            if let Err(e) = relation_service.create_relation(&relation_request).await {
                tracing::warn!(
                    "创建指令卡借用关联失败: card_id={}, borrow_id={}, error={}",
                    card_id, borrow_id, e
                );
                // 不影响主流程，只记录警告日志
            } else {
                tracing::info!(
                    "成功创建指令卡借用关联: card_id={}, borrow_id={}, assigned_person={}",
                    card_id, borrow_id, request.assigned_person
                );
            }
        }

        // 记录操作日志
        self.log_action(
            card_id,
            "publish".to_string(),
            publisher_psn_num.clone(),
            None,
            Some(InstructionCardStatus::Pending.to_i32()),
            None,
            None,
            Some("发布指令卡".to_string()),
        ).await?;
        
        // 查询并返回创建的指令卡
        self.get_instruction_card_by_id(card_id).await
    }

    /// 批量创建指令卡
    pub async fn batch_create_instruction_cards(
        &self,
        request: BatchCreateInstructionCardRequest,
        publisher_psn_num: String,
    ) -> AppResult<Vec<InstructionCardResponse>> {
        let mut results = Vec::new();
        
        for card_request in request.cards {
            let result = self.create_instruction_card(card_request, publisher_psn_num.clone()).await?;
            results.push(result);
        }
        
        Ok(results)
    }

    /// 根据ID获取指令卡详情
    pub async fn get_instruction_card_by_id(&self, id: i64) -> AppResult<InstructionCardResponse> {
        let mut client = self.db_config.get_app_connection().await?;
        
        let query_sql = r#"
            SELECT
                ic.id, ic.card_number, ic.inventory_id, ic.operation_id, ic.equipment_id,
                ic.team_id, ic.workcenter_name, ic.assigned_person, ic.publisher_psn_num,
                ic.submitter_psn_num, ic.production_instruction_count, ic.completed_count,
                ic.leader_custom_count, ic.manager_custom_count, ic.status,
                ic.publish_time, ic.submit_time, ic.leader_review_time, ic.manager_review_time,
                ic.leader_reviewer, ic.manager_reviewer, ic.leader_review_remarks,
                ic.manager_review_remarks, ic.auto_submit, ic.auto_leader_approve, ic.auto_manager_approve,
                ic.created_at, ic.updated_at,
                -- 关联数据
                inv.cinvname, inv.cInvStd,
                op.Description as operation_name, eq.ceqname as equipment_name,
                t.TeamName as team_name,
                p1.cPsn_Name as assigned_person_name, p2.cPsn_Name as publisher_name,
                p3.cPsn_Name as submitter_name, p4.cPsn_Name as leader_reviewer_name,
                p5.cPsn_Name as manager_reviewer_name
            FROM InstructionCards ic
            LEFT JOIN inventory inv ON ic.inventory_id = inv.cinvcode
            LEFT JOIN operation op ON ic.operation_id = op.opcode
            LEFT JOIN EQ_QEQDataSel eq ON ic.equipment_id = eq.ceqcode
            LEFT JOIN Teams t ON ic.team_id = t.TeamID
            LEFT JOIN person p1 ON ic.assigned_person = p1.cpsn_num
            LEFT JOIN person p2 ON ic.publisher_psn_num = p2.cpsn_num
            LEFT JOIN person p3 ON ic.submitter_psn_num = p3.cpsn_num
            LEFT JOIN person p4 ON ic.leader_reviewer = p4.cpsn_num
            LEFT JOIN person p5 ON ic.manager_reviewer = p5.cpsn_num
            WHERE ic.id = @P1
        "#;
        
        let mut query = Query::new(query_sql);
        query.bind(id);
        
        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;
        
        let row = rows.first()
            .ok_or_else(|| AppError::NotFound("指令卡不存在".to_string()))?;
        
        Ok(self.row_to_instruction_card_response(row)?)
    }

    /// 查询工作中心指令卡列表（带权限控制）
    pub async fn query_workcenter_instruction_cards(
        &self,
        user_psn_num: &str,
        request: InstructionCardQueryRequest,
    ) -> AppResult<InstructionCardPageResponse> {
        let pagination = PaginationRequest {
            page: request.page,
            page_size: request.page_size,
        };

        // 构建WHERE条件，包含工作中心权限控制
        let mut where_conditions = Vec::new();
        let mut params = Vec::new();
        let mut param_index = 1;

        // 添加工作中心权限控制：可以查看用户所负责的工作中心以及相同dep字段的工作中心下的指令卡
        where_conditions.push(format!(
            r#"ic.team_id IN (
                SELECT t.TeamID
                FROM Teams t
                INNER JOIN workcenter w ON t.Workcenter_DeptCode COLLATE SQL_Latin1_General_CP1_CI_AS = w.DeptCode COLLATE SQL_Latin1_General_CP1_CI_AS
                WHERE w.dep = (
                    SELECT w2.dep
                    FROM person p
                    INNER JOIN user_roles ur ON p.cpsn_num = ur.user_id
                    INNER JOIN roles r ON ur.role_id = r.id
                    INNER JOIN workcenter w2 ON p.cDept_num COLLATE SQL_Latin1_General_CP1_CI_AS = w2.DeptCode COLLATE SQL_Latin1_General_CP1_CI_AS
                    WHERE p.cpsn_num COLLATE SQL_Latin1_General_CP1_CI_AS = @P{} COLLATE SQL_Latin1_General_CP1_CI_AS
                    AND r.name = 'manager'
                )
            )"#,
            param_index
        ));
        params.push(("user_psn_num".to_string(), user_psn_num.to_string()));
        param_index += 1;

        // 添加其他查询条件
        if let Some(team_id) = request.team_id {
            where_conditions.push(format!("ic.team_id = @P{}", param_index));
            params.push(("team_id".to_string(), team_id.to_string()));
            param_index += 1;
        }

        if let Some(assigned_person) = &request.assigned_person {
            // 支持按姓名模糊查询：通过关联person表查询姓名
            where_conditions.push(format!("(ic.assigned_person LIKE @P{} OR p1.cPsn_Name LIKE @P{})", param_index, param_index + 1));
            params.push(("assigned_person_id".to_string(), format!("%{}%", assigned_person)));
            params.push(("assigned_person_name".to_string(), format!("%{}%", assigned_person)));
            param_index += 2;
        }

        if let Some(publisher_psn_num) = &request.publisher_psn_num {
            where_conditions.push(format!("ic.publisher_psn_num = @P{}", param_index));
            params.push(("publisher_psn_num".to_string(), publisher_psn_num.clone()));
            param_index += 1;
        }

        if let Some(status) = request.status {
            where_conditions.push(format!("ic.status = @P{}", param_index));
            params.push(("status".to_string(), status.to_string()));
            param_index += 1;
        }

        if let Some(start_time) = &request.start_time {
            where_conditions.push(format!("ic.publish_time >= @P{}", param_index));
            params.push(("start_time".to_string(), start_time.format("%Y-%m-%d %H:%M:%S").to_string()));
            param_index += 1;
        }

        if let Some(end_time) = &request.end_time {
            where_conditions.push(format!("ic.publish_time <= @P{}", param_index));
            params.push(("end_time".to_string(), end_time.format("%Y-%m-%d %H:%M:%S").to_string()));
            param_index += 1;
        }

        if let Some(inventory_id) = &request.inventory_id {
            where_conditions.push(format!("ic.inventory_id LIKE @P{}", param_index));
            params.push(("inventory_id".to_string(), format!("%{}%", inventory_id)));
            param_index += 1;
        }

        if let Some(operation_id) = &request.operation_id {
            where_conditions.push(format!("ic.operation_id LIKE @P{}", param_index));
            params.push(("operation_id".to_string(), format!("%{}%", operation_id)));
            param_index += 1;
        }

        if let Some(equipment_id) = &request.equipment_id {
            where_conditions.push(format!("ic.equipment_id LIKE @P{}", param_index));
            params.push(("equipment_id".to_string(), format!("%{}%", equipment_id)));
        }

        let where_clause = if where_conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_conditions.join(" AND "))
        };

        // 执行查询
        self.execute_instruction_card_query(where_clause, params, pagination).await
    }

    /// 执行指令卡查询的通用方法
    async fn execute_instruction_card_query(
        &self,
        where_clause: String,
        params: Vec<(String, String)>,
        pagination: PaginationRequest,
    ) -> AppResult<InstructionCardPageResponse> {
        let mut client = self.db_config.get_app_connection().await?;

        // 构建查询SQL
        let base_sql = r#"
            SELECT
                ic.id, ic.card_number, ic.inventory_id, ic.operation_id, ic.equipment_id,
                ic.team_id, ic.workcenter_name, ic.assigned_person, ic.publisher_psn_num,
                ic.submitter_psn_num, ic.production_instruction_count, ic.completed_count,
                ic.leader_custom_count, ic.manager_custom_count, ic.status,
                ic.publish_time, ic.submit_time, ic.leader_review_time, ic.manager_review_time,
                ic.leader_reviewer, ic.manager_reviewer, ic.leader_review_remarks,
                ic.manager_review_remarks, ic.auto_submit, ic.auto_leader_approve, ic.auto_manager_approve,
                ic.created_at, ic.updated_at,
                -- 关联数据
                inv.cinvname, inv.cInvStd,
                op.Description as operation_name,
                eq.ceqname as equipment_name,
                t.TeamName as team_name,
                p1.cPsn_Name as assigned_person_name,
                p2.cPsn_Name as publisher_name,
                p3.cPsn_Name as submitter_name,
                p4.cPsn_Name as leader_reviewer_name,
                p5.cPsn_Name as manager_reviewer_name
            FROM InstructionCards ic
            LEFT JOIN inventory inv ON ic.inventory_id = inv.Cinvcode
            LEFT JOIN operation op ON ic.operation_id = op.opcode
            LEFT JOIN EQ_QEQDataSel eq ON ic.equipment_id = eq.ceqcode
            LEFT JOIN Teams t ON ic.team_id = t.TeamID
            LEFT JOIN person p1 ON ic.assigned_person = p1.cpsn_num
            LEFT JOIN person p2 ON ic.publisher_psn_num = p2.cpsn_num
            LEFT JOIN person p3 ON ic.submitter_psn_num = p3.cpsn_num
            LEFT JOIN person p4 ON ic.leader_reviewer = p4.cpsn_num
            LEFT JOIN person p5 ON ic.manager_reviewer = p5.cpsn_num
        "#;

        // 计算总数 - 需要包含person表JOIN以支持姓名搜索
        let count_sql = format!(
            "SELECT COUNT(*) FROM InstructionCards ic left join person p1 on ic.assigned_person = p1.cpsn_num {}",
            where_clause
        );

        let mut count_query = tiberius::Query::new(&count_sql);
        for (_, value) in &params {
            count_query.bind(value);
        }

        let count_stream = count_query.query(&mut *client).await?;
        let count_rows: Vec<_> = count_stream.into_first_result().await?;
        let total = count_rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0) as u32;

        // 计算分页
        let offset = (pagination.page.unwrap_or(1) - 1) * pagination.page_size.unwrap_or(10);
        let limit = pagination.page_size.unwrap_or(10);

        // 构建分页查询SQL
        let query_sql = format!(
            "{} {} ORDER BY ic.created_at DESC OFFSET {} ROWS FETCH NEXT {} ROWS ONLY",
            base_sql, where_clause, offset, limit
        );

        let mut query = tiberius::Query::new(&query_sql);
        for (_, value) in &params {
            query.bind(value);
        }

        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        let items = rows.into_iter().map(|row| {
            self.row_to_instruction_card_response(&row)
        }).collect::<Result<Vec<_>, _>>()?;

        let page = pagination.page.unwrap_or(1);
        let page_size = pagination.page_size.unwrap_or(10);
        let total_pages = (total + page_size - 1) / page_size;

        Ok(InstructionCardPageResponse {
            items,
            total,
            page,
            page_size,
            total_pages,
        })
    }

    /// 查询指令卡列表
    pub async fn query_instruction_cards(
        &self,
        request: InstructionCardQueryRequest,
    ) -> AppResult<InstructionCardPageResponse> {
        let mut client = self.db_config.get_app_connection().await?;
        
        let pagination = PaginationRequest {
            page: request.page,
            page_size: request.page_size,
        };
        
        // 构建WHERE条件
        let mut where_conditions = Vec::new();
        let mut params = Vec::new();
        let mut param_index = 1;
        
        if let Some(team_id) = request.team_id {
            where_conditions.push(format!("ic.team_id = @P{}", param_index));
            params.push(("team_id".to_string(), team_id.to_string()));
            param_index += 1;
        }
        
        if let Some(assigned_person) = &request.assigned_person {
            // 支持按姓名模糊查询：通过关联person表查询姓名
            where_conditions.push(format!("(ic.assigned_person = @P{} OR p1.cPsn_Name LIKE @P{})", param_index, param_index + 1));
            params.push(("assigned_person_id".to_string(), assigned_person.clone()));
            params.push(("assigned_person_name".to_string(), format!("%{}%", assigned_person)));
            param_index += 2;
        }

        if let Some(publisher_psn_num) = &request.publisher_psn_num {
            where_conditions.push(format!("ic.publisher_psn_num = @P{}", param_index));
            params.push(("publisher_psn_num".to_string(), publisher_psn_num.clone()));
            param_index += 1;
        }
        
        if let Some(status) = request.status {
            where_conditions.push(format!("ic.status = @P{}", param_index));
            params.push(("status".to_string(), status.to_string()));
            param_index += 1;
        }
        
        if let Some(start_time) = &request.start_time {
            where_conditions.push(format!("ic.publish_time >= @P{}", param_index));
            params.push(("start_time".to_string(), start_time.format("%Y-%m-%d %H:%M:%S").to_string()));
            param_index += 1;
        }

        if let Some(end_time) = &request.end_time {
            where_conditions.push(format!("ic.publish_time <= @P{}", param_index));
            params.push(("end_time".to_string(), end_time.format("%Y-%m-%d %H:%M:%S").to_string()));
            param_index += 1;
        }
        
        if let Some(inventory_id) = &request.inventory_id {
            where_conditions.push(format!("ic.inventory_id = @P{}", param_index));
            params.push(("inventory_id".to_string(), inventory_id.clone()));
            param_index += 1;
        }
        
        if let Some(operation_id) = &request.operation_id {
            where_conditions.push(format!("ic.operation_id = @P{}", param_index));
            params.push(("operation_id".to_string(), operation_id.clone()));
            param_index += 1;
        }
        
        if let Some(equipment_id) = &request.equipment_id {
            where_conditions.push(format!("ic.equipment_id = @P{}", param_index));
            params.push(("equipment_id".to_string(), equipment_id.clone()));
            param_index += 1;
        }
        
        let where_clause = if where_conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_conditions.join(" AND "))
        };
        
        // 查询总数
        let count_sql = format!(
            "SELECT COUNT(*) FROM InstructionCards ic left join person p1 on ic.assigned_person = p1.cpsn_num {}",
            where_clause
        );
        
        let mut count_query = Query::new(&count_sql);
        self.bind_parameters(&mut count_query, &params);
        
        let count_stream = count_query.query(&mut *client).await?;
        let count_rows: Vec<_> = count_stream.into_first_result().await?;
        let total = count_rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0) as u32;
        
        // 查询数据
        let data_sql = format!(
            r#"
            SELECT
                ic.id, ic.card_number, ic.inventory_id, ic.operation_id, ic.equipment_id,
                ic.team_id, ic.workcenter_name, ic.assigned_person, ic.publisher_psn_num,
                ic.submitter_psn_num, ic.production_instruction_count, ic.completed_count,
                ic.leader_custom_count, ic.manager_custom_count, ic.status,
                ic.publish_time, ic.submit_time, ic.leader_review_time, ic.manager_review_time,
                ic.leader_reviewer, ic.manager_reviewer, ic.leader_review_remarks,
                ic.manager_review_remarks, ic.auto_submit, ic.auto_leader_approve, ic.auto_manager_approve,
                ic.created_at, ic.updated_at,
                -- 关联数据
                inv.cinvname, inv.cInvStd,
                op.Description as operation_name, eq.ceqname as equipment_name,
                t.TeamName as team_name,
                p1.cPsn_Name as assigned_person_name, p2.cPsn_Name as publisher_name,
                p3.cPsn_Name as submitter_name, p4.cPsn_Name as leader_reviewer_name,
                p5.cPsn_Name as manager_reviewer_name
            FROM InstructionCards ic
            LEFT JOIN inventory inv ON ic.inventory_id = inv.cinvcode
            LEFT JOIN operation op ON ic.operation_id = op.opcode
            LEFT JOIN EQ_QEQDataSel eq ON ic.equipment_id = eq.ceqcode
            LEFT JOIN Teams t ON ic.team_id = t.TeamID
            LEFT JOIN person p1 ON ic.assigned_person = p1.cpsn_num
            LEFT JOIN person p2 ON ic.publisher_psn_num = p2.cpsn_num
            LEFT JOIN person p3 ON ic.submitter_psn_num = p3.cpsn_num
            LEFT JOIN person p4 ON ic.leader_reviewer = p4.cpsn_num
            LEFT JOIN person p5 ON ic.manager_reviewer = p5.cpsn_num
            {}
            ORDER BY ic.publish_time DESC
            OFFSET {} ROWS FETCH NEXT {} ROWS ONLY
            "#,
            where_clause,
            pagination.get_offset(),
            pagination.get_page_size()
        );
        
        let mut data_query = Query::new(&data_sql);
        self.bind_parameters(&mut data_query, &params);
        
        let data_stream = data_query.query(&mut *client).await?;
        let data_rows: Vec<_> = data_stream.into_first_result().await?;
        
        let mut items = Vec::new();
        for row in data_rows {
            items.push(self.row_to_instruction_card_response(&row)?);
        }
        
        Ok(InstructionCardPageResponse {
            items,
            total,
            page: pagination.get_page(),
            page_size: pagination.get_page_size(),
            total_pages: pagination.get_total_pages(total),
        })
    }

    /// 记录操作日志
    async fn log_action(
        &self,
        card_id: i64,
        action_type: String,
        operator_psn_num: String,
        old_status: Option<i32>,
        new_status: Option<i32>,
        old_count: Option<i32>,
        new_count: Option<i32>,
        remarks: Option<String>,
    ) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;
        
        let insert_sql = r#"
            INSERT INTO InstructionCardAuditLogs (
                card_id, action_type, operator_psn_num, old_status, new_status,
                old_count, new_count, remarks, operation_time
            ) VALUES (
                @P1, @P2, @P3, @P4, @P5, @P6, @P7, @P8, GETDATE()
            )
        "#;
        
        let mut query = Query::new(insert_sql);
        query.bind(card_id);
        query.bind(&action_type);
        query.bind(&operator_psn_num);
        query.bind(old_status);
        query.bind(new_status);
        query.bind(old_count);
        query.bind(new_count);
        query.bind(remarks.as_deref());
        
        query.execute(&mut *client).await?;
        Ok(())
    }

    /// 绑定查询参数
    fn bind_parameters<'a>(&self, query: &mut Query<'a>, params: &'a [(String, String)]) {
        for (_, value) in params {
            query.bind(value);
        }
    }

    /// 将数据库行转换为指令卡响应DTO
    fn row_to_instruction_card_response(&self, row: &tiberius::Row) -> AppResult<InstructionCardResponse> {

        let status_value: i32 = row.get(14).unwrap_or(0);  // ic.status 是第14列
        let status = InstructionCardStatus::from_i32(status_value)
            .ok_or_else(|| AppError::DatabaseError(format!("无效的状态值: {}", status_value)))?;

        let card = InstructionCard {
            id: row.get::<i64, _>(0).unwrap_or(0),
            card_number: row.get::<&str, _>(1).unwrap_or("").to_string(),
            inventory_id: row.get::<&str, _>(2).unwrap_or("").to_string(),
            operation_id: row.get::<&str, _>(3).unwrap_or("").to_string(),
            equipment_id: row.get::<&str, _>(4).unwrap_or("").to_string(),
            team_id: row.get::<i32, _>(5).unwrap_or(0),
            workcenter_name: row.get::<&str, _>(6).map(|s| s.to_string()),
            assigned_person: row.get::<&str, _>(7).unwrap_or("").to_string(),
            publisher_psn_num: row.get::<&str, _>(8).unwrap_or("").to_string(),
            submitter_psn_num: row.get::<&str, _>(9).map(|s| s.to_string()),
            production_instruction_count: row.get::<i32, _>(10).unwrap_or(0),
            completed_count: row.get::<i32, _>(11),
            leader_custom_count: row.get::<i32, _>(12),
            manager_custom_count: row.get::<i32, _>(13),
            publish_time: match row.get::<chrono::DateTime<Utc>, _>(15) {  // ic.publish_time 是第15列
                Some(dt) => dt,
                None => return Err(AppError::DatabaseError("发布时间字段为空".to_string())),
            },
            submit_time: match row.get::<chrono::DateTime<Utc>, _>(16) {  // ic.submit_time 是第16列
                Some(dt) => Some(dt),
                None => None,
            },
            finish_time: None,  // finish_time 不在查询结果中
            status,
            leader_reviewer: row.get::<&str, _>(19).map(|s| s.to_string()),  // ic.leader_reviewer 是第19列
            leader_review_time: match row.get::<chrono::DateTime<chrono::Utc>, _>(17) {  // ic.leader_review_time 是第17列
                Some(dt) => Some(dt),
                None => None,
            },
            leader_review_remarks: row.get::<&str, _>(21).map(|s| s.to_string()),  // ic.leader_review_remarks 是第21列
            manager_reviewer: row.get::<&str, _>(20).map(|s| s.to_string()),  // ic.manager_reviewer 是第20列
            manager_review_time: match row.get::<chrono::DateTime<chrono::Utc>, _>(18) {  // ic.manager_review_time 是第18列
                Some(dt) => Some(dt),
                None => None,
            },
            manager_review_remarks: row.get::<&str, _>(22).map(|s| s.to_string()),  // ic.manager_review_remarks 是第22列
            auto_submit: row.get::<u8, _>(23).map(|v| v != 0).unwrap_or(false),  // ic.auto_submit 是第23列
            auto_leader_approve: row.get::<u8, _>(24).map(|v| v != 0).unwrap_or(false),  // ic.auto_leader_approve 是第24列
            auto_manager_approve: row.get::<u8, _>(25).map(|v| v != 0).unwrap_or(false),  // ic.auto_manager_approve 是第25列
            created_at: match row.get::<chrono::DateTime<chrono::Utc>, _>(26) {  // ic.created_at 是第26列
                Some(dt) => dt,
                None => return Err(AppError::DatabaseError("创建时间字段为空".to_string())),
            },
            updated_at: match row.get::<chrono::DateTime<chrono::Utc>, _>(27) {  // ic.updated_at 是第27列
                Some(dt) => dt,
                None => return Err(AppError::DatabaseError("更新时间字段为空".to_string())),
            },
        };

        Ok(InstructionCardResponse {
            card,
            cinvname: row.get::<&str, _>(28).map(|s| s.to_string()),  // inv.cinvname 是第28列
            cinv_std: row.get::<&str, _>(29).map(|s| s.to_string()),  // inv.cInvStd 是第29列
            operation_name: row.get::<&str, _>(30).map(|s| s.to_string()),  // op.Description 是第30列
            equipment_name: row.get::<&str, _>(31).map(|s| s.to_string()),  // eq.ceqname 是第31列
            team_name: row.get::<&str, _>(32).map(|s| s.to_string()),  // t.TeamName 是第32列
            assigned_person_name: row.get::<&str, _>(33).map(|s| s.to_string()),  // p1.cPsn_Name 是第33列
            publisher_name: row.get::<&str, _>(34).map(|s| s.to_string()),  // p2.cPsn_Name 是第34列
            submitter_name: row.get::<&str, _>(35).map(|s| s.to_string()),  // p3.cPsn_Name 是第35列
            leader_reviewer_name: row.get::<&str, _>(36).map(|s| s.to_string()),  // p4.cPsn_Name 是第36列
            manager_reviewer_name: row.get::<&str, _>(37).map(|s| s.to_string()),  // p5.cPsn_Name 是第37列
        })
    }

    /// 提交指令卡（员工填写完成数量）
    pub async fn submit_instruction_card(
        &self,
        card_id: i64,
        request: SubmitInstructionCardRequest,
        submitter_psn_num: String,
    ) -> AppResult<InstructionCardResponse> {
        let mut client = self.db_config.get_app_connection().await?;

        // 验证权限
        let _card = self.verify_card_permission(card_id, &submitter_psn_num, "submit").await?;

        // 更新指令卡状态
        let update_sql = r#"
            UPDATE InstructionCards
            SET completed_count = @P1, submitter_psn_num = @P2, submit_time = GETDATE(),
                status = @P3,leader_custom_count=@P4, updated_at = GETDATE()
            WHERE id = @P5
        "#;

        let mut update_query = Query::new(update_sql);
        update_query.bind(request.completed_count);
        update_query.bind(&submitter_psn_num);
        update_query.bind(InstructionCardStatus::Submitted.to_i32());
        update_query.bind(request.completed_count);
        update_query.bind(card_id);

        update_query.execute(&mut *client).await?;

        // 记录操作日志
        self.log_action(
            card_id,
            "submit".to_string(),
            submitter_psn_num,
            Some(InstructionCardStatus::Pending.to_i32()),
            Some(InstructionCardStatus::Submitted.to_i32()),
            None,
            Some(request.completed_count),
            Some("提交指令卡".to_string()),
        ).await?;

        // 返回更新后的指令卡
        self.get_instruction_card_by_id(card_id).await
    }

    /// 班长审核指令卡
    pub async fn leader_review_instruction_card(
        &self,
        card_id: i64,
        request: ReviewInstructionCardRequest,
        reviewer_psn_num: String,
    ) -> AppResult<InstructionCardResponse> {
        let mut client = self.db_config.get_app_connection().await?;

        // 验证权限
        let _card = self.verify_card_permission(card_id, &reviewer_psn_num, "leader_review").await?;

        // 更新指令卡状态 - 只有当manager_custom_count为NULL或0时才赋值
        let update_sql = r#"
            UPDATE InstructionCards
            SET leader_reviewer = @P1, leader_review_time = GETDATE(),
                leader_review_remarks = @P2, leader_custom_count = @P3,
                status = @P4,
                manager_custom_count = CASE
                    WHEN manager_custom_count IS NULL OR manager_custom_count = 0
                    THEN @P5
                    ELSE manager_custom_count
                END,
                updated_at = GETDATE()
            WHERE id = @P6
        "#;

        let mut update_query = Query::new(update_sql);
        update_query.bind(&reviewer_psn_num);
        update_query.bind(request.remarks.as_deref());
        update_query.bind(request.custom_count);
        update_query.bind(InstructionCardStatus::LeaderApproved.to_i32());
        update_query.bind(request.custom_count);
        update_query.bind(card_id);

        update_query.execute(&mut *client).await?;

        // 记录操作日志
        self.log_action(
            card_id,
            "leader_review".to_string(),
            reviewer_psn_num,
            Some(InstructionCardStatus::Submitted.to_i32()),
            Some(InstructionCardStatus::LeaderApproved.to_i32()),
            request.custom_count,
            request.custom_count,
            request.remarks,
        ).await?;

        // 返回更新后的指令卡
        self.get_instruction_card_by_id(card_id).await
    }

    /// 负责人审核指令卡
    pub async fn manager_review_instruction_card(
        &self,
        card_id: i64,
        request: ReviewInstructionCardRequest,
        reviewer_psn_num: String,
    ) -> AppResult<InstructionCardResponse> {
        let mut client = self.db_config.get_app_connection().await?;

        // 验证权限
        let _card = self.verify_card_permission(card_id, &reviewer_psn_num, "manager_review").await?;

        // 更新指令卡状态
        let update_sql = r#"
            UPDATE InstructionCards
            SET manager_reviewer = @P1, manager_review_time = GETDATE(),
                manager_review_remarks = @P2, manager_custom_count = @P3,
                status = @P4, finish_time = GETDATE(), updated_at = GETDATE()
            WHERE id = @P5
        "#;

        let mut update_query = Query::new(update_sql);
        update_query.bind(&reviewer_psn_num);
        update_query.bind(request.remarks.as_deref());
        update_query.bind(request.custom_count);
        update_query.bind(InstructionCardStatus::Completed.to_i32());
        update_query.bind(card_id);

        update_query.execute(&mut *client).await?;

        // 记录操作日志
        self.log_action(
            card_id,
            "manager_review".to_string(),
            reviewer_psn_num,
            Some(InstructionCardStatus::LeaderApproved.to_i32()),
            Some(InstructionCardStatus::Completed.to_i32()),
            request.custom_count,
            request.custom_count,
            request.remarks,
        ).await?;

        // 返回更新后的指令卡
        self.get_instruction_card_by_id(card_id).await
    }

    /// 班长修改班产数量（不审核通过）
    pub async fn update_leader_production_count(
        &self,
        card_id: i64,
        request: UpdateProductionCountRequest,
        operator_psn_num: String,
    ) -> AppResult<InstructionCardResponse> {
        let mut client = self.db_config.get_app_connection().await?;

        // 检查指令卡状态
        let card = self.get_instruction_card_by_id(card_id).await?;
        if card.card.status != InstructionCardStatus::Submitted {
            return Err(AppError::BadRequest("只有已提交状态的指令卡才能修改班长班产数量".to_string()));
        }

        let old_count = card.card.leader_custom_count;

        // 更新班长自定义数量
        let update_sql = r#"
            UPDATE InstructionCards
            SET leader_custom_count = @P1, updated_at = GETDATE()
            WHERE id = @P2
        "#;

        let mut update_query = Query::new(update_sql);
        update_query.bind(request.count);
        update_query.bind(card_id);

        update_query.execute(&mut *client).await?;

        // 记录操作日志
        self.log_action(
            card_id,
            "modify_leader_count".to_string(),
            operator_psn_num,
            None,
            None,
            old_count,
            Some(request.count),
            Some("班长修改班产数量".to_string()),
        ).await?;

        // 返回更新后的指令卡
        self.get_instruction_card_by_id(card_id).await
    }

    /// 负责人修改班产数量（不审核通过）
    pub async fn update_manager_production_count(
        &self,
        card_id: i64,
        request: UpdateProductionCountRequest,
        operator_psn_num: String,
    ) -> AppResult<InstructionCardResponse> {
        let mut client = self.db_config.get_app_connection().await?;

        // 检查指令卡状态
        let card = self.get_instruction_card_by_id(card_id).await?;
        if card.card.status != InstructionCardStatus::LeaderApproved {
            return Err(AppError::BadRequest("只有班长审核通过状态的指令卡才能修改负责人班产数量".to_string()));
        }

        let old_count = card.card.manager_custom_count;

        // 更新负责人自定义数量
        let update_sql = r#"
            UPDATE InstructionCards
            SET manager_custom_count = @P1, updated_at = GETDATE()
            WHERE id = @P2
        "#;

        let mut update_query = Query::new(update_sql);
        update_query.bind(request.count);
        update_query.bind(card_id);

        update_query.execute(&mut *client).await?;

        // 记录操作日志
        self.log_action(
            card_id,
            "modify_manager_count".to_string(),
            operator_psn_num,
            None,
            None,
            old_count,
            Some(request.count),
            Some("负责人修改班产数量".to_string()),
        ).await?;

        // 返回更新后的指令卡
        self.get_instruction_card_by_id(card_id).await
    }

    /// 删除指令卡（仅待审核状态）
    pub async fn delete_instruction_card(
        &self,
        card_id: i64,
        operator_psn_num: String,
    ) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        // 验证权限
        let card = self.verify_card_permission(card_id, &operator_psn_num, "delete").await?;

        // 记录删除日志
        self.log_action(
            card_id,
            "delete".to_string(),
            operator_psn_num,
            Some(card.card.status.to_i32()),
            None,
            None,
            None,
            Some("删除指令卡".to_string()),
        ).await?;

        // 删除指令卡
        let delete_sql = "DELETE FROM InstructionCards WHERE id = @P1";
        let mut delete_query = Query::new(delete_sql);
        delete_query.bind(card_id);
        delete_query.execute(&mut *client).await?;

        Ok(())
    }

    /// 获取指令卡统计信息
    pub async fn get_instruction_card_statistics(
        &self,
        request: InstructionCardQueryRequest,
    ) -> AppResult<InstructionCardStatistics> {
        let mut client = self.db_config.get_app_connection().await?;

        // 构建WHERE条件
        let mut where_conditions = Vec::new();
        let mut params = Vec::new();
        let mut param_index = 1;

        if let Some(team_id) = request.team_id {
            where_conditions.push(format!("team_id = @P{}", param_index));
            params.push(("team_id".to_string(), team_id.to_string()));
            param_index += 1;
        }

        if let Some(assigned_person) = &request.assigned_person {
            where_conditions.push(format!("assigned_person = @P{}", param_index));
            params.push(("assigned_person".to_string(), assigned_person.clone()));
            param_index += 1;
        }

        if let Some(start_time) = &request.start_time {
            where_conditions.push(format!("publish_time >= @P{}", param_index));
            params.push(("start_time".to_string(), start_time.format("%Y-%m-%d %H:%M:%S").to_string()));
            param_index += 1;
        }

        if let Some(end_time) = &request.end_time {
            where_conditions.push(format!("publish_time <= @P{}", param_index));
            params.push(("end_time".to_string(), end_time.format("%Y-%m-%d %H:%M:%S").to_string()));
        }

        let where_clause = if where_conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_conditions.join(" AND "))
        };

        // 查询统计数据
        let stats_sql = format!(
            r#"
            SELECT
                COUNT(*) as total_cards,
                SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as pending_count,
                SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as submitted_count,
                SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as leader_approved_count,
                SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed_count
            FROM InstructionCards
            {}
            "#,
            where_clause
        );

        let mut stats_query = Query::new(&stats_sql);
        self.bind_parameters(&mut stats_query, &params);

        let stats_stream = stats_query.query(&mut *client).await?;
        let stats_rows: Vec<_> = stats_stream.into_first_result().await?;

        let stats_row = stats_rows.first()
            .ok_or_else(|| AppError::DatabaseError("统计查询失败".to_string()))?;

        Ok(InstructionCardStatistics {
            total_cards: stats_row.get::<i32, _>(0).unwrap_or(0),
            pending_count: stats_row.get::<i32, _>(1).unwrap_or(0),
            submitted_count: stats_row.get::<i32, _>(2).unwrap_or(0),
            leader_approved_count: stats_row.get::<i32, _>(3).unwrap_or(0),
            completed_count: stats_row.get::<i32, _>(4).unwrap_or(0),
        })
    }

    /// 获取指令卡审核日志
    pub async fn get_instruction_card_audit_logs(
        &self,
        card_id: i64,
    ) -> AppResult<Vec<InstructionCardAuditLog>> {
        let mut client = self.db_config.get_app_connection().await?;

        let query_sql = r#"
            SELECT
                id, card_id, action_type, operator_psn_num, old_status, new_status,
                old_count, new_count, remarks, operation_time
            FROM InstructionCardAuditLogs
            WHERE card_id = @P1
            ORDER BY operation_time DESC
        "#;

        let mut query = Query::new(query_sql);
        query.bind(card_id);

        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        let mut logs = Vec::new();
        for row in rows {
            logs.push(InstructionCardAuditLog {
                id: row.get::<i64, _>(0).unwrap_or(0),
                card_id: row.get::<i64, _>(1).unwrap_or(0),
                action_type: row.get::<&str, _>(2).unwrap_or("").to_string(),
                operator_psn_num: row.get::<&str, _>(3).unwrap_or("").to_string(),
                old_status: row.get::<i32, _>(4),
                new_status: row.get::<i32, _>(5),
                old_count: row.get::<i32, _>(6),
                new_count: row.get::<i32, _>(7),
                remarks: row.get::<&str, _>(8).map(|s| s.to_string()),
                operation_time: match row.get::<chrono::DateTime<Utc>, _>(9) {
                    Some(dt) => dt,
                    None => return Err(AppError::DatabaseError("操作时间字段为空".to_string())),
                },
            });
        }

        Ok(logs)
    }

    /// 查询员工的指令卡（包括原班组和借用班组）
    pub async fn query_my_instruction_cards(
        &self,
        assigned_person: String,
        request: InstructionCardQueryRequest,
    ) -> AppResult<InstructionCardPageResponse> {
        let mut client = self.db_config.get_app_connection().await?;

        let pagination = PaginationRequest {
            page: request.page,
            page_size: request.page_size,
        };

        // 构建WHERE条件 - 包含原班组和借用班组的指令卡
        let mut where_conditions = Vec::new();
        let mut params = Vec::new();
        let mut param_index = 1;

        // 指定人员条件
        where_conditions.push(format!("ic.assigned_person = @P{}", param_index));
        params.push(("assigned_person".to_string(), assigned_person.clone()));
        param_index += 1;

        // 班组条件：原班组 + 借用班组 + 担任班长的班组
        where_conditions.push(format!(
            r#"(
                ic.team_id IN (
                    SELECT TeamID FROM TeamMembers
                    WHERE Member_psn_num = @P{} AND IsActive = 1
                )
                OR
                ic.team_id IN (
                    SELECT DISTINCT BorrowTeamID
                    FROM TeamMemberBorrows
                    WHERE Member_psn_num = @P{}
                    AND BorrowStatus = 1
                )
                OR
                ic.team_id IN (
                    SELECT TeamID FROM TeamLeaders
                    WHERE Leader_psn_num = @P{} AND IsActive = 1
                )
            )"#,
            param_index, param_index, param_index
        ));
        params.push(("member_psn_num".to_string(), assigned_person));
        param_index += 1;

        // 其他过滤条件
        if let Some(status) = request.status {
            where_conditions.push(format!("ic.status = @P{}", param_index));
            params.push(("status".to_string(), status.to_string()));
            param_index += 1;
        }

        if let Some(start_time) = &request.start_time {
            where_conditions.push(format!("ic.publish_time >= @P{}", param_index));
            params.push(("start_time".to_string(), start_time.format("%Y-%m-%d %H:%M:%S").to_string()));
            param_index += 1;
        }

        if let Some(end_time) = &request.end_time {
            where_conditions.push(format!("ic.publish_time <= @P{}", param_index));
            params.push(("end_time".to_string(), end_time.format("%Y-%m-%d %H:%M:%S").to_string()));
        }

        let where_clause = format!("WHERE {}", where_conditions.join(" AND "));

        // 查询总数 - 需要包含person表JOIN以支持姓名搜索
        let count_sql = format!(
            "SELECT COUNT(*) FROM InstructionCards ic left join person p1 on ic.assigned_person = p1.cpsn_num {}",
            where_clause
        );

        let mut count_query = Query::new(&count_sql);
        self.bind_parameters(&mut count_query, &params);

        let count_stream = count_query.query(&mut *client).await?;
        let count_rows: Vec<_> = count_stream.into_first_result().await?;
        let total = count_rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0) as u32;

        // 查询数据
        let data_sql = format!(
            r#"
            SELECT
                ic.id, ic.card_number, ic.inventory_id, ic.operation_id, ic.equipment_id,
                ic.team_id, ic.workcenter_name, ic.assigned_person, ic.publisher_psn_num,
                ic.submitter_psn_num, ic.production_instruction_count, ic.completed_count,
                ic.leader_custom_count, ic.manager_custom_count, ic.status,
                ic.publish_time, ic.submit_time, ic.leader_review_time, ic.manager_review_time,
                ic.leader_reviewer, ic.manager_reviewer, ic.leader_review_remarks,
                ic.manager_review_remarks, ic.auto_submit, ic.auto_leader_approve, ic.auto_manager_approve,
                ic.created_at, ic.updated_at,
                -- 关联数据
                inv.cinvname, inv.cInvStd,
                op.Description as operation_name, eq.ceqname as equipment_name,
                t.TeamName as team_name,
                p1.cPsn_Name as assigned_person_name, p2.cPsn_Name as publisher_name,
                p3.cPsn_Name as submitter_name, p4.cPsn_Name as leader_reviewer_name,
                p5.cPsn_Name as manager_reviewer_name
            FROM InstructionCards ic
            LEFT JOIN inventory inv ON ic.inventory_id = inv.cinvcode
            LEFT JOIN operation op ON ic.operation_id = op.opcode
            LEFT JOIN EQ_QEQDataSel eq ON ic.equipment_id = eq.ceqcode
            LEFT JOIN Teams t ON ic.team_id = t.TeamID
            LEFT JOIN person p1 ON ic.assigned_person COLLATE SQL_Latin1_General_CP1_CI_AS = p1.cpsn_num COLLATE SQL_Latin1_General_CP1_CI_AS
            LEFT JOIN person p2 ON ic.publisher_psn_num COLLATE SQL_Latin1_General_CP1_CI_AS = p2.cpsn_num COLLATE SQL_Latin1_General_CP1_CI_AS
            LEFT JOIN person p3 ON ic.submitter_psn_num COLLATE SQL_Latin1_General_CP1_CI_AS = p3.cpsn_num COLLATE SQL_Latin1_General_CP1_CI_AS
            LEFT JOIN person p4 ON ic.leader_reviewer COLLATE SQL_Latin1_General_CP1_CI_AS = p4.cpsn_num COLLATE SQL_Latin1_General_CP1_CI_AS
            LEFT JOIN person p5 ON ic.manager_reviewer COLLATE SQL_Latin1_General_CP1_CI_AS = p5.cpsn_num COLLATE SQL_Latin1_General_CP1_CI_AS
            {}
            ORDER BY ic.publish_time DESC
            OFFSET {} ROWS FETCH NEXT {} ROWS ONLY
            "#,
            where_clause,
            pagination.get_offset(),
            pagination.get_page_size()
        );

        let mut data_query = Query::new(&data_sql);
        self.bind_parameters(&mut data_query, &params);

        let data_stream = data_query.query(&mut *client).await?;
        let data_rows: Vec<_> = data_stream.into_first_result().await?;

        let mut items = Vec::new();
        for row in data_rows {
            items.push(self.row_to_instruction_card_response(&row)?);
        }

        Ok(InstructionCardPageResponse {
            items,
            total,
            page: pagination.get_page(),
            page_size: pagination.get_page_size(),
            total_pages: pagination.get_total_pages(total),
        })
    }

    /// 验证用户是否有权限操作指令卡
    pub async fn verify_card_permission(
        &self,
        card_id: i64,
        user_psn_num: &str,
        operation: &str, // "submit", "leader_review", "manager_review", "delete"
    ) -> AppResult<InstructionCardResponse> {
        let card = self.get_instruction_card_by_id(card_id).await?;

        match operation {
            "submit" => {
                // 只有指定人员才能提交
                if card.card.assigned_person != user_psn_num {
                    return Err(AppError::Forbidden("只有指定人员才能提交指令卡".to_string()));
                }
                // 只有待处理状态才能提交
                if card.card.status != InstructionCardStatus::Pending {
                    return Err(AppError::BadRequest("只有待处理状态的指令卡才能提交".to_string()));
                }
            },
            "leader_review" => {
                // 验证是否为班长
                if !self.is_team_leader(user_psn_num, card.card.team_id).await? {
                    return Err(AppError::Forbidden("只有班长才能审核指令卡".to_string()));
                }
                // 只有已提交状态才能班长审核
                if card.card.status != InstructionCardStatus::Submitted {
                    return Err(AppError::BadRequest("只有已提交状态的指令卡才能进行班长审核".to_string()));
                }
            },
            "manager_review" => {
                // 验证是否为负责人
                if !self.is_workcenter_manager(user_psn_num, card.card.team_id).await? {
                    return Err(AppError::Forbidden("只有负责人才能审核指令卡".to_string()));
                }
                // 只有班长审核通过状态才能负责人审核
                if card.card.status != InstructionCardStatus::LeaderApproved {
                    return Err(AppError::BadRequest("只有班长审核通过状态的指令卡才能进行负责人审核".to_string()));
                }
            },
            "delete" => {
                // 验证是否为班长
                if !self.is_team_leader(user_psn_num, card.card.team_id).await? {
                    return Err(AppError::Forbidden("只有班长才能删除指令卡".to_string()));
                }
                // 只有待处理状态才能删除
                if card.card.status != InstructionCardStatus::Pending {
                    return Err(AppError::BadRequest("只有待处理状态的指令卡才能删除".to_string()));
                }
            },
            _ => {
                return Err(AppError::BadRequest("无效的操作类型".to_string()));
            }
        }

        Ok(card)
    }

    /// 检查用户是否为班组长
    async fn is_team_leader(&self, user_psn_num: &str, team_id: i32) -> AppResult<bool> {
        let mut client = self.db_config.get_app_connection().await?;

        let query_sql = r#"
            SELECT COUNT(*) FROM TeamLeaders
            WHERE Leader_psn_num = @P1
            AND TeamID = @P2 AND IsActive = 1
        "#;

        let mut query = Query::new(query_sql);
        query.bind(user_psn_num);
        query.bind(team_id);

        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        let count = rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0);

        Ok(count > 0)
    }

    /// 检查用户是否为工作中心负责人
    async fn is_workcenter_manager(&self, user_psn_num: &str, team_id: i32) -> AppResult<bool> {
        let mut client = self.db_config.get_app_connection().await?;

        // 检查用户是否有manager角色且其部门与班组的工作中心具有相同的dep字段
        let query_sql = r#"
            SELECT COUNT(*) FROM person p
            INNER JOIN user_roles ur ON p.cpsn_num = ur.user_id
            INNER JOIN roles r ON ur.role_id = r.id
            INNER JOIN workcenter w1 ON p.cDept_num COLLATE SQL_Latin1_General_CP1_CI_AS = w1.DeptCode COLLATE SQL_Latin1_General_CP1_CI_AS
            INNER JOIN Teams t ON t.TeamID = @P2
            INNER JOIN workcenter w2 ON t.Workcenter_DeptCode COLLATE SQL_Latin1_General_CP1_CI_AS = w2.DeptCode COLLATE SQL_Latin1_General_CP1_CI_AS
            WHERE p.cpsn_num = @P1 AND r.name = 'manager'
            AND w1.dep = w2.dep 
        "#;

        let mut query = Query::new(query_sql);
        query.bind(user_psn_num);
        query.bind(team_id);

        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        let count = rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0);

        Ok(count > 0)
    }

    /// 检查人员是否可用于指定班组（原班组成员、有效借用人员或班长）
    async fn is_person_available_for_team(&self, person_psn_num: &str, team_id: i32) -> AppResult<bool> {
        let mut client = self.db_config.get_app_connection().await?;

        let query_sql = r#"
            SELECT COUNT(*) FROM (
                -- 原班组成员
                SELECT 1 as member_count FROM TeamMembers
                WHERE Member_psn_num COLLATE SQL_Latin1_General_CP1_CI_AS = @P1 COLLATE SQL_Latin1_General_CP1_CI_AS
                AND TeamID = @P2 AND IsActive = 1
                UNION
                -- 当前有效的借用人员
                SELECT 1 as member_count FROM TeamMemberBorrows
                WHERE Member_psn_num COLLATE SQL_Latin1_General_CP1_CI_AS = @P1 COLLATE SQL_Latin1_General_CP1_CI_AS
                AND BorrowTeamID = @P2
                AND BorrowStatus = 1
                AND GETDATE() BETWEEN StartDate AND EndDate
                UNION
                -- 班长（支持班长给同班级的其他班长指派指令卡）
                SELECT 1 as member_count FROM TeamLeaders
                WHERE Leader_psn_num COLLATE SQL_Latin1_General_CP1_CI_AS = @P1 COLLATE SQL_Latin1_General_CP1_CI_AS
                AND TeamID = @P2 AND IsActive = 1
            ) AS available_members
        "#;

        let mut query = Query::new(query_sql);
        query.bind(person_psn_num);
        query.bind(team_id);

        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        let count = rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0);

        Ok(count > 0)
    }

    /// 获取班组可选择的人员列表（原班组成员 + 当前有效借用人员 + 班长）
    pub async fn get_available_team_members(&self, team_id: i32) -> AppResult<Vec<TeamMemberInfo>> {
        let mut client = self.db_config.get_app_connection().await?;

        let query_sql = r#"
            SELECT DISTINCT p.cpsn_num, p.cPsn_Name,
                   CASE
                       WHEN tl.Leader_psn_num IS NOT NULL THEN '班长'
                       WHEN tm.Member_psn_num IS NOT NULL THEN '原班组成员'
                       ELSE '借用人员'
                   END as member_type
            FROM person p
            WHERE p.cpsn_num COLLATE SQL_Latin1_General_CP1_CI_AS IN (
                -- 原班组成员
                SELECT Member_psn_num COLLATE SQL_Latin1_General_CP1_CI_AS FROM TeamMembers
                WHERE TeamID = @P1 AND IsActive = 1
                UNION
                -- 当前有效的借用人员
                SELECT Member_psn_num COLLATE SQL_Latin1_General_CP1_CI_AS FROM TeamMemberBorrows
                WHERE BorrowTeamID = @P1
                AND BorrowStatus = 1
                AND GETDATE() BETWEEN StartDate AND EndDate
                UNION
                -- 班长
                SELECT Leader_psn_num COLLATE SQL_Latin1_General_CP1_CI_AS FROM TeamLeaders
                WHERE TeamID = @P1 AND IsActive = 1
            )
            LEFT JOIN TeamMembers tm ON p.cpsn_num COLLATE SQL_Latin1_General_CP1_CI_AS = tm.Member_psn_num COLLATE SQL_Latin1_General_CP1_CI_AS
            AND tm.TeamID = @P1 AND tm.IsActive = 1
            LEFT JOIN TeamLeaders tl ON p.cpsn_num COLLATE SQL_Latin1_General_CP1_CI_AS = tl.Leader_psn_num COLLATE SQL_Latin1_General_CP1_CI_AS
            AND tl.TeamID = @P1 AND tl.IsActive = 1
            ORDER BY
                CASE member_type
                    WHEN '班长' THEN 1
                    WHEN '原班组成员' THEN 2
                    ELSE 3
                END, p.cPsn_Name
        "#;

        let mut query = Query::new(query_sql);
        query.bind(team_id);

        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        let mut members = Vec::new();
        for row in rows {
            members.push(TeamMemberInfo {
                psn_num: row.get::<&str, _>(0).unwrap_or("").to_string(),
                name: row.get::<&str, _>(1).unwrap_or("").to_string(),
                member_type: row.get::<&str, _>(2).unwrap_or("").to_string(),
            });
        }

        Ok(members)
    }

    /// 批量班长审核指令卡
    pub async fn batch_leader_review_instruction_cards(
        &self,
        request: &crate::models::instruction_card::BatchReviewInstructionCardRequest,
        reviewer_psn_num: &str,
    ) -> AppResult<(u32, u32)> { // 返回 (成功数量, 失败数量)
        let mut client = self.db_config.get_app_connection().await?;
        let mut success_count = 0u32;
        let mut failed_count = 0u32;

        for card_id in &request.card_ids {
            // 检查指令卡状态
            let check_sql = r#"
                SELECT status, team_id, assigned_person, production_instruction_count,leader_custom_count
                FROM InstructionCards
                WHERE id = @P1
            "#;

            let mut check_query = Query::new(check_sql);
            check_query.bind(*card_id);

            let check_result = check_query.query(&mut *client).await;
            if let Err(_) = check_result {
                failed_count += 1;
                continue;
            }

            let check_rows: Vec<_> = check_result.unwrap().into_first_result().await.unwrap_or_default();
            if check_rows.is_empty() {
                failed_count += 1;
                continue;
            }

            let row = &check_rows[0];
            let status: i32 = row.get(0).unwrap_or(0);
            let team_id: i32 = row.get(1).unwrap_or(0);
            let original_count: i32 = row.get(3).unwrap_or(0);

            // 检查状态是否为已提交(1)
            if status != InstructionCardStatus::Submitted.to_i32() {
                failed_count += 1;
                continue;
            }

            // 检查审核权限：必须是该班组的班长
            if !self.is_team_leader(reviewer_psn_num, team_id).await.unwrap_or(false) {
                failed_count += 1;
                continue;
            }

            // 执行审核 - 只有当manager_custom_count为NULL或0时才赋值leader_custom_count
            let update_sql = r#"
                UPDATE InstructionCards
                SET status = @P1,
                    leader_reviewer = @P2,
                    leader_review_time = GETDATE(),
                    leader_review_remarks = @P3,
                    manager_custom_count = CASE
                        WHEN manager_custom_count IS NULL OR manager_custom_count = 0
                        THEN leader_custom_count
                        ELSE manager_custom_count
                    END,
                    updated_at = GETDATE()
                WHERE id = @P4
            "#;

            let mut update_query = Query::new(update_sql);
            update_query.bind(InstructionCardStatus::LeaderApproved.to_i32());
            update_query.bind(reviewer_psn_num);
            update_query.bind(request.remarks.as_deref());
            update_query.bind(*card_id);

            match update_query.execute(&mut *client).await {
                Ok(_) => {
                    // 记录操作日志
                    let _ = self.log_action(
                        *card_id,
                        "batch_leader_review".to_string(),
                        reviewer_psn_num.to_string(),
                        Some(InstructionCardStatus::Submitted.to_i32()),
                        Some(InstructionCardStatus::LeaderApproved.to_i32()),
                        Some(original_count),
                        Some(0),
                        Some("批量班长审核".to_string()),
                    ).await;
                    success_count += 1;
                },
                Err(_) => failed_count += 1,
            }
        }

        Ok((success_count, failed_count))
    }

    /// 批量负责人审核指令卡
    pub async fn batch_manager_review_instruction_cards(
        &self,
        request: &crate::models::instruction_card::BatchReviewInstructionCardRequest,
        reviewer_psn_num: &str,
    ) -> AppResult<(u32, u32)> { // 返回 (成功数量, 失败数量)
        let mut client = self.db_config.get_app_connection().await?;
        let mut success_count = 0u32;
        let mut failed_count = 0u32;

        for card_id in &request.card_ids {
            // 检查指令卡状态
            let check_sql = r#"
                SELECT status, team_id, assigned_person, leader_custom_count, production_instruction_count
                FROM InstructionCards
                WHERE id = @P1
            "#;

            let mut check_query = Query::new(check_sql);
            check_query.bind(*card_id);

            let check_result = check_query.query(&mut *client).await;
            if let Err(_) = check_result {
                failed_count += 1;
                continue;
            }

            let check_rows: Vec<_> = check_result.unwrap().into_first_result().await.unwrap_or_default();
            if check_rows.is_empty() {
                failed_count += 1;
                continue;
            }

            let row = &check_rows[0];
            let status: i32 = row.get(0).unwrap_or(0);
            let team_id: i32 = row.get(1).unwrap_or(0);
            let leader_custom_count: Option<i32> = row.get(3);

            // 检查状态是否为班长已审核(2)
            if status != InstructionCardStatus::LeaderApproved.to_i32() {
                failed_count += 1;
                continue;
            }

            // 检查审核权限：必须是该班组所在工作中心的负责人
            if !self.is_workcenter_manager(reviewer_psn_num, team_id).await.unwrap_or(false) {
                failed_count += 1;
                continue;
            }

            // 执行审核 - 不传递默认是0
            // let custom_count = request.custom_count.unwrap_or(0);
            let update_sql = r#"
                UPDATE InstructionCards
                SET status = @P1,
                    manager_reviewer = @P2,
                    manager_review_time = GETDATE(),
                    manager_review_remarks = @P3,
                    finish_time = GETDATE(),
                    updated_at = GETDATE()
                WHERE id = @P4
            "#;

            let mut update_query = Query::new(update_sql);
            update_query.bind(InstructionCardStatus::Completed.to_i32());
            update_query.bind(reviewer_psn_num);
            update_query.bind(request.remarks.as_deref());
            update_query.bind(*card_id);

            match update_query.execute(&mut *client).await {
                Ok(_) => {
                    // 记录操作日志
                    let _ = self.log_action(
                        *card_id,
                        "batch_manager_review".to_string(),
                        reviewer_psn_num.to_string(),
                        Some(InstructionCardStatus::LeaderApproved.to_i32()),
                        Some(InstructionCardStatus::Completed.to_i32()),
                        leader_custom_count,
                        Some(0),
                        Some("批量负责人审核".to_string()),
                    ).await;
                    success_count += 1;
                },
                Err(_) => failed_count += 1,
            }
        }

        Ok((success_count, failed_count))
    }

    /// 班长更新指令卡的leader_custom_count字段
    pub async fn update_leader_custom_count(
        &self,
        card_id: i64,
        count: i32,
        user_psn_num: &str,
        user_role: Option<&str>,
    ) -> AppResult<()> {
        // 权限检查：只有班长或管理员可以更新
        if user_role != Some("team_leader") && user_role != Some("admin") {
            return Err(AppError::Business("权限不足，只有班长才能更新班长自定义数量".to_string()));
        }

        // 参数验证
        if count < 0 || count > 10000 {
            return Err(AppError::Validation("数量必须在0-10000之间".to_string()));
        }

        let mut client = self.db_config.get_app_connection().await?;

        // 检查指令卡是否存在
        let check_sql = r#"
            SELECT id, status, team_id
            FROM InstructionCards
            WHERE id = @P1
        "#;

        let mut check_query = Query::new(check_sql);
        check_query.bind(card_id);

        let check_stream = check_query.query(&mut client).await?;
        let check_rows: Vec<_> = check_stream.into_first_result().await?;

        let card_row = check_rows.first()
            .ok_or_else(|| AppError::Business("指令卡不存在".to_string()))?;

        let team_id: i32 = card_row.get(2).unwrap_or(0);

        // 如果不是管理员，检查班组权限
        if user_role != Some("admin") {
            let user_team_id = self.get_user_team_id(user_psn_num).await?;
            if user_team_id != Some(team_id) {
                return Err(AppError::Business("只能更新自己班组的指令卡".to_string()));
            }
        }

        // 更新leader_custom_count字段
        let update_sql = r#"
            UPDATE InstructionCards
            SET leader_custom_count = @P1,
                updated_at = GETDATE()
            WHERE id = @P2
        "#;

        let mut update_query = Query::new(update_sql);
        update_query.bind(count);
        update_query.bind(card_id);

        update_query.execute(&mut client).await?;

        tracing::info!(
            "班长自定义数量更新成功: 指令卡ID={}, 数量={}, 操作人={}",
            card_id, count, user_psn_num
        );

        Ok(())
    }

    /// 主管更新指令卡的manager_custom_count字段
    pub async fn update_manager_custom_count(
        &self,
        card_id: i64,
        count: i32,
        user_psn_num: &str,
        user_role: Option<&str>,
    ) -> AppResult<()> {
        // 权限检查：只有主管或管理员可以更新
        if user_role != Some("manager") && user_role != Some("admin") {
            return Err(AppError::Business("权限不足，只有主管才能更新主管自定义数量".to_string()));
        }

        // 参数验证
        if count < 0 || count > 10000 {
            return Err(AppError::Validation("数量必须在0-10000之间".to_string()));
        }

        let mut client = self.db_config.get_app_connection().await?;

        // 检查指令卡是否存在
        let check_sql = r#"
            SELECT ic.id, ic.status, ic.team_id, t.Workcenter_DeptCode
            FROM InstructionCards ic
            LEFT JOIN Teams t ON ic.team_id = t.TeamID
            WHERE ic.id = @P1
        "#;

        let mut check_query = Query::new(check_sql);
        check_query.bind(card_id);

        let check_stream = check_query.query(&mut client).await?;
        let check_rows: Vec<_> = check_stream.into_first_result().await?;

        let card_row = check_rows.first()
            .ok_or_else(|| AppError::Business("指令卡不存在".to_string()))?;

        let workcenter_dept_code: Option<String> = card_row.get::<&str, _>(3).map(|s| s.to_string());

        // 如果不是管理员，检查工作中心权限
        if user_role != Some("admin") {
            let user_workcenter_dept_code = self.get_user_workcenter_dept_code(user_psn_num).await?;
            if user_workcenter_dept_code != workcenter_dept_code {
                return Err(AppError::Business("只能更新自己工作中心的指令卡".to_string()));
            }
        }

        // 更新manager_custom_count字段
        let update_sql = r#"
            UPDATE InstructionCards
            SET manager_custom_count = @P1,
                updated_at = GETDATE()
            WHERE id = @P2
        "#;

        let mut update_query = Query::new(update_sql);
        update_query.bind(count);
        update_query.bind(card_id);

        update_query.execute(&mut client).await?;

        tracing::info!(
            "主管自定义数量更新成功: 指令卡ID={}, 数量={}, 操作人={}",
            card_id, count, user_psn_num
        );

        Ok(())
    }

    /// 获取用户的班组ID
    async fn get_user_team_id(&self, user_psn_num: &str) -> AppResult<Option<i32>> {
        let mut client = self.db_config.get_app_connection().await?;

        let query_sql = r#"
            SELECT tm.TeamID
            FROM TeamMembers tm
            WHERE tm.Member_psn_num = @P1 AND tm.IsActive = 1
        "#;

        let mut query = Query::new(query_sql);
        query.bind(user_psn_num);

        let stream = query.query(&mut client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        Ok(rows.first().and_then(|row| row.get::<i32, _>(0)))
    }

    /// 获取用户的工作中心部门代码
    async fn get_user_workcenter_dept_code(&self, user_psn_num: &str) -> AppResult<Option<String>> {
        let mut client = self.db_config.get_app_connection().await?;

        let query_sql = r#"
            SELECT p.cDept_num
            FROM person p
            WHERE p.cpsn_num = @P1
        "#;

        let mut query = Query::new(query_sql);
        query.bind(user_psn_num);

        let stream = query.query(&mut client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        Ok(rows.first().and_then(|row| row.get::<&str, _>(0).map(|s| s.to_string())))
    }
}
