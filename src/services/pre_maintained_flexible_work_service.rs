use tiberius::Query;
use chrono::Utc;
use crate::config::DatabaseConfig;
use crate::models::{
    PreMaintainedFlexibleWork, PreMaintainedFlexibleWorkResponse, 
    CreatePreMaintainedFlexibleWorkRequest, UpdatePreMaintainedFlexibleWorkRequest,
    PreMaintainedFlexibleWorkQueryRequest, PreMaintainedFlexibleWorkPaginatedResponse
};
use crate::utils::{AppResult, AppError};

pub struct PreMaintainedFlexibleWorkService {
    db_config: DatabaseConfig,
}

impl PreMaintainedFlexibleWorkService {
    pub fn new(db_config: DatabaseConfig) -> Self {
        Self { db_config }
    }

    /// 生成预维护零活编号
    /// 格式：FW + 日期(YYYYMMDD) + 流水号(001-999)
    async fn generate_opcode(&self) -> AppResult<String> {
        let mut client = self.db_config.get_app_connection().await?;

        let today = Utc::now().format("%Y%m%d").to_string();
        let prefix = format!("FW{}", today);

        // 查询今天已有的最大流水号
        let query_sql = "SELECT MAX(opcode) FROM PreMaintainedFlexibleWorks WHERE opcode LIKE @P1";
        let mut query = Query::new(query_sql);
        query.bind(format!("{}%", prefix));

        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        let next_seq = if let Some(row) = rows.first() {
            if let Some(max_opcode) = row.get::<&str, _>(0) {
                // 提取流水号并加1
                let seq_str = &max_opcode[10..]; // FW20250724001 -> 001
                let seq: u32 = seq_str.parse().unwrap_or(0);
                seq + 1
            } else {
                1
            }
        } else {
            1
        };

        Ok(format!("{}{:03}", prefix, next_seq))
    }

    /// 获取用户的工作中心ID
    async fn get_user_workcenter_id(&self, user_psn_num: &str) -> AppResult<String> {
        let mut client = self.db_config.get_app_connection().await?;
        
        let query_sql = "SELECT cDept_num FROM person WHERE cpsn_num = @P1";
        let mut query = Query::new(query_sql);
        query.bind(user_psn_num);
        
        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;
        
        if let Some(row) = rows.first() {
            if let Some(dept_num) = row.get::<&str, _>(0) {
                return Ok(dept_num.to_string());
            }
        }
        
        Err(AppError::Business("无法获取用户工作中心信息".to_string()))
    }

    /// 查询预维护零活列表
    pub async fn query_pre_maintained_flexible_works(
        &self,
        request: &PreMaintainedFlexibleWorkQueryRequest,
        user_psn_num: &str,
    ) -> AppResult<PreMaintainedFlexibleWorkPaginatedResponse> {
        let mut client = self.db_config.get_app_connection().await?;
        
        // 获取用户工作中心ID
        let workcenter_id = self.get_user_workcenter_id(user_psn_num).await?;
        
        let mut where_conditions = vec!["pmfw.workcenter_id = @P1".to_string()];
        let mut params = vec![workcenter_id];
        let mut param_index = 2;

        // 关键词模糊查询
        if let Some(keyword) = &request.keyword {
            if !keyword.trim().is_empty() {
                where_conditions.push(format!(
                    "(pmfw.opcode LIKE @P{} OR pmfw.description LIKE @P{})", 
                    param_index, param_index + 1
                ));
                params.push(format!("%{}%", keyword.trim()));
                params.push(format!("%{}%", keyword.trim()));
                param_index += 2;
            }
        }

        let where_clause = if where_conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_conditions.join(" AND "))
        };

        // 查询总数
        let count_sql = format!(
            "SELECT COUNT(*) FROM PreMaintainedFlexibleWorks pmfw {}",
            where_clause
        );
        
        let mut count_query = Query::new(&count_sql);
        for param in &params {
            count_query.bind(param);
        }
        
        let count_stream = count_query.query(&mut *client).await?;
        let count_rows: Vec<_> = count_stream.into_first_result().await?;
        let total = count_rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0);

        // 查询数据
        let data_sql = format!(
            r#"
            SELECT 
                pmfw.opcode,
                pmfw.description,
                pmfw.workcenter_id,
                w.description as workcenter_name
            FROM PreMaintainedFlexibleWorks pmfw
            LEFT JOIN workcenter w ON pmfw.workcenter_id COLLATE SQL_Latin1_General_CP1_CI_AS = w.DeptCode COLLATE SQL_Latin1_General_CP1_CI_AS
            {}
            ORDER BY pmfw.opcode
            OFFSET @P{} ROWS FETCH NEXT @P{} ROWS ONLY
            "#,
            where_clause,
            param_index,
            param_index + 1
        );

        let mut data_query = Query::new(&data_sql);
        for param in &params {
            data_query.bind(param);
        }
        data_query.bind(request.get_offset());
        data_query.bind(request.get_page_size());

        let data_stream = data_query.query(&mut *client).await?;
        let data_rows: Vec<_> = data_stream.into_first_result().await?;

        let items: Vec<PreMaintainedFlexibleWorkResponse> = data_rows
            .iter()
            .map(|row| PreMaintainedFlexibleWorkResponse {
                opcode: row.get::<&str, _>(0).unwrap_or("").to_string(),
                description: row.get::<&str, _>(1).unwrap_or("").to_string(),
                workcenter_id: row.get::<&str, _>(2).unwrap_or("").to_string(),
                workcenter_name: row.get::<&str, _>(3).map(|s| s.to_string()),
            })
            .collect();

        let total_pages = (total as f64 / request.get_page_size() as f64).ceil() as i32;

        Ok(PreMaintainedFlexibleWorkPaginatedResponse {
            items,
            total: total as i64,
            page: request.get_page(),
            page_size: request.get_page_size(),
            total_pages,
        })
    }

    /// 创建预维护零活
    pub async fn create_pre_maintained_flexible_work(
        &self,
        request: &CreatePreMaintainedFlexibleWorkRequest,
        user_psn_num: &str,
    ) -> AppResult<PreMaintainedFlexibleWork> {
        let mut client = self.db_config.get_app_connection().await?;

        // 获取用户工作中心ID
        let workcenter_id = self.get_user_workcenter_id(user_psn_num).await?;

        // 生成opcode
        let opcode = self.generate_opcode().await?;

        // 插入数据
        let insert_sql = "INSERT INTO PreMaintainedFlexibleWorks (opcode, description, workcenter_id) VALUES (@P1, @P2, @P3)";
        let mut insert_query = Query::new(insert_sql);
        insert_query.bind(&opcode);
        insert_query.bind(&request.description);
        insert_query.bind(&workcenter_id);

        insert_query.execute(&mut *client).await?;

        Ok(PreMaintainedFlexibleWork {
            opcode,
            description: request.description.clone(),
            workcenter_id,
        })
    }

    /// 更新预维护零活
    pub async fn update_pre_maintained_flexible_work(
        &self,
        opcode: &str,
        request: &UpdatePreMaintainedFlexibleWorkRequest,
        user_psn_num: &str,
    ) -> AppResult<PreMaintainedFlexibleWork> {
        let mut client = self.db_config.get_app_connection().await?;
        
        // 获取用户工作中心ID
        let workcenter_id = self.get_user_workcenter_id(user_psn_num).await?;
        
        // 检查记录是否存在且属于该用户的工作中心
        let check_sql = "SELECT COUNT(*) FROM PreMaintainedFlexibleWorks WHERE opcode = @P1 AND workcenter_id = @P2";
        let mut check_query = Query::new(check_sql);
        check_query.bind(opcode);
        check_query.bind(&workcenter_id);
        
        let check_stream = check_query.query(&mut *client).await?;
        let check_rows: Vec<_> = check_stream.into_first_result().await?;
        let exists = check_rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0) > 0;
            
        if !exists {
            return Err(AppError::Business("记录不存在或无权限修改".to_string()));
        }
        
        // 更新数据
        let update_sql = "UPDATE PreMaintainedFlexibleWorks SET description = @P1 WHERE opcode = @P2 AND workcenter_id = @P3";
        let mut update_query = Query::new(update_sql);
        update_query.bind(&request.description);
        update_query.bind(opcode);
        update_query.bind(&workcenter_id);
        
        update_query.execute(&mut *client).await?;
        
        Ok(PreMaintainedFlexibleWork {
            opcode: opcode.to_string(),
            description: request.description.clone(),
            workcenter_id,
        })
    }

    /// 删除预维护零活
    pub async fn delete_pre_maintained_flexible_work(
        &self,
        opcode: &str,
        user_psn_num: &str,
    ) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;
        
        // 获取用户工作中心ID
        let workcenter_id = self.get_user_workcenter_id(user_psn_num).await?;
        
        // 检查是否有关联的零活录入记录
        let check_usage_sql = "SELECT COUNT(*) FROM FlexibleEntries WHERE operation_id = @P1";
        let mut check_usage_query = Query::new(check_usage_sql);
        check_usage_query.bind(opcode);
        
        let usage_stream = check_usage_query.query(&mut *client).await?;
        let usage_rows: Vec<_> = usage_stream.into_first_result().await?;
        let usage_count = usage_rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0);
            
        if usage_count > 0 {
            return Err(AppError::Business("该零活已被使用，无法删除".to_string()));
        }
        
        // 删除数据
        let delete_sql = "DELETE FROM PreMaintainedFlexibleWorks WHERE opcode = @P1 AND workcenter_id = @P2";
        let mut delete_query = Query::new(delete_sql);
        delete_query.bind(opcode);
        delete_query.bind(&workcenter_id);
        
        let result = delete_query.execute(&mut *client).await?;
        
        if result.rows_affected().first().unwrap_or(&0) == &0 {
            return Err(AppError::Business("记录不存在或无权限删除".to_string()));
        }
        
        Ok(())
    }

    /// 根据opcode获取预维护零活详情
    pub async fn get_pre_maintained_flexible_work_by_opcode(
        &self,
        opcode: &str,
    ) -> AppResult<Option<PreMaintainedFlexibleWork>> {
        let mut client = self.db_config.get_app_connection().await?;
        
        let query_sql = "SELECT opcode, description, workcenter_id FROM PreMaintainedFlexibleWorks WHERE opcode = @P1";
        let mut query = Query::new(query_sql);
        query.bind(opcode);
        
        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;
        
        if let Some(row) = rows.first() {
            Ok(Some(PreMaintainedFlexibleWork {
                opcode: row.get::<&str, _>(0).unwrap_or("").to_string(),
                description: row.get::<&str, _>(1).unwrap_or("").to_string(),
                workcenter_id: row.get::<&str, _>(2).unwrap_or("").to_string(),
            }))
        } else {
            Ok(None)
        }
    }
}
