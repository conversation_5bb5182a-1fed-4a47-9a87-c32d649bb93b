// 借用功能服务层
use tiberius::Query;
use crate::config::DatabaseConfig;
use crate::models::{
    BorrowRequestResponse, CreateBorrowRequest, ApproveBorrowRequest,
    BorrowRequestQuery, BorrowRequestListResponse, BorrowStatus,
    TeamMemberWithBorrowStatus, BorrowStatusInfo, UpdateBorrowStatusRequest
};
use crate::utils::{AppResult, AppError};
use chrono::{Utc, DateTime};

pub struct BorrowService {
    db_config: DatabaseConfig,
}

impl BorrowService {
    pub fn new(db_config: DatabaseConfig) -> Self {
        Self { db_config }
    }

    /// 发起借用申请
    pub async fn create_borrow_request(
        &self,
        borrow_team_id: i32,
        request: &CreateBorrowRequest,
        requested_by: &str,
    ) -> AppResult<i32> {
        let mut client = self.db_config.get_app_connection().await?;

        // 1. 验证被借用人员是否存在
        let member_check_sql = r#"
            SELECT hp.cpsn_num, hp.cPsn_Name
            FROM person hp
            WHERE hp.cpsn_num = @P1
        "#;
        let mut member_query = Query::new(member_check_sql);
        member_query.bind(&request.member_psn_num);
        let member_result = member_query.query(&mut client).await?;

        if member_result.into_row().await?.is_none() {
            return Err(AppError::Validation("指定的人员不存在".to_string()));
        }

        // 2. 查找该人员当前所属的班组（原班组）
        let original_team_sql = r#"
            SELECT tm.TeamID
            FROM TeamMembers tm
            WHERE tm.Member_psn_num = @P1 AND tm.IsActive = 1
        "#;
        let mut original_team_query = Query::new(original_team_sql);
        original_team_query.bind(&request.member_psn_num);
        let original_team_result = original_team_query.query(&mut client).await?;

        let original_team_id = if let Some(row) = original_team_result.into_row().await? {
            row.get::<i32, _>(0).unwrap_or(0)
        } else {
            return Err(AppError::Validation("该人员不属于任何班组，无法借用".to_string()));
        };

        // 3. 验证不能借用自己班组的人员
        if original_team_id == borrow_team_id {
            return Err(AppError::Validation("不能借用本班组的人员".to_string()));
        }

        // 4. 检查是否已有进行中的借用申请
        let existing_borrow_sql = r#"
            SELECT COUNT(*)
            FROM TeamMemberBorrows
            WHERE Member_psn_num = @P1 
            AND BorrowStatus IN (0, 1)  -- 待同意或已同意
        "#;
        let mut existing_query = Query::new(existing_borrow_sql);
        existing_query.bind(&request.member_psn_num);
        let existing_result = existing_query.query(&mut client).await?;

        if let Some(row) = existing_result.into_row().await? {
            let count: i32 = row.get(0).unwrap_or(0);
            if count > 0 {
                return Err(AppError::Validation("该人员已有进行中的借用申请".to_string()));
            }
        }

        // 5. 验证借用时间
        if request.end_date <= Utc::now() {
            return Err(AppError::Validation("借用结束时间必须晚于当前时间".to_string()));
        }

        // 6. 检查是否可以自动审核（班长申请且工作中心dep字段匹配）
        let auto_approve = self.check_auto_approve_eligibility(requested_by, &request.member_psn_num, borrow_team_id).await?;
        let initial_status = if auto_approve { 1 } else { 0 }; // 1=已同意, 0=待同意

        // 7. 创建借用申请
        let insert_sql = r#"
            INSERT INTO TeamMemberBorrows (
                Member_psn_num, OriginalTeamID, BorrowTeamID, BorrowStatus,
                StartDate, EndDate, RequestedBy, Remarks, RequestedAt, CreatedAt, UpdatedAt
            )
            OUTPUT INSERTED.BorrowID
            VALUES (@P1, @P2, @P3, @P4, @P5, @P6, @P7, @P8, GETDATE(), GETDATE(), GETDATE())
        "#;

        let mut insert_query = Query::new(insert_sql);
        insert_query.bind(&request.member_psn_num);
        insert_query.bind(original_team_id);
        insert_query.bind(borrow_team_id);
        insert_query.bind(initial_status);

        // 根据审批状态决定开始时间
        if auto_approve {
            // 自动通过：立即生效
            insert_query.bind(Utc::now());
        } else {
            // 需要审核：开始时间为空，审批通过时再设置
            insert_query.bind(Option::<DateTime<Utc>>::None);
        }

        insert_query.bind(request.end_date);
        insert_query.bind(requested_by);
        insert_query.bind(request.remarks.as_deref());

        let insert_result = insert_query.query(&mut client).await?;
        
        if let Some(row) = insert_result.into_row().await? {
            let borrow_id: i32 = row.get(0).unwrap_or(0);
            
            let status_text = if auto_approve { "自动审核通过" } else { "待审核" };
            tracing::info!(
                "借用申请创建成功：申请ID={}, 申请人={}, 被借用人={}, 原班组={}, 借用班组={}, 状态={}",
                borrow_id, requested_by, request.member_psn_num, original_team_id, borrow_team_id, status_text
            );
            
            Ok(borrow_id)
        } else {
            Err(AppError::Database("创建借用申请失败".to_string()))
        }
    }

    /// 审批借用申请
    pub async fn approve_borrow_request(
        &self,
        borrow_id: i32,
        request: &ApproveBorrowRequest,
        approved_by: &str,
    ) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        // 1. 验证借用申请是否存在且状态为待同意
        let check_sql = r#"
            SELECT BorrowID, BorrowStatus, OriginalTeamID
            FROM TeamMemberBorrows
            WHERE BorrowID = @P1
        "#;
        let mut check_query = Query::new(check_sql);
        check_query.bind(borrow_id);
        let check_result = check_query.query(&mut client).await?;

        let (current_status, original_team_id) = if let Some(row) = check_result.into_row().await? {
            let status: i32 = row.get(1).unwrap_or(-1);
            let team_id: i32 = row.get(2).unwrap_or(0);
            (status, team_id)
        } else {
            return Err(AppError::Validation("借用申请不存在".to_string()));
        };

        if current_status != 0 {
            return Err(AppError::Validation("只能审批待同意状态的借用申请".to_string()));
        }

        // 2. 验证审批人是否为原班组的班长
        let leader_check_sql = r#"
            SELECT COUNT(*)
            FROM TeamLeaders tl
            WHERE tl.TeamID = @P1 AND tl.Leader_psn_num = @P2 AND tl.IsActive = 1
        "#;
        let mut leader_query = Query::new(leader_check_sql);
        leader_query.bind(original_team_id);
        leader_query.bind(approved_by);
        let leader_result = leader_query.query(&mut client).await?;

        if let Some(row) = leader_result.into_row().await? {
            let count: i32 = row.get(0).unwrap_or(0);
            if count == 0 {
                return Err(AppError::Validation("只有原班组的班长才能审批借用申请".to_string()));
            }
        }

        // 3. 更新借用申请状态
        let new_status = match request.action.as_str() {
            "approve" => 1, // 已同意
            "reject" => 2,  // 已拒绝
            _ => return Err(AppError::Validation("无效的审批操作".to_string())),
        };

        let update_sql = if new_status == 1 {
            // 审批通过：设置开始时间为当前时间（立即生效）
            r#"
                UPDATE TeamMemberBorrows
                SET BorrowStatus = @P1, ApprovedBy = @P2, ApprovedAt = GETDATE(),
                    StartDate = GETDATE(), Remarks = COALESCE(@P3, Remarks), UpdatedAt = GETDATE()
                WHERE BorrowID = @P4
            "#
        } else {
            // 审批拒绝：不设置开始时间
            r#"
                UPDATE TeamMemberBorrows
                SET BorrowStatus = @P1, ApprovedBy = @P2, ApprovedAt = GETDATE(),
                    Remarks = COALESCE(@P3, Remarks), UpdatedAt = GETDATE()
                WHERE BorrowID = @P4
            "#
        };

        let mut update_query = Query::new(update_sql);
        update_query.bind(new_status);
        update_query.bind(approved_by);
        update_query.bind(request.remarks.as_deref());
        update_query.bind(borrow_id);

        update_query.execute(&mut client).await?;

        let action_text = if new_status == 1 { "同意" } else { "拒绝" };
        tracing::info!(
            "借用申请审批完成：申请ID={}, 审批人={}, 操作={}", 
            borrow_id, approved_by, action_text
        );

        Ok(())
    }

    /// 查询借用申请列表
    pub async fn get_borrow_requests(
        &self,
        team_id: i32,
        query: &BorrowRequestQuery,
    ) -> AppResult<BorrowRequestListResponse> {
        let mut client = self.db_config.get_app_connection().await?;

        let page = query.page.unwrap_or(1);
        let page_size = query.page_size.unwrap_or(20);
        let offset = (page - 1) * page_size;

        // 构建查询条件
        let mut where_conditions = Vec::new();

        // 根据查询类型确定条件
        match query.request_type.as_deref() {
            Some("incoming") => {
                // 收到的申请（其他班组申请借用本班组的人员）
                where_conditions.push(format!("tmb.OriginalTeamID = {}", team_id));
            }
            Some("outgoing") => {
                // 发出的申请（本班组申请借用其他班组的人员）
                where_conditions.push(format!("tmb.BorrowTeamID = {}", team_id));
            }
            _ => {
                // 默认查询所有相关的申请
                where_conditions.push(format!("(tmb.OriginalTeamID = {} OR tmb.BorrowTeamID = {})", team_id, team_id));
            }
        }

        // 状态过滤
        if let Some(status) = query.status {
            where_conditions.push(format!("tmb.BorrowStatus = {}", status));
        }

        // 时间范围过滤
        if let Some(start_time) = &query.start_time {
            where_conditions.push(format!("tmb.RequestedAt >= '{}'", start_time.format("%Y-%m-%d %H:%M:%S")));
        }

        if let Some(end_time) = &query.end_time {
            where_conditions.push(format!("tmb.RequestedAt <= '{}'", end_time.format("%Y-%m-%d %H:%M:%S")));
        }

        let where_clause = if where_conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_conditions.join(" AND "))
        };

        // 查询总数
        let count_sql = format!(
            r#"
            SELECT COUNT(*)
            FROM TeamMemberBorrows tmb
            {}
            "#,
            where_clause
        );

        let count_query = Query::new(&count_sql);
        let count_result = count_query.query(&mut client).await?;
        let total = if let Some(row) = count_result.into_row().await? {
            row.get::<i32, _>(0).unwrap_or(0) as u32
        } else {
            0
        };

        // 查询数据
        let data_sql = format!(
            r#"
            SELECT
                tmb.BorrowID,
                tmb.Member_psn_num,
                hp.cPsn_Name AS MemberName,
                tmb.OriginalTeamID,
                ot.TeamName AS OriginalTeamName,
                tmb.BorrowTeamID,
                bt.TeamName AS BorrowTeamName,
                tmb.BorrowStatus,
                tmb.StartDate,
                tmb.EndDate,
                tmb.RequestedBy,
                req_hp.cPsn_Name AS RequestedByName,
                tmb.ApprovedBy,
                app_hp.cPsn_Name AS ApprovedByName,
                tmb.RequestedAt,
                tmb.ApprovedAt,
                tmb.Remarks
            FROM TeamMemberBorrows tmb
            INNER JOIN person hp ON tmb.Member_psn_num COLLATE SQL_Latin1_General_CP1_CI_AS = hp.cpsn_num COLLATE SQL_Latin1_General_CP1_CI_AS
            INNER JOIN Teams ot ON tmb.OriginalTeamID = ot.TeamID
            INNER JOIN Teams bt ON tmb.BorrowTeamID = bt.TeamID
            LEFT JOIN person req_hp ON tmb.RequestedBy COLLATE SQL_Latin1_General_CP1_CI_AS = req_hp.cpsn_num COLLATE SQL_Latin1_General_CP1_CI_AS
            LEFT JOIN person app_hp ON tmb.ApprovedBy COLLATE SQL_Latin1_General_CP1_CI_AS = app_hp.cpsn_num COLLATE SQL_Latin1_General_CP1_CI_AS
            {}
            ORDER BY tmb.RequestedAt DESC
            OFFSET {} ROWS FETCH NEXT {} ROWS ONLY
            "#,
            where_clause, offset, page_size
        );

        let data_query = Query::new(&data_sql);
        let data_result = data_query.query(&mut client).await?;
        let rows: Vec<_> = data_result.into_first_result().await?;

        let mut requests = Vec::new();
        for row in rows {
            requests.push(BorrowRequestResponse {
                borrow_id: row.get::<i32, _>(0).unwrap_or(0),
                member_psn_num: row.get::<&str, _>(1).unwrap_or("").to_string(),
                member_name: row.get::<&str, _>(2).map(|s| s.to_string()),
                original_team_id: row.get::<i32, _>(3).unwrap_or(0),
                original_team_name: row.get::<&str, _>(4).unwrap_or("").to_string(),
                borrow_team_id: row.get::<i32, _>(5).unwrap_or(0),
                borrow_team_name: row.get::<&str, _>(6).unwrap_or("").to_string(),
                borrow_status: BorrowStatus::from(row.get::<i32, _>(7).unwrap_or(0)),
                start_date: row.get::<chrono::DateTime<Utc>, _>(8)
                    .map(|dt| dt)
                    .unwrap_or_else(|| Utc::now()),
                end_date: row.get::<chrono::DateTime<Utc>, _>(9)
                    .map(|dt| dt)
                    .unwrap_or_else(|| Utc::now()),
                requested_by: row.get::<&str, _>(10).unwrap_or("").to_string(),
                requested_by_name: row.get::<&str, _>(11).map(|s| s.to_string()),
                approved_by: row.get::<&str, _>(12).map(|s| s.to_string()),
                approved_by_name: row.get::<&str, _>(13).map(|s| s.to_string()),
                requested_at: row.get::<chrono::DateTime<Utc>, _>(14)
                    .map(|dt| dt)
                    .unwrap_or_else(|| Utc::now()),
                approved_at: row.get::<chrono::DateTime<Utc>, _>(15)
                    .map(|dt| dt),
                remarks: row.get::<&str, _>(16).map(|s| s.to_string()),
            });
        }

        let total_pages = ((total as f64) / (page_size as f64)).ceil() as u32;

        Ok(BorrowRequestListResponse {
            requests,
            total,
            page,
            page_size,
            total_pages,
        })
    }

    /// 查询被借走的人员列表
    pub async fn get_borrowed_out_members(&self, team_id: i32) -> AppResult<Vec<TeamMemberWithBorrowStatus>> {
        let mut client = self.db_config.get_app_connection().await?;

        let sql = r#"
            SELECT
                tmb.OriginalTeamID AS TeamID,
                tmb.Member_psn_num,
                hp.cPsn_Name AS MemberName,
                tmb.StartDate AS JoinedAt,
                1 AS IsActive,
                tmb.BorrowTeamID,
                bt.TeamName AS BorrowTeamName,
                tmb.EndDate,
                tmb.BorrowStatus,
                CASE
                    WHEN tmb.BorrowStatus = 1 AND GETDATE() > tmb.EndDate THEN 3  -- 自动归还
                    ELSE tmb.BorrowStatus
                END AS ActualStatus
            FROM TeamMemberBorrows tmb
            INNER JOIN person hp ON tmb.Member_psn_num COLLATE SQL_Latin1_General_CP1_CI_AS = hp.cpsn_num COLLATE SQL_Latin1_General_CP1_CI_AS
            INNER JOIN Teams bt ON tmb.BorrowTeamID = bt.TeamID
            WHERE tmb.OriginalTeamID = @P1
            AND tmb.BorrowStatus IN (1, 3)  -- 借用中、已归还
            ORDER BY tmb.BorrowStatus ASC, tmb.StartDate DESC
        "#;

        let mut query = Query::new(sql);
        query.bind(team_id);
        let result = query.query(&mut client).await?;
        let rows: Vec<_> = result.into_first_result().await?;

        let mut members = Vec::new();
        for row in rows {
            let actual_status: i32 = row.get::<i32, _>(9).unwrap_or(1); // ActualStatus
            let borrow_status_enum = BorrowStatus::from(actual_status);

            let borrow_status = Some(BorrowStatusInfo {
                is_borrowed_out: true,
                is_borrowed_in: false,
                borrow_status: borrow_status_enum,
                status_description: borrow_status_enum.description().to_string(),
                borrow_team_id: row.get::<i32, _>(5).map(|id| id),
                borrow_team_name: row.get::<&str, _>(6).map(|s| s.to_string()),
                original_team_id: None,
                original_team_name: None,
                borrow_end_date: row.get::<tiberius::time::chrono::DateTime<Utc>, _>(7)
                    .map(|dt| dt.timestamp()),
            });

            members.push(TeamMemberWithBorrowStatus {
                team_id: row.get::<i32, _>(0).unwrap_or(0),
                member_psn_num: row.get::<&str, _>(1).unwrap_or("").to_string(),
                member_name: row.get::<&str, _>(2).map(|s| s.to_string()),
                joined_at: row.get::<tiberius::time::chrono::DateTime<Utc>, _>(3)
                    .map(|dt| dt.timestamp())
                    .unwrap_or_else(|| Utc::now().timestamp()),
                status: row.get::<i32, _>(4).unwrap_or(1) as i16,
                borrow_status,
            });
        }

        Ok(members)
    }

    /// 查询借用来的人员列表
    pub async fn get_borrowed_in_members(&self, team_id: i32) -> AppResult<Vec<TeamMemberWithBorrowStatus>> {
        let mut client = self.db_config.get_app_connection().await?;

        let sql = r#"
            SELECT
                tmb.BorrowTeamID AS TeamID,
                tmb.Member_psn_num,
                hp.cPsn_Name AS MemberName,
                tmb.StartDate AS JoinedAt,
                1 AS IsActive,
                tmb.OriginalTeamID,
                ot.TeamName AS OriginalTeamName,
                tmb.EndDate,
                tmb.BorrowStatus,
                CASE
                    WHEN tmb.BorrowStatus = 1 AND GETDATE() > tmb.EndDate THEN 3  -- 自动归还
                    ELSE tmb.BorrowStatus
                END AS ActualStatus
            FROM TeamMemberBorrows tmb
            INNER JOIN person hp ON tmb.Member_psn_num COLLATE SQL_Latin1_General_CP1_CI_AS = hp.cpsn_num COLLATE SQL_Latin1_General_CP1_CI_AS
            INNER JOIN Teams ot ON tmb.OriginalTeamID = ot.TeamID
            WHERE tmb.BorrowTeamID = @P1
            AND tmb.BorrowStatus IN (1, 3)  -- 借用中、已归还
            ORDER BY tmb.BorrowStatus ASC, tmb.StartDate DESC
        "#;

        let mut query = Query::new(sql);
        query.bind(team_id);
        let result = query.query(&mut client).await?;
        let rows: Vec<_> = result.into_first_result().await?;

        let mut members = Vec::new();
        for row in rows {
            let actual_status: i32 = row.get::<i32, _>(9).unwrap_or(1); // ActualStatus
            let borrow_status_enum = BorrowStatus::from(actual_status);

            let borrow_status = Some(BorrowStatusInfo {
                is_borrowed_out: false,
                is_borrowed_in: true,
                borrow_status: borrow_status_enum,
                status_description: borrow_status_enum.description().to_string(),
                borrow_team_id: None,
                borrow_team_name: None,
                original_team_id: row.get::<i32, _>(5).map(|id| id),
                original_team_name: row.get::<&str, _>(6).map(|s| s.to_string()),
                borrow_end_date: row.get::<tiberius::time::chrono::DateTime<Utc>, _>(7)
                    .map(|dt| dt.timestamp()),
            });

            members.push(TeamMemberWithBorrowStatus {
                team_id: row.get::<i32, _>(0).unwrap_or(0),
                member_psn_num: row.get::<&str, _>(1).unwrap_or("").to_string(),
                member_name: row.get::<&str, _>(2).map(|s| s.to_string()),
                joined_at: row.get::<tiberius::time::chrono::DateTime<Utc>, _>(3)
                    .map(|dt| dt.timestamp())
                    .unwrap_or_else(|| Utc::now().timestamp()),
                status: row.get::<i32, _>(4).unwrap_or(1) as i16,
                borrow_status,
            });
        }

        Ok(members)
    }

    /// 检查是否符合自动审核条件
    /// 条件：1) 申请人是班长 2) 申请人和被借用人的工作中心dep字段相同 3) 被借用人不在申请人的班组
    async fn check_auto_approve_eligibility(
        &self,
        requested_by: &str,
        member_psn_num: &str,
        borrow_team_id: i32,
    ) -> AppResult<bool> {
        let mut client = self.db_config.get_app_connection().await?;

        // 1. 检查申请人是否为指定班组的班长
        let leader_check_sql = r#"
            SELECT COUNT(*)
            FROM TeamLeaders tl
            WHERE tl.TeamID = @P1 AND tl.Leader_psn_num = @P2 AND tl.IsActive = 1
        "#;
        let mut leader_query = Query::new(leader_check_sql);
        leader_query.bind(borrow_team_id);
        leader_query.bind(requested_by);
        let leader_result = leader_query.query(&mut client).await?;

        let is_leader = if let Some(row) = leader_result.into_row().await? {
            row.get::<i32, _>(0).unwrap_or(0) > 0
        } else {
            false
        };

        if !is_leader {
            return Ok(false); // 不是班长，不符合自动审核条件
        }

        // 2. 获取申请人的工作中心dep字段
        let requester_dep = self.get_person_workcenter_dep(requested_by).await?;

        // 3. 获取被借用人的工作中心dep字段
        let member_dep = self.get_person_workcenter_dep(member_psn_num).await?;

        // 4. 检查dep字段是否匹配
        match (requester_dep, member_dep) {
            (Some(req_dep), Some(mem_dep)) => {
                let deps_match = req_dep == mem_dep;
                if deps_match {
                    tracing::info!(
                        "自动审核条件满足：申请人={}, 被借用人={}, 工作中心dep字段={}",
                        requested_by, member_psn_num, req_dep
                    );
                }
                Ok(deps_match)
            }
            _ => {
                tracing::warn!(
                    "无法获取工作中心dep字段：申请人={}, 被借用人={}",
                    requested_by, member_psn_num
                );
                Ok(false) // 无法获取dep字段，不符合自动审核条件
            }
        }
    }

    /// 根据人员编号获取其工作中心的dep字段
    async fn get_person_workcenter_dep(&self, psn_num: &str) -> AppResult<Option<String>> {
        let mut client = self.db_config.get_app_connection().await?;

        let source_db = &self.db_config.get_source_database_name();
        let sql = format!(
            r#"
            SELECT w.dep
            FROM person p
            LEFT JOIN {}.dbo.workcenter w ON p.cDept_num COLLATE SQL_Latin1_General_CP1_CI_AS = w.DeptCode COLLATE SQL_Latin1_General_CP1_CI_AS
            WHERE p.cpsn_num = @P1
            "#,
            source_db
        );

        let mut query = Query::new(&sql);
        query.bind(psn_num);
        let result = query.query(&mut client).await?;

        if let Some(row) = result.into_row().await? {
            let dep = row.get::<&str, _>(0).map(|s| s.to_string());
            Ok(dep)
        } else {
            Ok(None)
        }
    }

    /// 检查用户是否是指定班组的班长
    pub async fn is_team_leader(&self, team_id: i32, user_psn_num: &str) -> AppResult<bool> {
        let mut client = self.db_config.get_app_connection().await?;

        let check_leader_sql = r#"
            SELECT COUNT(*)
            FROM TeamLeaders tl
            WHERE tl.TeamID = @P1 AND tl.Leader_psn_num = @P2 AND tl.IsActive = 1
        "#;

        let mut query = Query::new(check_leader_sql);
        query.bind(team_id);
        query.bind(user_psn_num);
        let result = query.query(&mut client).await?;

        if let Some(row) = result.into_row().await? {
            let count = row.get::<i32, _>(0).unwrap_or(0);
            Ok(count > 0)
        } else {
            Ok(false)
        }
    }

    /// 检查用户是否是借用申请中被借用人员的原班组班长
    pub async fn is_original_team_leader(&self, borrow_id: i32, user_psn_num: &str) -> AppResult<bool> {
        let mut client = self.db_config.get_app_connection().await?;

        let check_sql = r#"
            SELECT COUNT(*)
            FROM TeamMemberBorrows tmb
            INNER JOIN TeamLeaders tl ON tmb.OriginalTeamID = tl.TeamID
            WHERE tmb.BorrowID = @P1
              AND tl.Leader_psn_num = @P2
              AND tl.IsActive = 1
        "#;

        let mut query = Query::new(check_sql);
        query.bind(borrow_id);
        query.bind(user_psn_num);
        let result = query.query(&mut client).await?;

        if let Some(row) = result.into_row().await? {
            let count = row.get::<i32, _>(0).unwrap_or(0);
            Ok(count > 0)
        } else {
            Ok(false)
        }
    }

    /// 验证借用申请是否属于指定的班组（原班组）
    pub async fn validate_borrow_request_team(&self, borrow_id: i32, team_id: i32) -> AppResult<bool> {
        let mut client = self.db_config.get_app_connection().await?;

        let check_sql = r#"
            SELECT COUNT(*)
            FROM TeamMemberBorrows tmb
            WHERE tmb.BorrowID = @P1 AND tmb.OriginalTeamID = @P2
        "#;

        let mut query = Query::new(check_sql);
        query.bind(borrow_id);
        query.bind(team_id);
        let result = query.query(&mut client).await?;

        if let Some(row) = result.into_row().await? {
            let count = row.get::<i32, _>(0).unwrap_or(0);
            Ok(count > 0)
        } else {
            Ok(false)
        }
    }

    /// 修改借用状态（主动归还）
    pub async fn update_borrow_status(
        &self,
        borrow_id: i32,
        request: &UpdateBorrowStatusRequest,
        updated_by: &str,
    ) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        // 1. 验证借用申请是否存在且状态为已同意
        let check_sql = r#"
            SELECT BorrowID, BorrowStatus, Member_psn_num, OriginalTeamID, BorrowTeamID
            FROM TeamMemberBorrows
            WHERE BorrowID = @P1
        "#;
        let mut check_query = Query::new(check_sql);
        check_query.bind(borrow_id);
        let check_result = check_query.query(&mut client).await?;

        let (current_status, member_psn_num, original_team_id, borrow_team_id) = if let Some(row) = check_result.into_row().await? {
            let status: i32 = row.get(1).unwrap_or(-1);
            let member: String = row.get::<&str, _>(2).unwrap_or("").to_string();
            let orig_team: i32 = row.get(3).unwrap_or(0);
            let borrow_team: i32 = row.get(4).unwrap_or(0);
            (status, member, orig_team, borrow_team)
        } else {
            return Err(AppError::Validation("借用申请不存在".to_string()));
        };

        // 2. 检查当前状态是否为已同意(1)
        if current_status != (BorrowStatus::Approved as i32) {
            return Err(AppError::Validation("只有已同意状态的借用申请才能修改为已归还".to_string()));
        }

        // 3. 验证操作类型
        if request.action != "return" {
            return Err(AppError::Validation("无效的操作类型，只支持 'return'".to_string()));
        }

        // 4. 执行状态更新
        let update_sql = r#"
            UPDATE TeamMemberBorrows
            SET BorrowStatus = @P1, UpdatedAt = GETDATE()
            WHERE BorrowID = @P2
        "#;
        let mut update_query = Query::new(update_sql);
        update_query.bind(BorrowStatus::Returned as i32);
        update_query.bind(borrow_id);

        match update_query.execute(&mut client).await {
            Ok(_) => {
                tracing::info!(
                    "借用状态修改成功：申请ID={}, 被借用人={}, 原班组={}, 借用班组={}, 操作人={}",
                    borrow_id, member_psn_num, original_team_id, borrow_team_id, updated_by
                );
                Ok(())
            }
            Err(e) => {
                tracing::error!("修改借用状态失败: {}", e);
                Err(AppError::Database("修改借用状态失败".to_string()))
            }
        }
    }
}
