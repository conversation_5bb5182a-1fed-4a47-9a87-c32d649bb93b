// 班组管理服务 - 修复版本
use tiberius::Query;
use crate::config::DatabaseConfig;
use crate::models::{
    TeamResponse, TeamDetailResponse, TeamLeaderResponse, TeamMemberResponse, SimpleTeamMemberResponse,
    UserTeamInfoResponse, UserRoleTeamStatusResponse, CreateTeamRequest, UpdateTeamRequest,
    AssignLeaderRequest, UnifiedAssignLeaderRequest, AddMemberRequest, TeamListQuery, TeamListResponse
};
use crate::utils::{AppResult, AppError};

use chrono::Utc;

pub struct TeamService {
    db_config: DatabaseConfig,
}

impl TeamService {
    pub fn new(db_config: DatabaseConfig) -> Self {
        Self { db_config }
    }

    /// 公共工具方法：将班组成员转换为班长
    /// 如果指定人员是班组的普通成员，将其从 TeamMembers 移除并添加到 TeamLeaders
    async fn promote_member_to_leader(
        &self,
        team_id: i32,
        psn_num: &str,
        _assigned_by: &str,
    ) -> AppResult<bool> {
        let mut client = self.db_config.get_app_connection().await?;
        // 检查该人员是否是该班组的普通成员
        let member_check_sql = r#"
            SELECT COUNT(*)
            FROM TeamMembers
            WHERE TeamID = @P1 AND Member_psn_num = @P2 AND IsActive = 1
        "#;

        let mut member_query = Query::new(member_check_sql);
        member_query.bind(team_id);
        member_query.bind(psn_num);
        let member_result = member_query.query(&mut client).await?;

        let is_member = if let Some(row) = member_result.into_row().await? {
            row.get::<i32, _>(0).unwrap_or(0) > 0
        } else {
            false
        };

        if is_member {
            // 从 TeamMembers 中移除
            let remove_member_sql = r#"
                UPDATE TeamMembers
                SET IsActive = 0
                WHERE TeamID = @P1 AND Member_psn_num = @P2 AND IsActive = 1
            "#;

            let mut remove_query = Query::new(remove_member_sql);
            remove_query.bind(team_id);
            remove_query.bind(psn_num);
            remove_query.execute(&mut client).await?;

            tracing::info!(
                "已将用户 {} 从班组 {} 的成员列表中移除，准备提升为班长",
                psn_num, team_id
            );

            return Ok(true);
        }

        Ok(false)
    }

    /// 获取班组列表 - 支持分页和筛选
    pub async fn get_teams(&self, query: &TeamListQuery) -> AppResult<TeamListResponse> {
        let mut client = self.db_config.get_app_connection().await?;

        let page = query.page.unwrap_or(1);
        let page_size = query.page_size.unwrap_or(20).min(200); // 最大100条
        let offset = (page - 1) * page_size;

        // 构建查询条件
        let mut where_conditions = Vec::new();
        let mut params = Vec::new();
        let mut param_index = 1;

        if let Some(workcenter) = &query.workcenter_dept_code {
            where_conditions.push(format!("t.Workcenter_DeptCode = @P{}", param_index));
            params.push(workcenter.clone());
            param_index += 1;
        }

        if let Some(keyword) = &query.keyword {
            where_conditions.push(format!("t.TeamName LIKE @P{}", param_index));
            params.push(format!("%{}%", keyword));
            param_index += 1;
        }

        if let Some(status) = query.status {
            where_conditions.push(format!("t.Status = @P{}", param_index));
            params.push(status.to_string());
        }

        let where_clause = if where_conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", where_conditions.join(" AND "))
        };

        // 查询总数
        let count_sql = format!(
            "SELECT COUNT(*) FROM Teams t {}",
            where_clause
        );

        let mut count_query = Query::new(count_sql);
        for param in &params {
            count_query.bind(param);
        }

        let count_result = count_query.query(&mut client).await?;
        let total = if let Some(row) = count_result.into_row().await? {
            row.get::<i32, _>(0).unwrap_or(0) as i64
        } else {
            0
        };

        // 查询数据
        let data_sql = format!(
            r#"
            SELECT 
                t.TeamID,
                t.TeamName,
                t.Workcenter_DeptCode,
                w.Description AS WorkcenterName,
                t.Description,
                t.Status,
                t.CreatedAt,
                t.UpdatedAt,
                t.CreatedBy,
                (SELECT COUNT(*) FROM TeamLeaders tl WHERE tl.TeamID = t.TeamID AND tl.IsActive = 1) AS LeaderCount,
                (SELECT COUNT(*) FROM TeamMembers tm WHERE tm.TeamID = t.TeamID AND tm.IsActive = 1) AS MemberCount
            FROM Teams t
            LEFT JOIN workcenter w ON t.Workcenter_DeptCode COLLATE SQL_Latin1_General_CP1_CI_AS = w.DeptCode COLLATE SQL_Latin1_General_CP1_CI_AS
            {}
            ORDER BY t.CreatedAt DESC
            OFFSET {} ROWS FETCH NEXT {} ROWS ONLY
            "#,
            where_clause, offset, page_size
        );

        let mut data_query = Query::new(data_sql);
        for param in &params {
            data_query.bind(param);
        }

        let data_result = data_query.query(&mut client).await?;
        let rows: Vec<_> = data_result.into_first_result().await?;

        let mut teams = Vec::new();
        for row in rows {
            let team_id = row.get::<i32, _>(0).unwrap_or(0);

            // 查询该班组的班长信息
            let leaders = self.get_team_leaders(team_id).await.unwrap_or_default();

            teams.push(TeamResponse {
                team_id,
                team_name: row.get::<&str, _>(1).unwrap_or("").to_string(),
                workcenter_dept_code: row.get::<&str, _>(2).unwrap_or("").to_string(),
                workcenter_name: row.get::<&str, _>(3).map(|s| s.to_string()),
                description: row.get::<&str, _>(4).map(|s| s.to_string()),
                status: row.get::<u8, _>(5).unwrap_or(1),
                leader_count: row.get::<i32, _>(9).unwrap_or(0),
                member_count: row.get::<i32, _>(10).unwrap_or(0),
                leaders, // 添加班长信息数组
                created_at: row.get::<chrono::DateTime<chrono::Utc>, _>(6)
                    .unwrap_or_else(|| Utc::now()),
                updated_at: row.get::<chrono::DateTime<chrono::Utc>, _>(7)
                    .unwrap_or_else(|| Utc::now()),
                created_by: row.get::<&str, _>(8).map(|s| s.to_string()),
            });
        }

        let total_pages = ((total as f64) / (page_size as f64)).ceil() as u32;

        Ok(TeamListResponse {
            teams,
            total,
            page,
            page_size,
            total_pages,
        })
    }

    /// 根据ID获取班组详情
    pub async fn get_team_by_id(&self, team_id: i32) -> AppResult<Option<TeamDetailResponse>> {
        let mut client = self.db_config.get_app_connection().await?;

        // 获取班组基本信息
        let team_sql = r#"
            SELECT 
                t.TeamID,
                t.TeamName,
                t.Workcenter_DeptCode,
                w.Description AS WorkcenterName,
                t.Description,
                t.Status,
                t.CreatedAt,
                t.UpdatedAt,
                t.CreatedBy,
                (SELECT COUNT(*) FROM TeamLeaders tl WHERE tl.TeamID = t.TeamID AND tl.IsActive = 1) AS LeaderCount,
                (SELECT COUNT(*) FROM TeamMembers tm WHERE tm.TeamID = t.TeamID AND tm.IsActive = 1) AS MemberCount
            FROM Teams t
            LEFT JOIN workcenter w ON t.Workcenter_DeptCode COLLATE SQL_Latin1_General_CP1_CI_AS = w.DeptCode COLLATE SQL_Latin1_General_CP1_CI_AS
            WHERE t.TeamID = @P1
        "#;

        let mut team_query = Query::new(team_sql);
        team_query.bind(team_id);
        let team_result = team_query.query(&mut client).await?;

        let team_row = if let Some(row) = team_result.into_row().await? {
            row
        } else {
            return Ok(None);
        };

        let team_id = team_row.get::<i32, _>(0).unwrap_or(0);
        let leaders = self.get_team_leaders(team_id).await.unwrap_or_default();

        let team = TeamResponse {
            team_id,
            team_name: team_row.get::<&str, _>(1).unwrap_or("").to_string(),
            workcenter_dept_code: team_row.get::<&str, _>(2).unwrap_or("").to_string(),
            workcenter_name: team_row.get::<&str, _>(3).map(|s| s.to_string()),
            description: team_row.get::<&str, _>(4).map(|s| s.to_string()),
            status: team_row.get::<u8, _>(5).unwrap_or(1),
            leader_count: team_row.get::<i32, _>(9).unwrap_or(0),
            member_count: team_row.get::<i32, _>(10).unwrap_or(0),
            leaders,
            created_at: team_row.get::<chrono::DateTime<chrono::Utc>, _>(6)
                .unwrap_or_else(|| Utc::now()),
            updated_at: team_row.get::<chrono::DateTime<chrono::Utc>, _>(7)
                .unwrap_or_else(|| Utc::now()),
            created_by: team_row.get::<&str, _>(8).map(|s| s.to_string()),
        };

        // 获取班长列表
        let leaders = self.get_team_leaders(team_id).await?;

        // 获取成员列表
        let members = self.get_team_members(team_id).await?;

        Ok(Some(TeamDetailResponse {
            team,
            leaders,
            members,
        }))
    }

    /// 创建新班组
    pub async fn create_team(&self, request: &CreateTeamRequest, created_by: &str) -> AppResult<i32> {
        let mut client = self.db_config.get_app_connection().await?;

        // 检查工作中心是否存在
        let workcenter_check_sql = "SELECT COUNT(*) FROM workcenter WHERE DeptCode = @P1";
        let mut workcenter_query = Query::new(workcenter_check_sql);
        workcenter_query.bind(&request.workcenter_dept_code);
        let workcenter_result = workcenter_query.query(&mut client).await?;

        if let Some(row) = workcenter_result.into_row().await? {
            let count = row.get::<i32, _>(0).unwrap_or(0);
            if count == 0 {
                return Err(AppError::Business("指定的工作中心不存在".to_string()));
            }
        }

        // 检查同一工作中心内班组名称是否重复
        let name_check_sql = "SELECT COUNT(*) FROM Teams WHERE Workcenter_DeptCode = @P1 AND TeamName = @P2";
        let mut name_query = Query::new(name_check_sql);
        name_query.bind(&request.workcenter_dept_code);
        name_query.bind(&request.team_name);
        let name_result = name_query.query(&mut client).await?;

        if let Some(row) = name_result.into_row().await? {
            let count = row.get::<i32, _>(0).unwrap_or(0);
            if count > 0 {
                return Err(AppError::Business("该工作中心内已存在同名班组".to_string()));
            }
        }

        // 插入新班组
        let insert_sql = r#"
            INSERT INTO Teams (TeamName, Workcenter_DeptCode, Description, Status, CreatedBy, CreatedAt, UpdatedAt)
            OUTPUT INSERTED.TeamID
            VALUES (@P1, @P2, @P3, 1, @P4, GETDATE(), GETDATE())
        "#;

        let mut insert_query = Query::new(insert_sql);
        insert_query.bind(&request.team_name);
        insert_query.bind(&request.workcenter_dept_code);
        insert_query.bind(request.description.as_deref());
        insert_query.bind(created_by);
        let insert_result = insert_query.query(&mut client).await?;

        if let Some(row) = insert_result.into_row().await? {
            let team_id = row.get::<i32, _>(0).unwrap_or(0);
            Ok(team_id)
        } else {
            Err(AppError::Database("创建班组失败".to_string()))
        }
    }

    /// 更新班组信息
    pub async fn update_team(&self, team_id: i32, request: &UpdateTeamRequest) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        // 检查班组是否存在
        let exists_sql = "SELECT COUNT(*) FROM Teams WHERE TeamID = @P1";
        let mut exists_query = Query::new(exists_sql);
        exists_query.bind(team_id);
        let exists_result = exists_query.query(&mut client).await?;

        if let Some(row) = exists_result.into_row().await? {
            let count = row.get::<i32, _>(0).unwrap_or(0);
            if count == 0 {
                return Err(AppError::Business("班组不存在".to_string()));
            }
        }

        // 构建更新语句
        let mut set_clauses = Vec::new();
        let mut params = Vec::new();
        let mut param_index = 1;

        if let Some(team_name) = &request.team_name {
            set_clauses.push(format!("TeamName = @P{}", param_index));
            params.push(team_name.clone());
            param_index += 1;
        }

        if let Some(description) = &request.description {
            set_clauses.push(format!("Description = @P{}", param_index));
            params.push(description.clone());
            param_index += 1;
        }

        if let Some(status) = request.status {
            set_clauses.push(format!("Status = @P{}", param_index));
            params.push(status.to_string());
            param_index += 1;
        }

        if set_clauses.is_empty() {
            return Ok(()); // 没有需要更新的字段
        }

        set_clauses.push(format!("UpdatedAt = GETDATE()"));

        let update_sql = format!(
            "UPDATE Teams SET {} WHERE TeamID = @P{}",
            set_clauses.join(", "),
            param_index
        );

        let mut update_query = Query::new(update_sql);
        for param in &params {
            update_query.bind(param);
        }
        update_query.bind(team_id);

        update_query.execute(&mut client).await?;
        Ok(())
    }

    /// 删除班组
    pub async fn delete_team(&self, team_id: i32) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        // 检查班组是否存在
        let exists_sql = "SELECT COUNT(*) FROM Teams WHERE TeamID = @P1";
        let mut exists_query = Query::new(exists_sql);
        exists_query.bind(team_id);
        let exists_result = exists_query.query(&mut client).await?;

        if let Some(row) = exists_result.into_row().await? {
            let count = row.get::<i32, _>(0).unwrap_or(0);
            if count == 0 {
                return Err(AppError::Business("班组不存在".to_string()));
            }
        }

        // 删除班组（级联删除会自动删除相关的班长和成员记录）
        let delete_sql = "DELETE FROM Teams WHERE TeamID = @P1";
        let mut delete_query = Query::new(delete_sql);
        delete_query.bind(team_id);
        delete_query.execute(&mut client).await?;

        Ok(())
    }

    /// 获取班组的班长列表
    pub async fn get_team_leaders(&self, team_id: i32) -> AppResult<Vec<TeamLeaderResponse>> {
        let mut client = self.db_config.get_app_connection().await?;

        let sql = r#"
            SELECT
                tl.TeamID,
                tl.Leader_psn_num,
                hp.cPsn_Name AS LeaderName,
                tl.StartDate,
                tl.EndDate,
                tl.IsActive,
                tl.CreatedAt
            FROM TeamLeaders tl
            INNER JOIN person hp ON tl.Leader_psn_num = hp.cpsn_num
            WHERE tl.TeamID = @P1 AND tl.IsActive = 1
            ORDER BY tl.CreatedAt DESC
        "#;

        let mut query = Query::new(sql);
        query.bind(team_id);
        let result = query.query(&mut client).await?;

        let rows: Vec<_> = result.into_first_result().await?;
        let mut leaders = Vec::new();
        for row in rows {
            leaders.push(TeamLeaderResponse {
                team_id: row.get::<i32, _>(0).unwrap_or(0),
                leader_psn_num: row.get::<&str, _>(1).unwrap_or("").to_string(),
                leader_name: row.get::<&str, _>(2).map(|s| s.to_string()),
                assigned_at: row.get::<chrono::NaiveDate, _>(3)
                    .map(|d| d.and_hms_opt(0, 0, 0).unwrap().and_utc())
                    .unwrap_or_else(|| Utc::now()),
                status: if row.get::<bool, _>(5).unwrap_or(false) { 1 } else { 0 },
            });
        }

        Ok(leaders)
    }

    /// 获取班组的成员列表
    pub async fn get_team_members(&self, team_id: i32) -> AppResult<Vec<TeamMemberResponse>> {
        let query = crate::models::team::TeamMemberQuery { member_name: None };
        self.get_team_members_with_query(team_id, &query).await
    }

    /// 获取班组的成员列表（支持查询条件）
    pub async fn get_team_members_with_query(&self, team_id: i32, query: &crate::models::team::TeamMemberQuery) -> AppResult<Vec<TeamMemberResponse>> {
        let mut client = self.db_config.get_app_connection().await?;

        // 构建查询条件
        let mut where_conditions = vec!["tm.TeamID = @P1".to_string(), "tm.IsActive = 1".to_string()];
        let mut params = vec![team_id.to_string()];
        let mut param_index = 2;

        // 添加姓名模糊查询条件
        if let Some(member_name) = &query.member_name {
            if !member_name.trim().is_empty() {
                where_conditions.push(format!("hp.cPsn_Name LIKE @P{}", param_index));
                params.push(format!("%{}%", member_name.trim()));
                param_index += 1;
            }
        }

        let where_clause = where_conditions.join(" AND ");

        let sql = format!(r#"
            SELECT
                tm.TeamID,
                tm.Member_psn_num,
                hp.cPsn_Name AS MemberName,
                tm.JoinDate,
                tm.LeaveDate,
                tm.IsActive,
                tm.CreatedAt
            FROM TeamMembers tm
            INNER JOIN person hp ON tm.Member_psn_num = hp.cpsn_num
            WHERE {}
            ORDER BY tm.CreatedAt DESC
        "#, where_clause);

        let mut query = Query::new(sql);
        // 绑定所有参数
        for param in params {
            query.bind(param);
        }
        let result = query.query(&mut client).await?;

        let rows: Vec<_> = result.into_first_result().await?;
        let mut members = Vec::new();
        for row in rows {
            members.push(TeamMemberResponse {
                team_id: row.get::<i32, _>(0).unwrap_or(0),
                member_psn_num: row.get::<&str, _>(1).unwrap_or("").to_string(),
                member_name: row.get::<&str, _>(2).map(|s| s.to_string()),
                joined_at: row.get::<chrono::NaiveDate, _>(3)
                    .map(|d| d.and_hms_opt(0, 0, 0).unwrap().and_utc())
                    .unwrap_or_else(|| Utc::now()),
                joined_by: None,
                joined_by_name: None,
                status: if row.get::<bool, _>(5).unwrap_or(false) { 1 } else { 0 },
            });
        }

        Ok(members)
    }

    /// 任命班长 - 包含角色协同功能
    pub async fn assign_leader(&self, team_id: i32, request: &AssignLeaderRequest, assigned_by: &str) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        // 检查班组是否存在
        let team_exists_sql = "SELECT COUNT(*) FROM Teams WHERE TeamID = @P1";
        let mut team_query = Query::new(team_exists_sql);
        team_query.bind(team_id);
        let team_result = team_query.query(&mut client).await?;

        if let Some(row) = team_result.into_row().await? {
            let count = row.get::<i32, _>(0).unwrap_or(0);
            if count == 0 {
                return Err(AppError::Business("班组不存在".to_string()));
            }
        }

        // 获取班组的工作中心
        let team_sql = "SELECT Workcenter_DeptCode FROM Teams WHERE TeamID = @P1";
        let mut team_query = Query::new(team_sql);
        team_query.bind(team_id);
        let team_result = team_query.query(&mut client).await?;

        let workcenter_dept_code = if let Some(row) = team_result.into_row().await? {
            row.get::<&str, _>(0).map(|s| s.to_string())
                .ok_or_else(|| AppError::Business("班组未关联工作中心".to_string()))?
        } else {
            return Err(AppError::Business("无法获取班组信息".to_string()));
        };

        // 检查人员是否存在且工作中心匹配
        let person_sql = "SELECT cDept_num FROM person WHERE cpsn_num = @P1";
        let mut person_query = Query::new(person_sql);
        person_query.bind(&request.leader_psn_num);
        let person_result = person_query.query(&mut client).await?;

        let person_dept_code = if let Some(row) = person_result.into_row().await? {
            row.get::<&str, _>(0).map(|s| s.to_string())
        } else {
            return Err(AppError::Validation("指定的人员不存在".to_string()));
        };

        // 验证人员的工作中心是否与班组的工作中心匹配
        if let Some(dept_code) = person_dept_code {
            if dept_code != workcenter_dept_code {
                return Err(AppError::Business(format!(
                    "人员工作中心({})与班组工作中心({})不匹配，无法任命为班长",
                    dept_code,
                    workcenter_dept_code
                )));
            }
        } else {
            return Err(AppError::Validation("人员未分配工作中心，无法任命为班长".to_string()));
        };

        // 检查是否已经是班长
        let leader_exists_sql = "SELECT COUNT(*) FROM TeamLeaders WHERE TeamID = @P1 AND Leader_psn_num = @P2 AND IsActive = 1";
        let mut leader_query = Query::new(leader_exists_sql);
        leader_query.bind(team_id);
        leader_query.bind(&request.leader_psn_num);
        let leader_result = leader_query.query(&mut client).await?;

        if let Some(row) = leader_result.into_row().await? {
            let count = row.get::<i32, _>(0).unwrap_or(0);
            if count > 0 {
                return Err(AppError::Validation("该人员已经是此班组的班长".to_string()));
            }
        }

        // 开始事务处理
        // 1. 检查并处理成员到班长的转换
        let was_member = self.promote_member_to_leader(team_id, &request.leader_psn_num, assigned_by).await?;
        if was_member {
            tracing::info!("用户 {} 已从班组 {} 的成员转换为班长", request.leader_psn_num, team_id);
        }

        // 2. 插入班长记录
        let insert_leader_sql = r#"
            INSERT INTO TeamLeaders (TeamID, Leader_psn_num, StartDate, IsActive, CreatedAt)
            VALUES (@P1, @P2, CAST(GETDATE() AS DATE), 1, GETDATE())
        "#;

        let mut insert_query = Query::new(insert_leader_sql);
        insert_query.bind(team_id);
        insert_query.bind(&request.leader_psn_num);
        insert_query.execute(&mut client).await?;

        // 2. 检查用户是否已经有 team_leader 角色
        let role_check_sql = r#"
            SELECT COUNT(*)
            FROM user_roles ur
            INNER JOIN roles r ON ur.role_id = r.id
            INNER JOIN person hp ON ur.user_id = hp.cpsn_num
            WHERE hp.cpsn_num = @P1 AND r.name = 'team_leader'
        "#;

        let mut role_check_query = Query::new(role_check_sql);
        role_check_query.bind(&request.leader_psn_num);
        let role_check_result = role_check_query.query(&mut client).await?;

        let has_team_leader_role = if let Some(row) = role_check_result.into_row().await? {
            row.get::<i32, _>(0).unwrap_or(0) > 0
        } else {
            false
        };

        // 3. 如果用户还没有 team_leader 角色，则自动分配
        if !has_team_leader_role {
            // 获取 team_leader 角色ID
            let get_role_id_sql = "SELECT id FROM roles WHERE name = 'team_leader'";
            let get_role_query = Query::new(get_role_id_sql);
            let role_result = get_role_query.query(&mut client).await?;

            if let Some(row) = role_result.into_row().await? {
                let role_id = row.get::<i64, _>(0).unwrap_or(0) as i32;

                // 分配 team_leader 角色
                let assign_role_sql = r#"
                    INSERT INTO user_roles (user_id, role_id)
                    VALUES (@P1, @P2)
                "#;

                let mut assign_role_query = Query::new(assign_role_sql);
                assign_role_query.bind(&request.leader_psn_num);
                assign_role_query.bind(role_id);
                assign_role_query.execute(&mut client).await?;

                tracing::info!(
                    "自动为用户 {} 分配了 team_leader 角色，因为被任命为班组 {} 的班长",
                    request.leader_psn_num, team_id
                );
            } else {
                tracing::warn!("未找到 team_leader 角色，无法自动分配角色给用户 {}", request.leader_psn_num);
            }
        }

        Ok(())
    }

    /// 解除班长任命 - 包含角色协同功能
    pub async fn remove_leader(&self, team_id: i32, leader_psn_num: &str) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        // 检查班长是否存在
        let exists_sql = r#"
            SELECT COUNT(*)
            FROM TeamLeaders tl
            INNER JOIN person hp ON tl.Leader_psn_num = hp.cpsn_num
            WHERE tl.TeamID = @P1 AND tl.Leader_psn_num = @P2 AND tl.IsActive = 1
        "#;
        let mut exists_query = Query::new(exists_sql);
        exists_query.bind(team_id);
        exists_query.bind(leader_psn_num);
        let exists_result = exists_query.query(&mut client).await?;

        if let Some(row) = exists_result.into_row().await? {
            let count = row.get::<i32, _>(0).unwrap_or(0);
            if count == 0 {
                return Err(AppError::Business("班长任命不存在".to_string()));
            }
        } else {
            return Err(AppError::Business("班长任命不存在".to_string()));
        };

        // 1. 更新状态为已解除
        let update_sql = "UPDATE TeamLeaders SET IsActive = 0, EndDate = CAST(GETDATE() AS DATE) WHERE TeamID = @P1 AND Leader_psn_num = @P2";
        let mut update_query = Query::new(update_sql);
        update_query.bind(team_id);
        update_query.bind(leader_psn_num);
        update_query.execute(&mut client).await?;

        // 2. 检查用户是否还在其他班组担任班长
        let other_leader_sql = r#"
            SELECT COUNT(*)
            FROM TeamLeaders tl
            WHERE tl.Leader_psn_num = @P1 AND tl.IsActive = 1
        "#;

        let mut other_leader_query = Query::new(other_leader_sql);
        other_leader_query.bind(leader_psn_num);
        let other_leader_result = other_leader_query.query(&mut client).await?;

        let still_leader_elsewhere = if let Some(row) = other_leader_result.into_row().await? {
            row.get::<i32, _>(0).unwrap_or(0) > 0
        } else {
            false
        };

        // 3. 如果用户不再担任任何班组的班长，考虑移除 team_leader 角色
        if !still_leader_elsewhere {
            // 检查用户是否有 team_leader 角色
            let role_check_sql = r#"
                SELECT ur.user_role_id, r.id
                FROM user_roles ur
                INNER JOIN roles r ON ur.role_id = r.id
                WHERE ur.user_id = @P1 AND r.name = 'team_leader'
            "#;

            let mut role_check_query = Query::new(role_check_sql);
            role_check_query.bind(leader_psn_num);
            let role_check_result = role_check_query.query(&mut client).await?;

            if let Some(row) = role_check_result.into_row().await? {
                let user_role_id = row.get::<i32, _>(0).unwrap_or(0);

                // 移除 team_leader 角色
                let remove_role_sql = "DELETE FROM user_roles WHERE user_role_id = @P1";
                let mut remove_role_query = Query::new(remove_role_sql);
                remove_role_query.bind(user_role_id);
                remove_role_query.execute(&mut client).await?;

                tracing::info!(
                    "自动移除了用户 {} 的 team_leader 角色，因为不再担任任何班组的班长",
                    leader_psn_num
                );
            }
        }

        Ok(())
    }

    /// 添加班组成员
    pub async fn add_member(&self, team_id: i32, request: &AddMemberRequest, _joined_by: &str) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        // 获取班组的工作中心
        let team_sql = "SELECT Workcenter_DeptCode FROM Teams WHERE TeamID = @P1";
        let mut team_query = Query::new(team_sql);
        team_query.bind(team_id);
        let team_result = team_query.query(&mut client).await?;

        let workcenter_dept_code = if let Some(row) = team_result.into_row().await? {
            row.get::<&str, _>(0).map(|s| s.to_string())
                .ok_or_else(|| AppError::Business("班组未关联工作中心".to_string()))?
        } else {
            return Err(AppError::Business("班组不存在".to_string()));
        };

        // 检查人员是否存在且工作中心匹配
        let person_sql = "SELECT cDept_num FROM person WHERE cpsn_num = @P1";
        let mut person_query = Query::new(person_sql);
        person_query.bind(&request.member_psn_num);
        let person_result = person_query.query(&mut client).await?;

        let person_dept_code = if let Some(row) = person_result.into_row().await? {
            row.get::<&str, _>(0).map(|s| s.to_string())
        } else {
            return Err(AppError::Validation("指定的人员不存在".to_string()));
        };

        // 验证人员的工作中心是否与班组的工作中心匹配
        if let Some(dept_code) = person_dept_code {
            if dept_code != workcenter_dept_code {
                return Err(AppError::Business(format!(
                    "人员工作中心({})与班组工作中心({})不匹配，无法添加为班组成员",
                    dept_code,
                    workcenter_dept_code
                )));
            }
        } else {
            return Err(AppError::Validation("人员未分配工作中心，无法添加为班组成员".to_string()));
        }

        // 检查用户角色，避免角色错乱
        let role_check_sql = r#"
            SELECT r.name
            FROM user_roles ur
            INNER JOIN roles r ON ur.role_id = r.id
            WHERE ur.user_id = @P1 AND r.name IN ('team_leader', 'manager')
        "#;

        let mut role_check_query = Query::new(role_check_sql);
        role_check_query.bind(&request.member_psn_num);
        let role_result = role_check_query.query(&mut client).await?;

        if let Some(row) = role_result.into_row().await? {
            let role_name: &str = row.get(0).unwrap_or("");
            return Err(AppError::Validation(
                format!("该人员具有 {} 角色，不能添加为普通班组成员。请使用相应的管理接口进行操作", role_name)
            ));
        }

        // 检查是否已经是其他班组的成员
        let member_exists_sql = "SELECT COUNT(*) FROM TeamMembers WHERE Member_psn_num = @P1 AND IsActive = 1";
        let mut member_query = Query::new(member_exists_sql);
        member_query.bind(&request.member_psn_num);
        let member_result = member_query.query(&mut client).await?;

        if let Some(row) = member_result.into_row().await? {
            let count = row.get::<i32, _>(0).unwrap_or(0);
            if count > 0 {
                return Err(AppError::Validation("该人员已经是其他班组的成员".to_string()));
            }
        }

        // 插入成员记录
        let insert_sql = r#"
            INSERT INTO TeamMembers (TeamID, Member_psn_num, JoinDate, IsActive, CreatedAt)
            VALUES (@P1, @P2, CAST(GETDATE() AS DATE), 1, GETDATE())
        "#;

        let mut insert_query = Query::new(insert_sql);
        insert_query.bind(team_id);
        insert_query.bind(&request.member_psn_num);
        insert_query.execute(&mut client).await?;

        Ok(())
    }

    /// 移除班组成员
    pub async fn remove_member(&self, team_id: i32, member_psn_num: &str) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        // 检查成员是否存在
        let exists_sql = "SELECT COUNT(*) FROM TeamMembers WHERE TeamID = @P1 AND Member_psn_num = @P2 AND IsActive = 1";
        let mut exists_query = Query::new(exists_sql);
        exists_query.bind(team_id);
        exists_query.bind(member_psn_num);
        let exists_result = exists_query.query(&mut client).await?;

        if let Some(row) = exists_result.into_row().await? {
            let count = row.get::<i32, _>(0).unwrap_or(0);
            if count == 0 {
                return Err(AppError::Business("班组成员不存在".to_string()));
            }
        }

        // 检查是否有未完成的指令卡（不包括已完成状态）
        let instruction_card_sql = r#"
            SELECT COUNT(*) FROM InstructionCards
            WHERE assigned_person = @P1
            AND status IN (0, 1, 2)
        "#;
        let mut instruction_query = Query::new(instruction_card_sql);
        instruction_query.bind(member_psn_num);
        let instruction_result = instruction_query.query(&mut client).await?;

        if let Some(row) = instruction_result.into_row().await? {
            let count = row.get::<i32, _>(0).unwrap_or(0);
            if count > 0 {
                return Err(AppError::Business(format!(
                    "该人员有 {} 个未完成的指令卡，无法移除", count
                )));
            }
        }

        // 检查是否正在被其他班组借用
        let borrowed_sql = r#"
            SELECT COUNT(*) FROM TeamMemberBorrows
            WHERE Member_psn_num = @P1
            AND BorrowStatus IN (0, 1)
        "#;
        let mut borrowed_query = Query::new(borrowed_sql);
        borrowed_query.bind(member_psn_num);
        let borrowed_result = borrowed_query.query(&mut client).await?;

        if let Some(row) = borrowed_result.into_row().await? {
            let count = row.get::<i32, _>(0).unwrap_or(0);
            if count > 0 {
                return Err(AppError::Business("该人员正在被其他班组借用，无法移除".to_string()));
            }
        }

        // 检查是否正在借用其他班组人员
        // let borrowing_sql = r#"
        //     SELECT COUNT(*) FROM TeamMemberBorrows
        //     WHERE BorrowTeamID = @P1
        //     AND BorrowStatus IN (0, 1)
        //     AND EXISTS (
        //         SELECT 1 FROM TeamMembers tm
        //         WHERE tm.Member_psn_num = @P2
        //         AND tm.TeamID = @P1
        //         AND tm.IsActive = 1
        //     )
        // "#;
        // let mut borrowing_query = Query::new(borrowing_sql);
        // borrowing_query.bind(team_id);
        // borrowing_query.bind(member_psn_num);
        // let borrowing_result = borrowing_query.query(&mut client).await?;

        // if let Some(row) = borrowing_result.into_row().await? {
        //     let count = row.get::<i32, _>(0).unwrap_or(0);
        //     if count > 0 {
        //         return Err(AppError::Business("该人员所在班组正在借用其他班组人员，无法移除".to_string()));
        //     }
        // }

        // 更新状态为已移除
        let update_sql = "UPDATE TeamMembers SET IsActive = 0, LeaveDate = CAST(GETDATE() AS DATE) WHERE TeamID = @P1 AND Member_psn_num = @P2";
        let mut update_query = Query::new(update_sql);
        update_query.bind(team_id);
        update_query.bind(member_psn_num);
        update_query.execute(&mut client).await?;

        Ok(())
    }

    /// 获取用户的班组信息
    pub async fn get_user_team_info(&self, psn_num: &str) -> AppResult<Option<UserTeamInfoResponse>> {
        let mut client = self.db_config.get_app_connection().await?;

        // 检查用户是否存在
        let user_sql = r#"
            SELECT hp.cpsn_num, hp.cPsn_Name, hp.cDept_num, w.Description AS WorkcenterName
            FROM person hp
            LEFT JOIN workcenter w ON hp.cDept_num = w.DeptCode
            WHERE hp.cpsn_num = @P1
        "#;

        let mut user_query = Query::new(user_sql);
        user_query.bind(psn_num);
        let user_result = user_query.query(&mut client).await?;

        let user_row = if let Some(row) = user_result.into_row().await? {
            row
        } else {
            return Ok(None);
        };

        let person_name = user_row.get::<&str, _>(1).unwrap_or("").to_string();
        let workcenter_dept_code = user_row.get::<&str, _>(2).map(|s| s.to_string());
        let workcenter_name = user_row.get::<&str, _>(3).map(|s| s.to_string());

        // 查找用户作为成员的班组
        let member_sql = r#"
            SELECT
                t.TeamID, t.TeamName, t.Workcenter_DeptCode, w.Description AS WorkcenterName,
                t.Description, t.Status, t.CreatedAt, t.UpdatedAt, t.CreatedBy,
                ISNULL(leader_stats.LeaderCount, 0) AS LeaderCount,
                ISNULL(member_stats.MemberCount, 0) AS MemberCount
            FROM TeamMembers tm
            INNER JOIN Teams t ON tm.TeamID = t.TeamID
            LEFT JOIN workcenter w ON t.Workcenter_DeptCode COLLATE SQL_Latin1_General_CP1_CI_AS = w.DeptCode COLLATE SQL_Latin1_General_CP1_CI_AS
            LEFT JOIN (
                SELECT TeamID, COUNT(*) AS LeaderCount
                FROM TeamLeaders
                WHERE IsActive = 1
                GROUP BY TeamID
            ) leader_stats ON t.TeamID = leader_stats.TeamID
            LEFT JOIN (
                SELECT TeamID, COUNT(*) AS MemberCount
                FROM TeamMembers
                WHERE IsActive = 1
                GROUP BY TeamID
            ) member_stats ON t.TeamID = member_stats.TeamID
            WHERE tm.Member_psn_num = @P1 AND tm.IsActive = 1
        "#;

        let mut member_query = Query::new(member_sql);
        member_query.bind(psn_num);
        let member_result = member_query.query(&mut client).await?;

        let member_of_team = if let Some(row) = member_result.into_row().await? {
            let team_id = row.get::<i32, _>(0).unwrap_or(0);
            let leaders = self.get_team_leaders(team_id).await.unwrap_or_default();

            Some(TeamResponse {
                team_id,
                team_name: row.get::<&str, _>(1).unwrap_or("").to_string(),
                workcenter_dept_code: row.get::<&str, _>(2).unwrap_or("").to_string(),
                workcenter_name: row.get::<&str, _>(3).map(|s| s.to_string()),
                description: row.get::<&str, _>(4).map(|s| s.to_string()),
                status: row.get::<u8, _>(5).unwrap_or(1),
                leader_count: row.get::<i32, _>(9).unwrap_or(0),
                member_count: row.get::<i32, _>(10).unwrap_or(0),
                leaders,
                created_at: row.get::<chrono::DateTime<chrono::Utc>, _>(6)
                    .unwrap_or_else(|| Utc::now()),
                updated_at: row.get::<chrono::DateTime<chrono::Utc>, _>(7)
                    .unwrap_or_else(|| Utc::now()),
                created_by: row.get::<&str, _>(8).map(|s| s.to_string()),
            })
        } else {
            None
        };

        // 查找用户作为班长的班组
        let leader_sql = r#"
            SELECT
                t.TeamID, t.TeamName, t.Workcenter_DeptCode, w.Description AS WorkcenterName,
                t.Description, t.Status, t.CreatedAt, t.UpdatedAt, t.CreatedBy,
                ISNULL(leader_stats.LeaderCount, 0) AS LeaderCount,
                ISNULL(member_stats.MemberCount, 0) AS MemberCount
            FROM TeamLeaders tl
            INNER JOIN Teams t ON tl.TeamID = t.TeamID
            LEFT JOIN workcenter w ON t.Workcenter_DeptCode COLLATE SQL_Latin1_General_CP1_CI_AS = w.DeptCode COLLATE SQL_Latin1_General_CP1_CI_AS
            LEFT JOIN (
                SELECT TeamID, COUNT(*) AS LeaderCount
                FROM TeamLeaders
                WHERE IsActive = 1
                GROUP BY TeamID
            ) leader_stats ON t.TeamID = leader_stats.TeamID
            LEFT JOIN (
                SELECT TeamID, COUNT(*) AS MemberCount
                FROM TeamMembers
                WHERE IsActive = 1
                GROUP BY TeamID
            ) member_stats ON t.TeamID = member_stats.TeamID
            WHERE tl.Leader_psn_num = @P1 AND tl.IsActive = 1
        "#;

        let mut leader_query = Query::new(leader_sql);
        leader_query.bind(psn_num);
        let leader_result = leader_query.query(&mut client).await?;

        let rows: Vec<_> = leader_result.into_first_result().await?;
        let mut leader_of_teams = Vec::new();
        for row in rows {
            let team_id = row.get::<i32, _>(0).unwrap_or(0);
            let leaders = self.get_team_leaders(team_id).await.unwrap_or_default();

            leader_of_teams.push(TeamResponse {
                team_id,
                team_name: row.get::<&str, _>(1).unwrap_or("").to_string(),
                workcenter_dept_code: row.get::<&str, _>(2).unwrap_or("").to_string(),
                workcenter_name: row.get::<&str, _>(3).map(|s| s.to_string()),
                description: row.get::<&str, _>(4).map(|s| s.to_string()),
                status: row.get::<u8, _>(5).unwrap_or(1),
                leader_count: row.get::<i32, _>(9).unwrap_or(0),
                member_count: row.get::<i32, _>(10).unwrap_or(0),
                leaders,
                created_at: row.get::<chrono::DateTime<chrono::Utc>, _>(6)
                    .unwrap_or_else(|| Utc::now()),
                updated_at: row.get::<chrono::DateTime<chrono::Utc>, _>(7)
                    .unwrap_or_else(|| Utc::now()),
                created_by: row.get::<&str, _>(8).map(|s| s.to_string()),
            });
        }

        Ok(Some(UserTeamInfoResponse {
            psn_num: psn_num.to_string(),
            person_name,
            member_of_team,
            leader_of_teams,
            workcenter_dept_code,
            workcenter_name,
        }))
    }

    /// 获取用户的角色和班组协同状态
    pub async fn get_user_role_team_status(&self, psn_num: &str) -> AppResult<Option<UserRoleTeamStatusResponse>> {
        let mut client = self.db_config.get_app_connection().await?;

        // 检查用户是否存在，并获取基本信息
        let user_sql = r#"
            SELECT hp.cpsn_num, hp.cPsn_Name, hp.cDept_num, w.Description AS WorkcenterName
            FROM person hp
            LEFT JOIN workcenter w ON hp.cDept_num = w.DeptCode
            WHERE hp.cpsn_num = @P1
        "#;

        let mut user_query = Query::new(user_sql);
        user_query.bind(psn_num);
        let user_result = user_query.query(&mut client).await?;

        let person_name = if let Some(row) = user_result.into_row().await? {
            row.get::<&str, _>(1).unwrap_or("").to_string()
        } else {
            return Ok(None);
        };

        // 检查用户是否有 team_leader 角色
        let role_sql = r#"
            SELECT r.name
            FROM user_roles ur
            INNER JOIN roles r ON ur.role_id = r.id
            WHERE ur.user_id = @P1
        "#;

        let mut role_query = Query::new(role_sql);
        role_query.bind(psn_num);
        let role_result = role_query.query(&mut client).await?;

        let rows: Vec<_> = role_result.into_first_result().await?;
        let mut roles = Vec::new();
        let mut has_team_leader_role = false;

        for row in rows {
            let role_name = row.get::<&str, _>(0).unwrap_or("").to_string();
            if role_name == "team_leader" {
                has_team_leader_role = true;
            }
            roles.push(role_name);
        }

        // 获取用户班组信息
        let user_team_info = self.get_user_team_info(psn_num).await?;
        let (leader_of_teams, member_of_team) = if let Some(info) = user_team_info {
            (info.leader_of_teams, info.member_of_team)
        } else {
            (Vec::new(), None)
        };

        Ok(Some(UserRoleTeamStatusResponse {
            psn_num: psn_num.to_string(),
            person_name,
            user_id: 0, // 不再使用 user_id
            has_team_leader_role,
            leader_of_teams,
            member_of_team,
            roles,
        }))
    }

    /// 根据工作中心获取班组列表
    pub async fn get_teams_by_workcenter(&self, workcenter_dept_code: &str) -> AppResult<Vec<TeamResponse>> {
        let mut client = self.db_config.get_app_connection().await?;

        let sql = r#"
            SELECT
                t.TeamID,
                t.TeamName,
                t.Workcenter_DeptCode,
                w.Description AS WorkcenterName,
                t.Description,
                t.Status,
                t.CreatedAt,
                t.UpdatedAt,
                t.CreatedBy,
                (SELECT COUNT(*) FROM TeamLeaders tl WHERE tl.TeamID = t.TeamID AND tl.IsActive = 1) AS LeaderCount,
                (SELECT COUNT(*) FROM TeamMembers tm WHERE tm.TeamID = t.TeamID AND tm.IsActive = 1) AS MemberCount
            FROM Teams t
            LEFT JOIN workcenter w ON t.Workcenter_DeptCode COLLATE SQL_Latin1_General_CP1_CI_AS = w.DeptCode COLLATE SQL_Latin1_General_CP1_CI_AS
            WHERE t.Workcenter_DeptCode = @P1 AND t.Status = 1
            ORDER BY t.TeamName
        "#;

        let mut query = Query::new(sql);
        query.bind(workcenter_dept_code);
        let result = query.query(&mut client).await?;

        let rows: Vec<_> = result.into_first_result().await?;
        let mut teams = Vec::new();
        for row in rows {
            let team_id = row.get::<i32, _>(0).unwrap_or(0);
            let leaders = self.get_team_leaders(team_id).await.unwrap_or_default();

            teams.push(TeamResponse {
                team_id,
                team_name: row.get::<&str, _>(1).unwrap_or("").to_string(),
                workcenter_dept_code: row.get::<&str, _>(2).unwrap_or("").to_string(),
                workcenter_name: row.get::<&str, _>(3).map(|s| s.to_string()),
                description: row.get::<&str, _>(4).map(|s| s.to_string()),
                status: row.get::<u8, _>(5).unwrap_or(1),
                leader_count: row.get::<i32, _>(9).unwrap_or(0),
                member_count: row.get::<i32, _>(10).unwrap_or(0),
                leaders,
                created_at: row.get::<chrono::DateTime<chrono::Utc>, _>(6)
                    .unwrap_or_else(|| Utc::now()),
                updated_at: row.get::<chrono::DateTime<chrono::Utc>, _>(7)
                    .unwrap_or_else(|| Utc::now()),
                created_by: row.get::<&str, _>(8).map(|s| s.to_string()),
            });
        }

        Ok(teams)
    }

    /// 统一任命班组长（一步到位：角色分配 + 班组任命）
    pub async fn unified_assign_leader(&self, request: &UnifiedAssignLeaderRequest, assigned_by: &str) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        // 1. 验证工作中心是否存在
        let workcenter_check_sql = "SELECT COUNT(*) FROM workcenter WHERE DeptCode = @P1";
        let mut workcenter_query = Query::new(workcenter_check_sql);
        workcenter_query.bind(&request.workcenter_dept_code);
        let workcenter_result = workcenter_query.query(&mut client).await?;

        if let Some(row) = workcenter_result.into_row().await? {
            let count = row.get::<i32, _>(0).unwrap_or(0);
            if count == 0 {
                return Err(AppError::Validation("指定的工作中心不存在".to_string()));
            }
        }

        // 2. 验证班组是否存在且属于指定工作中心
        let team_check_sql = "SELECT COUNT(*) FROM Teams WHERE TeamID = @P1 AND Workcenter_DeptCode = @P2";
        let mut team_query = Query::new(team_check_sql);
        team_query.bind(request.team_id);
        team_query.bind(&request.workcenter_dept_code);
        let team_result = team_query.query(&mut client).await?;

        if let Some(row) = team_result.into_row().await? {
            let count = row.get::<i32, _>(0).unwrap_or(0);
            if count == 0 {
                return Err(AppError::Validation("指定的班组不存在或不属于该工作中心".to_string()));
            }
        }

        // 3. 验证人员是否存在且工作中心匹配
        let person_sql = "SELECT cDept_num FROM person WHERE cpsn_num = @P1";
        let mut person_query = Query::new(person_sql);
        person_query.bind(&request.leader_psn_num);
        let person_result = person_query.query(&mut client).await?;

        let person_dept_code = if let Some(row) = person_result.into_row().await? {
            row.get::<&str, _>(0).map(|s| s.to_string())
        } else {
            return Err(AppError::Validation("指定的人员不存在".to_string()));
        };

        // 验证人员的工作中心是否与班组的工作中心匹配
        if let Some(dept_code) = person_dept_code {
            if dept_code != request.workcenter_dept_code {
                return Err(AppError::Business(format!(
                    "人员工作中心({})与班组工作中心({})不匹配，无法任命为班长",
                    dept_code,
                    request.workcenter_dept_code
                )));
            }
        } else {
            return Err(AppError::Validation("人员未分配工作中心，无法任命为班长".to_string()));
        }

        // 4. 检查是否已经是该班组的班长
        let leader_exists_sql = "SELECT COUNT(*) FROM TeamLeaders WHERE TeamID = @P1 AND Leader_psn_num = @P2 AND IsActive = 1";
        let mut leader_query = Query::new(leader_exists_sql);
        leader_query.bind(request.team_id);
        leader_query.bind(&request.leader_psn_num);
        let leader_result = leader_query.query(&mut client).await?;

        if let Some(row) = leader_result.into_row().await? {
            let count = row.get::<i32, _>(0).unwrap_or(0);
            if count > 0 {
                return Err(AppError::Validation("该人员已经是此班组的班长".to_string()));
            }
        }

        // 5. 检查并处理成员到班长的转换
        let was_member = self.promote_member_to_leader(request.team_id, &request.leader_psn_num, assigned_by).await?;
        if was_member {
            tracing::info!("统一任命：用户 {} 已从班组 {} 的成员转换为班长", request.leader_psn_num, request.team_id);
        }

        // 6. 插入班长记录
        let insert_leader_sql = r#"
            INSERT INTO TeamLeaders (TeamID, Leader_psn_num, StartDate, IsActive, CreatedAt)
            VALUES (@P1, @P2, CAST(GETDATE() AS DATE), 1, GETDATE())
        "#;

        let mut insert_query = Query::new(insert_leader_sql);
        insert_query.bind(request.team_id);
        insert_query.bind(&request.leader_psn_num);
        insert_query.execute(&mut client).await?;

        // 6. 检查用户是否已经有 team_leader 角色
        let role_check_sql = r#"
            SELECT COUNT(*)
            FROM user_roles ur
            INNER JOIN roles r ON ur.role_id = r.id
            WHERE ur.user_id = @P1 AND r.name = 'team_leader'
        "#;

        let mut role_check_query = Query::new(role_check_sql);
        role_check_query.bind(&request.leader_psn_num);
        let role_check_result = role_check_query.query(&mut client).await?;

        let has_team_leader_role = if let Some(row) = role_check_result.into_row().await? {
            row.get::<i32, _>(0).unwrap_or(0) > 0
        } else {
            false
        };

        // 7. 如果用户还没有 team_leader 角色，则自动分配
        if !has_team_leader_role {
            // 获取 team_leader 角色ID
            let get_role_id_sql = "SELECT id FROM roles WHERE name = 'team_leader'";
            let get_role_query = Query::new(get_role_id_sql);
            let role_result = get_role_query.query(&mut client).await?;

            if let Some(row) = role_result.into_row().await? {
                let role_id = row.get::<i64, _>(0).unwrap_or(0) as i32;

                // 分配 team_leader 角色
                let assign_role_sql = r#"
                    INSERT INTO user_roles (user_id, role_id)
                    VALUES (@P1, @P2)
                "#;

                let mut assign_role_query = Query::new(assign_role_sql);
                assign_role_query.bind(&request.leader_psn_num);
                assign_role_query.bind(role_id);
                assign_role_query.execute(&mut client).await?;

                tracing::info!(
                    "统一任命：为用户 {} 分配了 team_leader 角色并任命为班组 {} 的班长",
                    request.leader_psn_num, request.team_id
                );

                // 将 User角色尝试删除，无论成功与否都执行一次清理
                let remove_user_role_sql = r#"
                    DELETE FROM user_roles
                    WHERE user_id = @P1
                    AND role_id = (SELECT id FROM roles WHERE name = 'user')
                "#;

                let mut remove_user_role_query = Query::new(remove_user_role_sql);
                remove_user_role_query.bind(&request.leader_psn_num);
                remove_user_role_query.execute(&mut client).await?;

                tracing::info!(
                    "统一任命：尝试移除了用户 {} 的 user 角色",
                    request.leader_psn_num
                );
            } else {
                tracing::warn!("未找到 team_leader 角色，无法自动分配角色给用户 {}", request.leader_psn_num);
            }
        } else {
            tracing::info!(
                "统一任命：用户 {} 已有 team_leader 角色，直接任命为班组 {} 的班长",
                request.leader_psn_num, request.team_id
            );
        }

        Ok(())
    }

    /// 统一移除班组长（一步到位：移除所有班组任命 + 角色回收）
    pub async fn unified_remove_leader(&self, leader_psn_num: &str) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        // 1. 获取用户ID和当前担任班长的班组信息
        let user_info_sql = r#"
            SELECT DISTINCT hp.cpsn_num, hp.cPsn_Name
            FROM person hp
            WHERE hp.cpsn_num = @P1
        "#;

        let mut user_query = Query::new(user_info_sql);
        user_query.bind(leader_psn_num);
        let user_result = user_query.query(&mut client).await?;

        let person_name = if let Some(row) = user_result.into_row().await? {
            row.get::<&str, _>(1).unwrap_or("").to_string()
        } else {
            return Err(AppError::Validation("指定的人员不存在".to_string()));
        };

        // 2. 检查用户当前担任班长的班组
        let current_teams_sql = r#"
            SELECT t.TeamID, t.TeamName
            FROM TeamLeaders tl
            INNER JOIN Teams t ON tl.TeamID = t.TeamID
            WHERE tl.Leader_psn_num = @P1 AND tl.IsActive = 1
        "#;

        let mut teams_query = Query::new(current_teams_sql);
        teams_query.bind(leader_psn_num);
        let teams_result = teams_query.query(&mut client).await?;

        let rows: Vec<_> = teams_result.into_first_result().await?;
        if rows.is_empty() {
            return Err(AppError::Validation("该人员当前没有担任任何班组的班长".to_string()));
        }

        let mut team_names = Vec::new();
        for row in &rows {
            let team_name = row.get::<&str, _>(1).unwrap_or("").to_string();
            team_names.push(team_name);
        }

        // 3. 移除所有班组长任命
        let remove_all_leaders_sql = "UPDATE TeamLeaders SET IsActive = 0, EndDate = CAST(GETDATE() AS DATE) WHERE Leader_psn_num = @P1 AND IsActive = 1";
        let mut remove_query = Query::new(remove_all_leaders_sql);
        remove_query.bind(leader_psn_num);
        remove_query.execute(&mut client).await?;

        // 4. 移除 team_leader 角色
        let remove_role_sql = r#"
            DELETE FROM user_roles WHERE user_id = @P1
        "#;

        let mut remove_role_query = Query::new(remove_role_sql);
        remove_role_query.bind(leader_psn_num);
        remove_role_query.execute(&mut client).await?;

        tracing::info!(
            "统一移除：移除了用户 {} ({}) 的所有班组长任命（班组：{}）和 team_leader 角色",
            person_name, leader_psn_num, team_names.join(", ")
        );

        Ok(())
    }

    /// 获取所有活跃的班组成员列表（用于零活录入指派）
    pub async fn get_all_active_team_members(&self) -> AppResult<Vec<SimpleTeamMemberResponse>> {
        let mut client = self.db_config.get_app_connection().await?;

        let query_sql = r#"
            SELECT DISTINCT
                tm.Member_psn_num,
                p.cPsn_Name as member_name,
                t.TeamID,
                t.TeamName,
                wc.Description as work_center_name
            FROM TeamMembers tm
            INNER JOIN Teams t ON tm.TeamID = t.TeamID
            INNER JOIN person p ON tm.Member_psn_num = p.cpsn_num
            LEFT JOIN workcenter wc ON p.cDept_num = wc.DeptCode
            WHERE tm.IsActive = 1
            ORDER BY t.TeamName, p.cPsn_Name
        "#;

        let query = Query::new(query_sql);
        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        let mut members = Vec::new();
        for row in rows {
            let member = SimpleTeamMemberResponse {
                member_psn_num: row.get::<&str, _>(0).unwrap_or("").to_string(),
                member_name: row.get::<&str, _>(1).map(|s| s.to_string()),
                team_id: row.get::<i32, _>(2).unwrap_or(0),
                team_name: row.get::<&str, _>(3).map(|s| s.to_string()),
                work_center_name: row.get::<&str, _>(4).map(|s| s.to_string()),
            };
            members.push(member);
        }

        Ok(members)
    }

    /// 获取班级内且未被借出的可用成员列表
    pub async fn get_available_team_members(
        &self,
        query: &crate::models::team::AvailableTeamMemberQuery,
    ) -> AppResult<crate::models::team::AvailableTeamMemberPageResponse> {
        let mut client = self.db_config.get_app_connection().await?;

        let page = query.page.unwrap_or(1);
        let page_size = query.page_size.unwrap_or(20).min(100);
        let offset = (page - 1) * page_size;

        // 构建查询条件
        let mut where_conditions = vec![
            // "tm.TeamID = @P1".to_string(),
            "tm.IsActive = 1".to_string(),
        ];
        let mut params = vec![];
        let mut param_index = 1;

        if let Some(team_id) = &query.team_id{
            where_conditions.push(format!("tm.TeamID = @P{}", param_index));
            params.push(team_id.to_string());
            param_index += 1;
        }

        // 工作中心筛选
        if let Some(work_center) = &query.work_center {
            if !work_center.trim().is_empty() {
                where_conditions.push(format!("w.Description LIKE @P{}", param_index));
                params.push(format!("%{}%", work_center.trim()));
                param_index += 1;
            }
        }

        // 成员姓名模糊查询
        if let Some(member_name) = &query.member_name {
            if !member_name.trim().is_empty() {
                where_conditions.push(format!("p.cPsn_Name LIKE @P{}", param_index));
                params.push(format!("%{}%", member_name.trim()));
                param_index += 1;
            }
        }

        // 关键词模糊查询（姓名或ID）
        if let Some(keyword) = &query.keyword {
            if !keyword.trim().is_empty() {
                where_conditions.push(format!(
                    "(p.cPsn_Name LIKE @P{} OR p.cpsn_num LIKE @P{})",
                    param_index, param_index + 1
                ));
                params.push(format!("%{}%", keyword.trim()));
                params.push(format!("%{}%", keyword.trim()));
                // param_index += 2; // 移除：这是最后一次使用，不需要再递增
            }
        }

        // 排除正在被借出的成员（状态为1=已同意/借用中）
        where_conditions.push(
            "NOT EXISTS (
                SELECT 1 FROM TeamMemberBorrows tmb
                WHERE tmb.member_psn_num COLLATE Chinese_PRC_CI_AS = tm.Member_psn_num COLLATE Chinese_PRC_CI_AS
                AND tmb.BorrowStatus = 1
            )".to_string()
        );

        let where_clause = format!("WHERE {}", where_conditions.join(" AND "));

        // 查询总数
        let source_db = &self.db_config.get_source_database_name();
        let count_sql = format!(
            "SELECT COUNT(*) as total
             FROM TeamMembers tm
             LEFT JOIN person p ON tm.Member_psn_num COLLATE Chinese_PRC_CI_AS = p.cpsn_num COLLATE Chinese_PRC_CI_AS
             LEFT JOIN {}.dbo.workcenter w ON p.cDept_num COLLATE Chinese_PRC_CI_AS = w.DeptCode COLLATE Chinese_PRC_CI_AS
             LEFT JOIN Teams t ON tm.TeamID = t.TeamID
             {}",
            source_db, where_clause
        );

        let mut count_query = Query::new(&count_sql);
        for param in &params {
            count_query.bind(param);
        }

        let count_result = count_query.query(&mut client).await?;
        let count_rows: Vec<_> = count_result.into_first_result().await?;
        let total = count_rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0) as u32;

        // 查询数据
        let data_sql = format!(
            "SELECT
                tm.Member_psn_num, p.cPsn_Name, tm.TeamID, t.TeamName,
                w.Description as work_center_name, tm.JoinDate
             FROM TeamMembers tm
             LEFT JOIN person p ON tm.Member_psn_num COLLATE Chinese_PRC_CI_AS = p.cpsn_num COLLATE Chinese_PRC_CI_AS
             LEFT JOIN {}.dbo.workcenter w ON p.cDept_num COLLATE Chinese_PRC_CI_AS = w.DeptCode COLLATE Chinese_PRC_CI_AS
             LEFT JOIN Teams t ON tm.TeamID = t.TeamID
             {}
             ORDER BY tm.JoinDate DESC
             OFFSET {} ROWS FETCH NEXT {} ROWS ONLY",
            source_db, where_clause, offset, page_size
        );

        let mut data_query = Query::new(&data_sql);
        for param in &params {
            data_query.bind(param);
        }

        let data_result = data_query.query(&mut client).await?;
        let data_rows: Vec<_> = data_result.into_first_result().await?;

        let mut members = Vec::new();
        for row in data_rows {
            members.push(crate::models::team::AvailableTeamMemberResponse {
                cpsn_num: row.get::<&str, _>(0).unwrap_or("").to_string(),
                name: row.get::<&str, _>(1).unwrap_or("").to_string(),
                team_id: row.get::<i32, _>(2).unwrap_or(0),
                team_name: row.get::<&str, _>(3).unwrap_or("").to_string(),
                work_center_name: row.get::<&str, _>(4).map(|s| s.to_string()),
                joined_at: row.get::<chrono::NaiveDate, _>(5)
                    .map(|d| d.and_hms_opt(0, 0, 0).unwrap().and_utc())
                    .unwrap_or_else(|| Utc::now()),
            });
        }

        let total_pages = (total + page_size - 1) / page_size;

        Ok(crate::models::team::AvailableTeamMemberPageResponse {
            items: members,
            total,
            page,
            page_size,
            total_pages,
        })
    }
}
