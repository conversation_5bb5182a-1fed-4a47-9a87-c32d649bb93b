use crate::config::database::DatabaseConfig;
use crate::models::product::{Product, ProductQueryRequest, ProductPageResponse};
use crate::utils::{AppError, AppResult};
use std::sync::Arc;
use tiberius::Query;

pub struct ProductService {
    db_config: Arc<DatabaseConfig>,
}

impl ProductService {
    pub fn new(db_config: Arc<DatabaseConfig>) -> Self {
        Self { db_config }
    }

    /// 分页查询产品列表
    pub async fn get_products(&self, request: ProductQueryRequest) -> AppResult<ProductPageResponse> {
        let mut client = self.db_config.get_source_connection().await?;

        // 构建WHERE条件
        let (where_clause, params) = self.build_where_clause(&request);
        
        // 查询总数
        let count_sql = format!(
            "SELECT COUNT(*) as total FROM inventory {}",
            where_clause
        );
        
        let mut count_query = Query::new(&count_sql);
        self.bind_parameters(&mut count_query, &params);
        
        let count_stream = count_query.query(&mut *client).await?;
        let count_rows: Vec<_> = count_stream.into_first_result().await?;
        let total = count_rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0) as u32;

        // 查询数据
        let data_sql = format!(
            "SELECT Cinvcode, Cinvname, cInvStd FROM inventory {} ORDER BY Cinvcode OFFSET {} ROWS FETCH NEXT {} ROWS ONLY",
            where_clause,
            request.get_offset(),
            request.get_page_size()
        );

        let mut data_query = Query::new(&data_sql);
        self.bind_parameters(&mut data_query, &params);
        
        let data_stream = data_query.query(&mut *client).await?;
        let data_rows: Vec<_> = data_stream.into_first_result().await?;

        let mut products = Vec::new();
        for row in data_rows {
            let product = Product {
                cinvcode: row.get::<&str, _>(0).unwrap_or("").to_string(),
                cinvname: row.get::<&str, _>(1).map(|s| s.to_string()),
                cinv_std: row.get::<&str, _>(2).map(|s| s.to_string()),
            };
            products.push(product);
        }

        let page = request.get_page();
        let page_size = request.get_page_size();
        let total_pages = if total == 0 { 0 } else { (total + page_size - 1) / page_size };

        Ok(ProductPageResponse {
            items: products,
            total,
            page,
            page_size,
            total_pages,
        })
    }

    /// 根据物料编码获取单个产品
    pub async fn get_product_by_code(&self, cinvcode: &str) -> AppResult<Product> {
        let mut client = self.db_config.get_source_connection().await?;

        let mut query = Query::new(
            "SELECT Cinvcode, Cinvname, cInvStd FROM inventory WHERE Cinvcode = @P1"
        );
        query.bind(cinvcode);

        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        if let Some(row) = rows.first() {
            Ok(Product {
                cinvcode: row.get::<&str, _>(0).unwrap_or("").to_string(),
                cinvname: row.get::<&str, _>(1).map(|s| s.to_string()),
                cinv_std: row.get::<&str, _>(2).map(|s| s.to_string()),
            })
        } else {
            Err(AppError::Business("产品不存在".to_string()))
        }
    }

    /// 构建WHERE条件
    fn build_where_clause(&self, request: &ProductQueryRequest) -> (String, Vec<String>) {
        let mut conditions = Vec::new();
        let mut params = Vec::new();
        let mut param_index = 1;

        // 处理具体字段查询
        if let Some(cinvcode) = &request.cinvcode {
            conditions.push(format!("Cinvcode LIKE @P{}", param_index));
            params.push(format!("%{}%", cinvcode));
            param_index += 1;
        }

        if let Some(cinvname) = &request.cinvname {
            conditions.push(format!("Cinvname LIKE @P{}", param_index));
            params.push(format!("%{}%", cinvname));
            param_index += 1;
        }

        if let Some(cinv_std) = &request.cinv_std {
            conditions.push(format!("cInvStd LIKE @P{}", param_index));
            params.push(format!("%{}%", cinv_std));
            param_index += 1;
        }

        // 处理全字段模糊查询
        if let Some(keyword) = &request.keyword {
            let keyword_conditions = vec![
                format!("Cinvcode LIKE @P{}", param_index),
                format!("Cinvname LIKE @P{}", param_index + 1),
                format!("cInvStd LIKE @P{}", param_index + 2),
            ];
            conditions.push(format!("({})", keyword_conditions.join(" OR ")));
            
            // 为每个字段添加相同的关键词参数
            for _ in 0..3 {
                params.push(format!("%{}%", keyword));
            }
        }

        let where_clause = if conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", conditions.join(" AND "))
        };

        (where_clause, params)
    }

    /// 绑定查询参数
    fn bind_parameters<'a>(&self, query: &mut Query<'a>, params: &'a [String]) {
        for param in params {
            query.bind(param);
        }
    }
}
