use crate::config::database::DatabaseConfig;
use crate::models::android_upgrade_config::*;
use crate::utils::AppResult;
use chrono::Utc;
use tiberius::{Query, Row};

pub struct AndroidUpgradeConfigService {
    db_config: DatabaseConfig,
}

impl AndroidUpgradeConfigService {
    pub fn new(db_config: DatabaseConfig) -> Self {
        Self { db_config }
    }

    /// 获取所有安卓升级配置（分页）
    pub async fn get_configs(
        &self,
        request: AndroidUpgradeConfigQueryRequest,
    ) -> AppResult<AndroidUpgradeConfigPageResponse> {
        let mut client = self.db_config.get_app_connection().await?;

        // 查询总数
        let count_sql = "SELECT COUNT(*) FROM android_upgrade_configs";
        let count_query = Query::new(count_sql);
        let count_stream = count_query.query(&mut *client).await?;
        let count_rows: Vec<_> = count_stream.into_first_result().await?;
        let total = count_rows
            .first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0) as u32;

        // 查询数据
        let data_sql = format!(
            r#"
            SELECT id, config_json
            FROM android_upgrade_configs
            ORDER BY id DESC
            OFFSET {} ROWS FETCH NEXT {} ROWS ONLY
            "#,
            request.get_offset(),
            request.get_page_size()
        );

        let data_query = Query::new(&data_sql);
        let data_stream = data_query.query(&mut *client).await?;
        let data_rows: Vec<_> = data_stream.into_first_result().await?;

        let mut items = Vec::new();
        for row in data_rows {
            items.push(self.row_to_config_response(&row)?);
        }

        let total_pages = if total == 0 {
            0
        } else {
            (total + request.get_page_size() - 1) / request.get_page_size()
        };

        Ok(AndroidUpgradeConfigPageResponse {
            items,
            total,
            page: request.get_page(),
            page_size: request.get_page_size(),
            total_pages,
        })
    }

    /// 创建安卓升级配置
    pub async fn create_config(
        &self,
        request: CreateAndroidUpgradeConfigRequest,
    ) -> AppResult<AndroidUpgradeConfigResponse> {
        let mut client = self.db_config.get_app_connection().await?;

        let sql = r#"
            INSERT INTO android_upgrade_configs (config_json)
            OUTPUT INSERTED.id, INSERTED.config_json
            VALUES (@P1)
        "#;

        let mut query = Query::new(sql);
        query.bind(&request.config_json);

        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        if let Some(row) = rows.first() {
            Ok(self.row_to_config_response(row)?)
        } else {
            Err(crate::utils::AppError::Database(
                "创建安卓升级配置失败".to_string(),
            ))
        }
    }

    /// 获取最新的安卓升级配置
    pub async fn get_latest_config(&self) -> AppResult<Option<AndroidUpgradeConfigResponse>> {
        let mut client = self.db_config.get_app_connection().await?;

        let sql = r#"
            SELECT TOP 1 id, config_json
            FROM android_upgrade_configs
            ORDER BY id DESC
        "#;

        let query = Query::new(sql);
        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        if let Some(row) = rows.first() {
            Ok(Some(self.row_to_config_response(row)?))
        } else {
            Ok(None)
        }
    }

    /// 删除安卓升级配置
    pub async fn delete_config(&self, id: i32) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        let sql = "DELETE FROM android_upgrade_configs WHERE id = @P1";
        let mut query = Query::new(sql);
        query.bind(id);

        let result = query.execute(&mut *client).await?;

        if result.rows_affected().len() > 0 && result.rows_affected()[0] > 0 {
            Ok(())
        } else {
            Err(crate::utils::AppError::NotFound(
                "安卓升级配置不存在".to_string(),
            ))
        }
    }

    /// 将数据库行转换为配置响应
    fn row_to_config_response(&self, row: &Row) -> AppResult<AndroidUpgradeConfigResponse> {
        let id: i32 = row.get(0).unwrap_or(0);
        let config_json: &str = row.get(1).unwrap_or("");

        // 处理创建时间 - 使用当前时间作为默认值
        let created_at = Utc::now();

        Ok(AndroidUpgradeConfigResponse {
            id,
            config_json: config_json.to_string(),
            created_at,
        })
    }
}
