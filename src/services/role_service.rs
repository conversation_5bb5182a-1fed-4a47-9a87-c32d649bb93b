// 角色服务
use tiberius::Query;
use crate::config::DatabaseConfig;
use crate::models::{Role, CreateRoleRequest, UpdateRoleRequest, AssignRoleRequest, User, UserResponse};
use crate::utils::{AppResult, AppError, PaginatedResponse};
use chrono::Utc;

pub struct RoleService {
    db_config: DatabaseConfig,
}

impl RoleService {
    pub fn new(db_config: DatabaseConfig) -> Self {
        Self { db_config }
    }

    // 获取所有角色
    pub async fn get_roles(&self) -> AppResult<Vec<Role>> {
        let mut client = self.db_config.get_app_connection().await?;

        let query = Query::new(
            "SELECT id, name, description, level, permissions
             FROM roles ORDER BY id"
        );

        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        let roles: Vec<Role> = rows.iter().map(|row| {
            Role {
                id: row.get::<i64, _>(0).unwrap_or(0),
                name: row.get::<&str, _>(1).unwrap_or("").to_string(),
                description: row.get::<&str, _>(2).map(|s| s.to_string()),
                level: row.get::<i32, _>(3).unwrap_or(4), // 默认为普通用户级别
                permissions: row.get::<&str, _>(4).map(|s| s.to_string()), // 获取权限字段
                created_at: Utc::now(), // 使用当前时间
                updated_at: Utc::now(), // 使用当前时间
            }
        }).collect();

        Ok(roles)
    }

    // 根据ID获取角色
    pub async fn get_role_by_id(&self, role_id: i64) -> AppResult<Role> {
        let mut client = self.db_config.get_app_connection().await?;

        let mut query = Query::new(
            "SELECT id, name, description, level, permissions
             FROM roles WHERE id = @P1"
        );
        query.bind(role_id);

        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        if let Some(row) = rows.first() {
            let role = Role {
                id: row.get::<i64, _>(0).unwrap_or(0),
                name: row.get::<&str, _>(1).unwrap_or("").to_string(),
                description: row.get::<&str, _>(2).map(|s| s.to_string()),
                level: row.get::<i32, _>(3).unwrap_or(4), // 默认为普通用户级别
                permissions: row.get::<&str, _>(4).map(|s| s.to_string()), // 获取权限字段
                created_at: Utc::now(), // 使用当前时间
                updated_at: Utc::now(), // 使用当前时间
            };
            Ok(role)
        } else {
            Err(AppError::Business("角色不存在".to_string()))
        }
    }

    // 创建角色
    pub async fn create_role(&self, request: &CreateRoleRequest) -> AppResult<Role> {
        let mut client = self.db_config.get_app_connection().await?;

        // 检查角色名是否已存在
        let check_query = Query::new("SELECT COUNT(*) FROM roles WHERE name = @P1");
        let mut check_query = check_query;
        check_query.bind(&request.name);
        let check_stream = check_query.query(&mut *client).await?;
        let check_rows: Vec<_> = check_stream.into_first_result().await?;
        let exists = check_rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0) > 0;

        if exists {
            return Err(AppError::Validation("角色名已存在".to_string()));
        }

        // 验证权限是否存在
        if let Some(permissions) = &request.permissions {
            self.validate_permissions(permissions).await?;
        }

        // 序列化权限
        let permissions_json = if let Some(permissions) = &request.permissions {
            serde_json::to_string(permissions).map_err(|e| AppError::Validation(format!("权限序列化失败: {}", e)))?
        } else {
            "{}".to_string()
        };

        // 插入新角色
        let insert_query = Query::new(
            "INSERT INTO roles (name, description, permissions)
             OUTPUT INSERTED.id
             VALUES (@P1, @P2, @P3)"
        );
        let mut insert_query = insert_query;
        insert_query.bind(&request.name);
        insert_query.bind(request.description.as_deref().unwrap_or(""));
        insert_query.bind(&permissions_json);

        let insert_stream = insert_query.query(&mut *client).await?;
        let insert_rows: Vec<_> = insert_stream.into_first_result().await?;
        let role_id = insert_rows.first()
            .and_then(|row| row.get::<i64, _>(0))
            .ok_or_else(|| AppError::Database("创建角色失败".to_string()))?;

        // 返回创建的角色
        self.get_role_by_id(role_id).await
    }

    // 更新角色
    pub async fn update_role(&self, role_id: i64, request: &UpdateRoleRequest) -> AppResult<Role> {
        let mut client = self.db_config.get_app_connection().await?;

        let mut update_fields = Vec::new();
        let mut params = Vec::new();
        let mut param_index = 1;

        if let Some(name) = &request.name {
            // 检查新名称是否与其他角色冲突
            let check_query = Query::new("SELECT COUNT(*) FROM roles WHERE name = @P1 AND id != @P2");
            let mut check_query = check_query;
            check_query.bind(name);
            check_query.bind(role_id);
            let check_stream = check_query.query(&mut *client).await?;
            let check_rows: Vec<_> = check_stream.into_first_result().await?;
            let exists = check_rows.first()
                .and_then(|row| row.get::<i32, _>(0))
                .unwrap_or(0) > 0;

            if exists {
                return Err(AppError::Validation("角色名已存在".to_string()));
            }

            update_fields.push(format!("name = @P{}", param_index));
            params.push(name.clone());
            param_index += 1;
        }

        if let Some(description) = &request.description {
            update_fields.push(format!("description = @P{}", param_index));
            params.push(description.clone());
            param_index += 1;
        }

        if let Some(permissions) = &request.permissions {
            let permissions_json = serde_json::to_string(permissions).map_err(|e| AppError::Validation(format!("权限序列化失败: {}", e)))?;
            update_fields.push(format!("permissions = @P{}", param_index));
            params.push(permissions_json);
            param_index += 1;
        }

        if update_fields.is_empty() {
            return Err(AppError::Validation("没有要更新的字段".to_string()));
        }

        update_fields.push("updated_at = GETDATE()".to_string());

        let update_sql = format!(
            "UPDATE roles SET {} WHERE id = @P{}",
            update_fields.join(", "),
            param_index
        );

        let mut update_query = Query::new(&update_sql);
        for param in &params {
            update_query.bind(param);
        }
        update_query.bind(role_id);

        update_query.execute(&mut *client).await?;

        // 返回更新后的角色
        self.get_role_by_id(role_id).await
    }

    // 删除角色
    pub async fn delete_role(&self, role_id: i64) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        // 检查是否有用户使用此角色
        let check_query = Query::new("SELECT COUNT(*) FROM user_roles WHERE role_id = @P1");
        let mut check_query = check_query;
        check_query.bind(role_id);
        let check_stream = check_query.query(&mut *client).await?;
        let check_rows: Vec<_> = check_stream.into_first_result().await?;
        let user_count = check_rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0);

        if user_count > 0 {
            return Err(AppError::Business("该角色正在被用户使用，无法删除".to_string()));
        }

        // 删除角色
        let delete_query = Query::new("DELETE FROM roles WHERE id = @P1");
        let mut delete_query = delete_query;
        delete_query.bind(role_id);

        let result = delete_query.execute(&mut *client).await?;
        if result.rows_affected().len() == 0 || result.rows_affected()[0] == 0 {
            return Err(AppError::Business("角色不存在".to_string()));
        }

        Ok(())
    }

    // 直接分配角色给用户（用于系统内部调用）
    pub async fn assign_role_to_user(&self, user_id: &str, role_id: i64) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        // 检查用户是否存在
        let user_check = Query::new("SELECT COUNT(*) FROM person WHERE cpsn_num = @P1");
        let mut user_check = user_check;
        user_check.bind(user_id);
        let user_stream = user_check.query(&mut *client).await?;
        let user_rows: Vec<_> = user_stream.into_first_result().await?;

        let user_exists = user_rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0) > 0;

        if !user_exists {
            return Err(AppError::Business("用户不存在".to_string()));
        }

        // 检查角色是否存在
        let role_check = Query::new("SELECT COUNT(*) FROM roles WHERE id = @P1");
        let mut role_check = role_check;
        role_check.bind(role_id);
        let role_stream = role_check.query(&mut *client).await?;
        let role_rows: Vec<_> = role_stream.into_first_result().await?;

        let role_exists = role_rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0) > 0;

        if !role_exists {
            return Err(AppError::Business("角色不存在".to_string()));
        }

        // 检查是否已经分配了该角色
        let existing_check = Query::new("SELECT COUNT(*) FROM user_roles WHERE user_id = @P1 AND role_id = @P2");
        let mut existing_check = existing_check;
        existing_check.bind(user_id);
        existing_check.bind(role_id);
        let existing_stream = existing_check.query(&mut *client).await?;
        let existing_rows: Vec<_> = existing_stream.into_first_result().await?;

        let already_assigned = existing_rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0) > 0;

        if already_assigned {
            return Ok(()); // 已经分配了，直接返回成功
        }

        // 分配角色
        let assign_query = Query::new("INSERT INTO user_roles (user_id, role_id) VALUES (@P1, @P2)");
        let mut assign_query = assign_query;
        assign_query.bind(user_id);
        assign_query.bind(role_id);
        assign_query.execute(&mut *client).await?;

        Ok(())
    }

    // 分配角色给用户（API接口调用）
    // 实现一对一关系：分配新角色时自动移除旧角色
    pub async fn assign_role(&self, request: &AssignRoleRequest) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        // 检查用户是否存在，同时检查是否为admin用户
        let user_check = Query::new("SELECT COUNT(*), MAX(CAST(is_admin AS INT)) FROM person WHERE cpsn_num = @P1");
        let mut user_check = user_check;
        user_check.bind(&request.user_id);
        let user_stream = user_check.query(&mut *client).await?;
        let user_rows: Vec<_> = user_stream.into_first_result().await?;

        if let Some(row) = user_rows.first() {
            let user_exists = row.get::<i32, _>(0).unwrap_or(0) > 0;
            let is_admin = row.get::<i32, _>(1).unwrap_or(0) > 0;

            if !user_exists {
                return Err(AppError::Business("用户不存在".to_string()));
            }

            if is_admin {
                return Err(AppError::Business("不能为admin用户分配角色".to_string()));
            }
        } else {
            return Err(AppError::Business("用户不存在".to_string()));
        }

        // 检查角色是否存在
        let role_check = Query::new("SELECT COUNT(*) FROM roles WHERE id = @P1");
        let mut role_check = role_check;
        role_check.bind(request.role_id);
        let role_stream = role_check.query(&mut *client).await?;
        let role_rows: Vec<_> = role_stream.into_first_result().await?;
        let role_exists = role_rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0) > 0;

        if !role_exists {
            return Err(AppError::Business("角色不存在".to_string()));
        }

        // 检查用户当前是否已有角色
        let current_role_check = Query::new("SELECT role_id FROM user_roles WHERE user_id = @P1");
        let mut current_role_check = current_role_check;
        current_role_check.bind(&request.user_id);
        let current_role_stream = current_role_check.query(&mut *client).await?;
        let current_role_rows: Vec<_> = current_role_stream.into_first_result().await?;

        if let Some(current_role_row) = current_role_rows.first() {
            let current_role_id: i64 = current_role_row.get(0).unwrap_or(0);

            // 如果用户已有相同角色，直接返回成功
            if current_role_id == request.role_id {
                tracing::info!("用户 {} 已拥有角色 {}，无需重复分配", request.user_id, request.role_id);
                return Ok(());
            }

            // 用户已有不同角色，先移除旧角色
            tracing::info!("用户 {} 当前角色: {}，将替换为角色: {}", request.user_id, current_role_id, request.role_id);
            let remove_old_query = Query::new("DELETE FROM user_roles WHERE user_id = @P1");
            let mut remove_old_query = remove_old_query;
            remove_old_query.bind(&request.user_id);
            remove_old_query.execute(&mut *client).await?;

            tracing::info!("已移除用户 {} 的旧角色 {}", request.user_id, current_role_id);
        } else {
            tracing::info!("用户 {} 当前无角色，直接分配角色 {}", request.user_id, request.role_id);
        }

        // 分配新角色 (不包含assigned_at字段，因为表中可能没有这个字段)
        let assign_query = Query::new(
            "INSERT INTO user_roles (user_id, role_id) VALUES (@P1, @P2)"
        );
        let mut assign_query = assign_query;
        assign_query.bind(&request.user_id);
        assign_query.bind(request.role_id);
        assign_query.execute(&mut *client).await?;

        tracing::info!("成功为用户 {} 分配角色 {}", request.user_id, request.role_id);
        Ok(())
    }

    // 移除用户角色
    pub async fn remove_user_role(&self, user_id: String, role_id: i64) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        // 首先检查用户角色关系是否存在
        let check_query = Query::new(
            "SELECT COUNT(*) FROM user_roles WHERE user_id = @P1 AND role_id = @P2"
        );
        let mut check_query = check_query;
        check_query.bind(&user_id);
        check_query.bind(role_id);
        let check_stream = check_query.query(&mut *client).await?;
        let check_rows: Vec<_> = check_stream.into_first_result().await?;
        let exists = check_rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0) > 0;

        if !exists {
            return Err(AppError::Business("用户角色关系不存在".to_string()));
        }

        // 删除用户角色关系
        let remove_query = Query::new(
            "DELETE FROM user_roles WHERE user_id = @P1 AND role_id = @P2"
        );
        let mut remove_query = remove_query;
        remove_query.bind(&user_id);
        remove_query.bind(role_id);
        remove_query.execute(&mut *client).await?;

        // 检查用户是否还有其他角色
        let remaining_roles_query = Query::new(
            "SELECT COUNT(*) FROM user_roles WHERE user_id = @P1"
        );
        let mut remaining_roles_query = remaining_roles_query;
        remaining_roles_query.bind(&user_id);
        let remaining_stream = remaining_roles_query.query(&mut *client).await?;
        let remaining_rows: Vec<_> = remaining_stream.into_first_result().await?;
        let remaining_count = remaining_rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0);

        // 如果用户没有其他角色了，自动分配默认User角色
        if remaining_count == 0 {
            // 获取默认User角色的ID
            match self.get_default_user_role_id().await {
                Ok(default_role_id) => {
                    // 分配默认User角色
                    if let Err(e) = self.assign_role_to_user(&user_id, default_role_id).await {
                        tracing::warn!("为用户 {} 自动分配默认User角色失败: {}", user_id, e);
                        // 不返回错误，因为主要操作（删除角色）已经成功
                    } else {
                        tracing::info!("用户 {} 的角色被移除后，已自动分配默认User角色", user_id);
                    }
                }
                Err(e) => {
                    tracing::warn!("获取默认User角色ID失败: {}", e);
                    // 不返回错误，因为主要操作（删除角色）已经成功
                }
            }
        }

        Ok(())
    }

    // 获取默认User角色的ID
    async fn get_default_user_role_id(&self) -> AppResult<i64> {
        let mut client = self.db_config.get_app_connection().await?;

        let mut query = Query::new("SELECT id FROM roles WHERE name = @P1");
        query.bind("user");
        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        if let Some(row) = rows.first() {
            Ok(row.get::<i64, _>(0).unwrap_or(0))
        } else {
            Err(AppError::Database("默认User角色不存在".to_string()))
        }
    }

    // 获取用户的所有角色
    pub async fn get_user_roles(&self, user_id: String) -> AppResult<Vec<Role>> {
        let mut client = self.db_config.get_app_connection().await?;

        let query = Query::new(
            "SELECT r.id, r.name, r.description, r.level, r.permissions
             FROM roles r
             INNER JOIN user_roles ur ON r.id = ur.role_id
             WHERE ur.user_id = @P1
             ORDER BY r.name"
        );
        let mut query = query;
        query.bind(user_id);

        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        let roles: Vec<Role> = rows.iter().map(|row| {
            Role {
                id: row.get::<i64, _>(0).unwrap_or(0),
                name: row.get::<&str, _>(1).unwrap_or("").to_string(),
                description: row.get::<&str, _>(2).map(|s| s.to_string()),
                level: row.get::<i32, _>(3).unwrap_or(4), // 默认为普通用户级别
                permissions: row.get::<&str, _>(4).map(|s| s.to_string()), // 获取权限字段
                created_at: Utc::now(), // 使用当前时间
                updated_at: Utc::now(), // 使用当前时间
            }
        }).collect();

        Ok(roles)
    }

    // 验证权限是否存在
    async fn validate_permissions(&self, permissions: &serde_json::Value) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        // 解析权限配置
        let permission_config: serde_json::Value = permissions.clone();

        // 如果是权限数组格式
        if let Some(permission_array) = permission_config.as_array() {
            for permission in permission_array {
                if let Some(resource) = permission.get("resource").and_then(|r| r.as_str()) {
                    // 跳过通配符权限
                    if resource == "*" {
                        continue;
                    }

                    // 检查资源是否存在
                    let check_resource_query = Query::new(
                        "SELECT COUNT(*) FROM permission_definitions WHERE resource_name = @P1"
                    );
                    let mut check_resource_query = check_resource_query;
                    check_resource_query.bind(resource);
                    let resource_stream = check_resource_query.query(&mut *client).await?;
                    let resource_rows: Vec<_> = resource_stream.into_first_result().await?;
                    let resource_exists = resource_rows.first()
                        .and_then(|row| row.get::<i32, _>(0))
                        .unwrap_or(0) > 0;

                    if !resource_exists {
                        return Err(AppError::Validation(format!("权限资源 '{}' 不存在", resource)));
                    }

                    // 检查操作是否存在
                    if let Some(actions) = permission.get("actions").and_then(|a| a.as_array()) {
                        for action in actions {
                            if let Some(action_str) = action.as_str() {
                                // 跳过通配符操作
                                if action_str == "*" {
                                    continue;
                                }

                                // 检查操作是否存在
                                let check_action_query = Query::new(
                                    "SELECT COUNT(*) FROM permission_actions pa
                                     INNER JOIN permission_definitions pd ON pa.permission_id = pd.id
                                     WHERE pd.resource_name = @P1 AND pa.action_name = @P2 AND pa.is_active = 1"
                                );
                                let mut check_action_query = check_action_query;
                                check_action_query.bind(resource);
                                check_action_query.bind(action_str);
                                let action_stream = check_action_query.query(&mut *client).await?;
                                let action_rows: Vec<_> = action_stream.into_first_result().await?;
                                let action_exists = action_rows.first()
                                    .and_then(|row| row.get::<i32, _>(0))
                                    .unwrap_or(0) > 0;

                                if !action_exists {
                                    return Err(AppError::Validation(format!("权限资源 '{}' 不支持操作 '{}'", resource, action_str)));
                                }
                            }
                        }
                    }
                }
            }
        }

        Ok(())
    }

    // 根据角色ID获取用户列表
    pub async fn get_users_by_role(&self, role_id: i64, page: Option<i32>, page_size: Option<i32>) -> AppResult<PaginatedResponse<UserResponse>> {
        let mut client = self.db_config.get_app_connection().await?;

        let page = page.unwrap_or(1);
        let page_size = page_size.unwrap_or(10);
        let offset = (page - 1) * page_size;

        // 首先验证角色是否存在
        let role_check_query = Query::new("SELECT COUNT(*) FROM roles WHERE id = @P1");
        let mut role_check = role_check_query;
        role_check.bind(role_id);
        let role_stream = role_check.query(&mut *client).await?;
        let role_rows: Vec<_> = role_stream.into_first_result().await?;
        let role_exists = role_rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0) > 0;

        if !role_exists {
            return Err(AppError::Validation("角色不存在".to_string()));
        }

        // 查询拥有该角色的用户总数
        let count_sql = "
            SELECT COUNT(DISTINCT u.cpsn_num) as total
            FROM person u
            INNER JOIN user_roles ur ON u.cpsn_num COLLATE SQL_Latin1_General_CP1_CI_AS = ur.user_id COLLATE SQL_Latin1_General_CP1_CI_AS
            WHERE ur.role_id = @P1
        ";

        let mut count_query = Query::new(count_sql);
        count_query.bind(role_id);
        let count_stream = count_query.query(&mut *client).await?;
        let count_rows: Vec<_> = count_stream.into_first_result().await?;
        let total = count_rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0) as i64;

        // 查询用户数据
        let source_db = &self.db_config.get_source_database_name();
        let data_sql = format!("
            SELECT DISTINCT u.cpsn_num, u.cPsn_Name, u.rEmployState, u.rSex, u.cDept_num,
                   u.username, u.password_hash, u.status, u.is_admin, u.last_login_at,
                   u.created_at, u.updated_at, w.Description
            FROM person u
            INNER JOIN user_roles ur ON u.cpsn_num COLLATE SQL_Latin1_General_CP1_CI_AS = ur.user_id COLLATE SQL_Latin1_General_CP1_CI_AS
            LEFT JOIN {}.dbo.workcenter w ON u.cDept_num COLLATE SQL_Latin1_General_CP1_CI_AS = w.DeptCode COLLATE SQL_Latin1_General_CP1_CI_AS
            WHERE ur.role_id = @P1
            ORDER BY u.created_at DESC
            OFFSET @P2 ROWS FETCH NEXT @P3 ROWS ONLY
        ", source_db);

        let mut data_query = Query::new(data_sql);
        data_query.bind(role_id);
        data_query.bind(offset);
        data_query.bind(page_size);

        let data_stream = data_query.query(&mut *client).await?;
        let data_rows: Vec<_> = data_stream.into_first_result().await?;

        // 创建用户响应列表，并为每个用户查询角色信息
        let mut users: Vec<UserResponse> = Vec::new();

        for row in data_rows.iter() {
            let user = User {
                cpsn_num: row.get::<&str, _>(0).unwrap_or("").to_string(),
                name: row.get::<&str, _>(1).unwrap_or("").to_string(),
                employ_state: row.get::<&str, _>(2).unwrap_or("").to_string(),
                sex: Some(self.normalize_gender(row.get::<&str, _>(3))),
                dept_num: row.get::<&str, _>(4).map(|s| s.to_string()),
                username: self.get_username_from_row(&row).unwrap_or_default(),
                password_hash: None, // 不返回密码哈希
                status: row.get::<i32, _>(7).unwrap_or(1),
                is_admin: row.get::<bool, _>(8).unwrap_or(false),
                last_login_at: row.get::<chrono::DateTime<chrono::Utc>, _>(9),
                created_at: row.get::<chrono::DateTime<chrono::Utc>, _>(10),
                updated_at: row.get::<chrono::DateTime<chrono::Utc>, _>(11),
            };

            let mut user_response = UserResponse::from(user);
            // 设置工作中心名称
            user_response.work_center_name = row.get::<&str, _>(12).map(|s| s.to_string());

            // 为每个用户查询角色信息
            match self.get_user_roles(user_response.cpsn_num.clone()).await {
                Ok(user_roles) => {
                    if user_roles.is_empty() {
                        // 如果用户没有角色，返回默认User角色
                        user_response.roles = Some(vec![self.create_default_user_role_response()]);
                    } else {
                        let role_responses: Vec<crate::models::role::RoleResponse> = user_roles.into_iter()
                            .map(crate::models::role::RoleResponse::from)
                            .collect();
                        user_response.roles = Some(role_responses);
                    }
                }
                Err(_) => {
                    // 如果查询失败，返回默认User角色
                    user_response.roles = Some(vec![self.create_default_user_role_response()]);
                }
            }

            users.push(user_response);
        }

        Ok(PaginatedResponse::new(users, total, page, page_size))
    }

    // 辅助方法：标准化性别字段
    fn normalize_gender(&self, gender: Option<&str>) -> String {
        match gender {
            Some("男") | Some("M") | Some("Male") | Some("1") => "男".to_string(),
            Some("女") | Some("F") | Some("Female") | Some("0") => "女".to_string(),
            _ => "未知".to_string(),
        }
    }

    // 辅助方法：从行中获取用户名
    fn get_username_from_row(&self, row: &tiberius::Row) -> Option<String> {
        row.get::<&str, _>(5).map(|s| s.to_string())
    }

    // 创建默认User角色响应
    fn create_default_user_role_response(&self) -> crate::models::role::RoleResponse {
        use chrono::Utc;

        crate::models::role::RoleResponse {
            id: 4, // 假设user角色的ID是4
            name: "user".to_string(),
            description: Some("普通用户".to_string()),
            permissions: Some(serde_json::json!({"level": 4, "name": "user"})),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }
}
