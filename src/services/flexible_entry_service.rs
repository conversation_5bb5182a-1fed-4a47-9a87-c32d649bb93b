use crate::config::DatabaseConfig;
use crate::models::{
    FlexibleEntry, FlexibleEntryResponse, FlexibleEntryStatus,
    CreateFlexibleEntryRequest, ApproveFlexibleEntryRequest,
    BatchApproveFlexibleEntryRequest, BatchApproveResult,
    FlexibleEntryQueryRequest, FlexibleEntryPageResponse
};
use crate::utils::{AppResult, AppError};
use tiberius::Query;
use chrono::{Utc, TimeZone};
use tracing::{info, warn};

/// 零活录入服务
#[derive(Clone)]
pub struct FlexibleEntryService {
    db_config: DatabaseConfig,
}

impl FlexibleEntryService {
    pub fn new(db_config: DatabaseConfig) -> Self {
        Self { db_config }
    }

    /// 创建零活录入（仅班长可操作）
    pub async fn create_flexible_entry(
        &self,
        request: &CreateFlexibleEntryRequest,
        creator_psn_num: &str,
    ) -> AppResult<i64> {
        // 验证请求数据
        request.validate().map_err(|e| AppError::Validation(e))?;

        // 验证预维护零活是否存在
        self.validate_pre_maintained_work_exists(&request.operation_id).await?;

        // 验证指派班组成员是否存在（如果提供）
        if let Some(ref member_psn_num) = request.assigned_member_psn_num {
            self.validate_person_exists(member_psn_num).await?;
            // 验证指派的班组成员是否为有效的班组成员或班长
            self.validate_assignable_member(member_psn_num, creator_psn_num).await?;
        }

        let mut client = self.db_config.get_app_connection().await?;

        let insert_sql = r#"
            INSERT INTO FlexibleEntries (
                inventory_id, operation_id, flexible_quantity,
                creator_psn_num, assigned_member_psn_num, created_time, status,
                created_at, updated_at,approved_quantity
            ) VALUES (
                @P1, @P2, @P3,
                @P4, @P5, GETDATE(), @P6,
                GETDATE(), GETDATE(),@P7
            );
            SELECT CAST(SCOPE_IDENTITY() AS BIGINT) as id;
        "#;

        let mut query = Query::new(insert_sql);
        query.bind(request.inventory_id.as_deref());
        query.bind(&request.operation_id);
        query.bind(request.flexible_quantity);
        query.bind(creator_psn_num);
        query.bind(request.assigned_member_psn_num.as_deref());
        query.bind(FlexibleEntryStatus::Pending.to_db_value());
        query.bind(request.flexible_quantity);

        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        let entry_id = rows.first()
            .and_then(|row| row.get::<i64, _>(0))
            .ok_or_else(|| AppError::Database("获取新创建的零活录入ID失败".to_string()))?;
        Ok(entry_id)
    }

    /// 审核零活录入（仅负责人可操作）
    pub async fn approve_flexible_entry(
        &self,
        entry_id: i64,
        request: &ApproveFlexibleEntryRequest,
        approver_psn_num: &str,
        approver_role: Option<&str>,
    ) -> AppResult<()> {
        // 验证请求数据
        request.validate().map_err(|e| AppError::Validation(e))?;

        let mut client = self.db_config.get_app_connection().await?;

        // 检查零活录入是否存在且为待审核状态，同时获取创建人的部门信息
        let check_sql = r#"
            SELECT fe.id, fe.status, fe.creator_psn_num, p.cDept_num as creator_dept
            FROM FlexibleEntries fe
            LEFT JOIN person p ON fe.creator_psn_num = p.cpsn_num
            WHERE fe.id = @P1
        "#;

        let mut check_query = Query::new(check_sql);
        check_query.bind(entry_id);

        let check_stream = check_query.query(&mut *client).await?;
        let check_rows: Vec<_> = check_stream.into_first_result().await?;

        let entry_row = check_rows.first()
            .ok_or_else(|| AppError::Business("零活录入不存在".to_string()))?;

        let current_status = entry_row.get::<u8, _>(1).unwrap_or(0) as i32;
        let creator_psn_num = entry_row.get::<&str, _>(2).unwrap_or("");
        let creator_dept = entry_row.get::<&str, _>(3).map(|s| s.to_string());

        if current_status != FlexibleEntryStatus::Pending.to_db_value() {
            return Err(AppError::Business("该零活录入已经审核过了".to_string()));
        }

        // 防止自己审核自己创建的记录
        if creator_psn_num == approver_psn_num {
            return Err(AppError::Business("不能审核自己创建的零活录入".to_string()));
        }

        // 工作中心权限检查（仅对非管理员用户）
        if let Some(role) = approver_role {
            if role != "admin" {
                let approver_dept = self.get_user_department(approver_psn_num).await?;

                // 负责人只能审核自己工作中心下的零活录入
                if role == "manager" {
                    if let (Some(approver_dept_code), Some(creator_dept_code)) = (approver_dept, creator_dept) {
                        if approver_dept_code != creator_dept_code {
                            return Err(AppError::Business("只能审核自己工作中心下的零活录入".to_string()));
                        }
                    } else {
                        return Err(AppError::Business("无法确定工作中心权限".to_string()));
                    }
                } else {
                    // 其他角色不能审核
                    return Err(AppError::Business("权限不足，无法审核零活录入".to_string()));
                }
            }
        }

        // 更新审核信息
        let update_sql = r#"
            UPDATE FlexibleEntries 
            SET approved_quantity = @P1,
                approval_remarks = @P2,
                approver_psn_num = @P3,
                approved_time = GETDATE(),
                status = @P4,
                updated_at = GETDATE()
            WHERE id = @P5
        "#;

        let mut update_query = Query::new(update_sql);
        update_query.bind(request.approved_quantity);
        update_query.bind(request.approval_remarks.as_deref());
        update_query.bind(approver_psn_num);
        update_query.bind(FlexibleEntryStatus::Approved.to_db_value());
        update_query.bind(entry_id);

        update_query.execute(&mut *client).await?;

        info!(
            "零活录入审核成功: ID={}, 审核数量={}, 审核人={}",
            entry_id, request.approved_quantity, approver_psn_num
        );

        Ok(())
    }

    /// 批量审核零活录入（仅负责人可操作）
    pub async fn batch_approve_flexible_entries(
        &self,
        request: &BatchApproveFlexibleEntryRequest,
        approver_psn_num: &str,
        approver_role: Option<&str>,
    ) -> AppResult<BatchApproveResult> {
        // 验证请求数据
        let mut success_count = 0;
        let mut failed_count = 0;
        let mut failed_entries = Vec::new();

        // 获取审核人的部门信息（用于权限检查）
        let approver_dept = if approver_role != Some("admin") {
            self.get_user_department(approver_psn_num).await?
        } else {
            None
        };

        for entry_id in &request.entry_ids {
            match self.approve_single_entry_in_batch(
                *entry_id,
                request.approval_remarks.as_deref(),
                approver_psn_num,
                approver_role,
                approver_dept.as_deref(),
            ).await {
                Ok(_) => {
                    success_count += 1;
                    info!("批量审核成功: 零活录入ID={}", entry_id);
                }
                Err(e) => {
                    failed_count += 1;
                    failed_entries.push((*entry_id, e.to_string()));
                    warn!("批量审核失败: 零活录入ID={}, 错误: {}", entry_id, e);
                }
            }
        }

        let total_count = request.entry_ids.len();
        let message = format!("批量审核完成：成功 {} 个，失败 {} 个", success_count, failed_count);

        info!(
            "批量审核零活录入完成: 总数={}, 成功={}, 失败={}, 审核人={}",
            total_count, success_count, failed_count, approver_psn_num
        );

        Ok(BatchApproveResult {
            total_count,
            success_count,
            failed_count,
            message,
            failed_entries,
        })
    }

    /// 批量审核中的单个条目处理
    async fn approve_single_entry_in_batch(
        &self,
        entry_id: i64,
        // approved_quantity: i32,
        approval_remarks: Option<&str>,
        approver_psn_num: &str,
        approver_role: Option<&str>,
        approver_dept: Option<&str>,
    ) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;
        // 检查零活录入是否存在且为待审核状态，同时获取创建人的部门信息
        let check_sql = r#"
            SELECT fe.id, fe.status, fe.creator_psn_num, p.cDept_num as creator_dept
            FROM FlexibleEntries fe
            LEFT JOIN person p ON fe.creator_psn_num = p.cpsn_num
            WHERE fe.id = @P1
        "#;

        let mut check_query = Query::new(check_sql);
        check_query.bind(entry_id as i64);

        let check_stream = check_query.query(&mut *client).await?;
        let check_rows: Vec<_> = check_stream.into_first_result().await?;

        let entry_row = check_rows.first()
            .ok_or_else(|| AppError::Business("零活录入不存在".to_string()))?;

        let current_status = entry_row.get::<u8, _>(1).unwrap_or(0) as i32;
        let creator_psn_num = entry_row.get::<&str, _>(2).unwrap_or("");
        let creator_dept = entry_row.get::<&str, _>(3).map(|s| s.to_string());

        if current_status != FlexibleEntryStatus::Pending.to_db_value() {
            return Err(AppError::Business("该零活录入已经审核过了".to_string()));
        }

        // 防止自己审核自己创建的记录
        if creator_psn_num == approver_psn_num {
            return Err(AppError::Business("不能审核自己创建的零活录入".to_string()));
        }

        // 工作中心权限检查（仅对非管理员用户）
        if let Some(role) = approver_role {
            if role != "admin" && role == "manager" {
                if let (Some(approver_dept_code), Some(creator_dept_code)) = (approver_dept, creator_dept) {
                    if approver_dept_code != creator_dept_code {
                        return Err(AppError::Business("只能审核自己工作中心下的零活录入".to_string()));
                    }
                } else {
                    return Err(AppError::Business("无法确定工作中心权限".to_string()));
                }
            } else if role != "admin" {
                return Err(AppError::Business("权限不足，无法审核零活录入".to_string()));
            }
        }

        // 更新审核信息
        let update_sql = r#"
            UPDATE FlexibleEntries
            SET 
                approval_remarks = @P1,
                approver_psn_num = @P2,
                approved_time = GETDATE(),
                status = @P3,
                updated_at = GETDATE()
            WHERE id = @P4
        "#;

        let mut update_query = Query::new(update_sql);
        update_query.bind(approval_remarks);
        update_query.bind(approver_psn_num);
        update_query.bind(FlexibleEntryStatus::Approved.to_db_value());
        update_query.bind(entry_id);

        update_query.execute(&mut *client).await?;

        Ok(())
    }

    /// 验证班组成员是否有效
    async fn validate_team_member(&self, psn_num: &str) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        // 检查该人员是否为某个班组的成员
        let mut query = Query::new(r#"
            SELECT COUNT(*)
            FROM TeamMembers tm
            INNER JOIN Teams t ON tm.TeamID = t.TeamID
            WHERE tm.Member_psn_num = @P1 AND tm.IsActive = 1
        "#);
        query.bind(psn_num);

        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        let count = rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0);

        if count == 0 {
            return Err(AppError::Business(format!("人员 {} 不是有效的班组成员", psn_num)));
        }

        Ok(())
    }

    /// 验证可指派的成员（班组成员或班长）
    async fn validate_assignable_member(&self, assigned_psn_num: &str, creator_psn_num: &str) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        // 1. 如果指派给自己，直接允许
        if assigned_psn_num == creator_psn_num {
            return Ok(());
        }

        // 2. 如果创建者是admin，允许指派给任何班长
        if creator_psn_num == "admin" {
            // 只需要验证被指派人是班长即可
            let leader_check_sql = r#"
                SELECT COUNT(*)
                FROM user_roles ur
                INNER JOIN roles r ON ur.role_id = r.id
                WHERE ur.user_id COLLATE Chinese_PRC_CI_AS = @P1 COLLATE Chinese_PRC_CI_AS AND r.name = 'team_leader'
            "#;

            let mut leader_query = Query::new(leader_check_sql);
            leader_query.bind(assigned_psn_num);

            let leader_stream = leader_query.query(&mut client).await?;
            let leader_rows: Vec<_> = leader_stream.into_first_result().await?;

            let is_leader = leader_rows.first()
                .and_then(|row| row.get::<i32, _>(0))
                .unwrap_or(0) > 0;

            if is_leader {
                return Ok(());
            } else {
                return Err(AppError::Business("只能指派给班长".to_string()));
            }
        }

        // 3. 对于非admin用户，检查指派的人员是否为班长（team_leader角色）
        let leader_check_sql = r#"
            SELECT COUNT(*)
            FROM user_roles ur
            INNER JOIN roles r ON ur.role_id = r.id
            WHERE ur.user_id COLLATE Chinese_PRC_CI_AS = @P1 COLLATE Chinese_PRC_CI_AS AND r.name = 'team_leader'
        "#;

        let mut leader_query = Query::new(leader_check_sql);
        leader_query.bind(assigned_psn_num);

        let leader_stream = leader_query.query(&mut client).await?;
        let leader_rows: Vec<_> = leader_stream.into_first_result().await?;

        let is_leader = leader_rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0) > 0;

        if is_leader {
            // 3. 如果是班长，检查是否在同一个班组（班长在TeamLeaders表中）
            let same_team_sql = r#"
                SELECT COUNT(*)
                FROM (
                    SELECT TeamID FROM TeamLeaders WHERE Leader_psn_num COLLATE Chinese_PRC_CI_AS = @P1 COLLATE Chinese_PRC_CI_AS AND IsActive = 1
                    UNION
                    SELECT TeamID FROM TeamMembers WHERE Member_psn_num COLLATE Chinese_PRC_CI_AS = @P1 COLLATE Chinese_PRC_CI_AS AND IsActive = 1
                ) t1
                INNER JOIN (
                    SELECT TeamID FROM TeamLeaders WHERE Leader_psn_num COLLATE Chinese_PRC_CI_AS = @P2 COLLATE Chinese_PRC_CI_AS AND IsActive = 1
                    UNION
                    SELECT TeamID FROM TeamMembers WHERE Member_psn_num COLLATE Chinese_PRC_CI_AS = @P2 COLLATE Chinese_PRC_CI_AS AND IsActive = 1
                ) t2 ON t1.TeamID = t2.TeamID
            "#;

            let mut same_team_query = Query::new(same_team_sql);
            same_team_query.bind(creator_psn_num);
            same_team_query.bind(assigned_psn_num);

            let same_team_stream = same_team_query.query(&mut client).await?;
            let same_team_rows: Vec<_> = same_team_stream.into_first_result().await?;

            let same_team_count = same_team_rows.first()
                .and_then(|row| row.get::<i32, _>(0))
                .unwrap_or(0);

            if same_team_count > 0 {
                return Ok(()); // 同班组的班长，允许指派
            } else {
                return Err(AppError::Business(format!("只能指派给同班组的班长")));
            }
        }

        // 4. 如果不是班长，检查是否为有效的班组成员
        self.validate_team_member(assigned_psn_num).await?;

        Ok(())
    }

    /// 获取用户所属部门
    async fn get_user_department(&self, user_psn_num: &str) -> AppResult<Option<String>> {
        let mut client = self.db_config.get_app_connection().await?;

        let mut query = Query::new("SELECT cDept_num FROM person WHERE cpsn_num = @P1");
        query.bind(user_psn_num);

        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        let dept_code = rows.first()
            .and_then(|row| row.get::<&str, _>(0))
            .map(|s| s.to_string());

        Ok(dept_code)
    }

    /// 根据ID获取零活录入详情
    pub async fn get_flexible_entry_by_id(&self, entry_id: i64) -> AppResult<FlexibleEntryResponse> {
        let mut client = self.db_config.get_app_connection().await?;

        let query_sql = r#"
            SELECT
                fe.id, fe.inventory_id, fe.operation_id, fe.flexible_quantity,
                fe.approved_quantity, fe.creator_psn_num, fe.approver_psn_num,
                fe.assigned_member_psn_num, fe.created_time, fe.approved_time, fe.approval_remarks,
                fe.status, fe.created_at, fe.updated_at,
                -- 关联数据
                inv.cinvname, inv.cInvStd,
                pmfw.description as operation_name,
                p1.cPsn_Name as creator_name,
                p2.cPsn_Name as approver_name,
                p3.cPsn_Name as assigned_member_name,
                wc.Description as work_center_name
            FROM FlexibleEntries fe
            LEFT JOIN inventory inv ON fe.inventory_id = inv.cinvcode
            LEFT JOIN PreMaintainedFlexibleWorks pmfw ON fe.operation_id = pmfw.opcode
            LEFT JOIN person p1 ON fe.creator_psn_num = p1.cpsn_num
            LEFT JOIN person p2 ON fe.approver_psn_num = p2.cpsn_num
            LEFT JOIN person p3 ON fe.assigned_member_psn_num = p3.cpsn_num
            LEFT JOIN workcenter wc ON p1.cDept_num = wc.DeptCode
            WHERE fe.id = @P1
        "#;

        let mut query = Query::new(query_sql);
        query.bind(entry_id);

        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        let row = rows.first()
            .ok_or_else(|| AppError::Business("零活录入不存在".to_string()))?;

        let entry = self.map_row_to_flexible_entry_response(row)?;
        Ok(entry)
    }

    /// 分页查询零活录入列表
    pub async fn get_flexible_entries(
        &self,
        request: &FlexibleEntryQueryRequest,
        user_psn_num: Option<&str>,
        user_role: Option<&str>,
    ) -> AppResult<FlexibleEntryPageResponse> {
        let mut client = self.db_config.get_app_connection().await?;

        // 构建WHERE条件（包含工作中心权限控制）
        let (where_clause, params) = self.build_where_clause_with_permission(request, user_psn_num, user_role).await?;

        // 查询总数 - 优化版本，只在需要时关联person表
        let (count_sql, count_params) = self.build_optimized_count_query(request, user_psn_num, user_role).await?;

        let mut count_query = Query::new(&count_sql);
        self.bind_parameters(&mut count_query, &count_params);

        let count_stream = count_query.query(&mut *client).await?;
        let count_rows: Vec<_> = count_stream.into_first_result().await?;
        let total = count_rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0) as u32;

        // 查询数据
        let data_sql = format!(
            r#"
            SELECT
                fe.id, fe.inventory_id, fe.operation_id, fe.flexible_quantity,
                fe.approved_quantity, fe.creator_psn_num, fe.approver_psn_num,
                fe.assigned_member_psn_num, fe.created_time, fe.approved_time, fe.approval_remarks,
                fe.status, fe.created_at, fe.updated_at,
                -- 关联数据
                inv.cinvname, inv.cInvStd,
                pmfw.description as operation_name,
                p1.cPsn_Name as creator_name,
                p2.cPsn_Name as approver_name,
                p3.cPsn_Name as assigned_member_name,
                wc.Description as work_center_name
            FROM FlexibleEntries fe
            LEFT JOIN inventory inv ON fe.inventory_id = inv.cinvcode
            LEFT JOIN PreMaintainedFlexibleWorks pmfw ON fe.operation_id = pmfw.opcode
            LEFT JOIN person p1 ON fe.creator_psn_num = p1.cpsn_num
            LEFT JOIN person p2 ON fe.approver_psn_num = p2.cpsn_num
            LEFT JOIN person p3 ON fe.assigned_member_psn_num = p3.cpsn_num
            LEFT JOIN workcenter wc ON p1.cDept_num = wc.DeptCode
            {} 
            ORDER BY fe.created_time DESC 
            OFFSET {} ROWS FETCH NEXT {} ROWS ONLY
            "#,
            where_clause,
            request.get_offset(),
            request.get_page_size()
        );



        let mut data_query = Query::new(&data_sql);
        self.bind_parameters(&mut data_query, &params);

        let data_stream = data_query.query(&mut *client).await?;
        let data_rows: Vec<_> = data_stream.into_first_result().await?;

        let mut entries = Vec::new();
        for row in data_rows {
            let entry = self.map_row_to_flexible_entry_response(&row)?;
            entries.push(entry);
        }

        let total_pages = if total == 0 {
            0
        } else {
            (total + request.get_page_size() - 1) / request.get_page_size()
        };

        Ok(FlexibleEntryPageResponse {
            items: entries,
            total,
            page: request.get_page(),
            page_size: request.get_page_size(),
            total_pages,
        })
    }

    /// 验证预维护零活是否存在
    async fn validate_pre_maintained_work_exists(&self, opcode: &str) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        let mut query = Query::new("SELECT COUNT(*) FROM PreMaintainedFlexibleWorks WHERE opcode = @P1");
        query.bind(opcode);

        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        let count = rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0);

        if count == 0 {
            return Err(AppError::Business(format!("预维护零活 {} 不存在", opcode)));
        }

        Ok(())
    }

    /// 验证人员是否存在
    async fn validate_person_exists(&self, psn_num: &str) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        let mut query = Query::new("SELECT COUNT(*) FROM person WHERE cpsn_num = @P1");
        query.bind(psn_num);

        let stream = query.query(&mut *client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        let count = rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0);

        if count == 0 {
            return Err(AppError::NotFound(format!("人员 {} 不存在", psn_num)));
        }

        Ok(())
    }

    /// 构建WHERE条件（包含工作中心权限控制）
    async fn build_where_clause_with_permission(
        &self,
        request: &FlexibleEntryQueryRequest,
        user_psn_num: Option<&str>,
        user_role: Option<&str>,
    ) -> AppResult<(String, Vec<String>)> {
        let mut conditions = Vec::new();
        let mut params = Vec::new();
        let mut param_index = 1;

        // 添加工作中心权限控制
        if let (Some(psn_num), Some(role)) = (user_psn_num, user_role) {
            match role {
                "admin" => {
                    // 管理员可以看到所有零活录入，不添加限制
                }
                "manager" => {
                    // 负责人只能看到自己工作中心下的零活录入
                    let user_dept = self.get_user_department(psn_num).await?;
                    if let Some(dept_code) = user_dept {
                        conditions.push(format!(
                            "EXISTS (SELECT 1 FROM person p WHERE p.cpsn_num = fe.creator_psn_num AND p.cDept_num = @P{})",
                            param_index
                        ));
                        params.push(dept_code);
                        param_index += 1;
                    }
                }
                "team_leader" => {
                    // 班长只能看到自己创建的零活录入
                    conditions.push(format!("fe.creator_psn_num = @P{}", param_index));
                    params.push(psn_num.to_string());
                    param_index += 1;
                }
                _ => {
                    // 其他角色只能看到自己创建的零活录入
                    conditions.push(format!("fe.creator_psn_num = @P{}", param_index));
                    params.push(psn_num.to_string());
                    param_index += 1;
                }
            }
        }

        // 添加查询条件
        if let Some(inventory_id) = &request.inventory_id {
            conditions.push(format!("fe.inventory_id LIKE @P{}", param_index));
            params.push(format!("%{}%", inventory_id));
            param_index += 1;
        }

        if let Some(operation_id) = &request.operation_id {
            conditions.push(format!("fe.operation_id LIKE @P{}", param_index));
            params.push(format!("%{}%", operation_id));
            param_index += 1;
        }

        if let Some(status) = request.status {
            conditions.push(format!("fe.status = @P{}", param_index));
            params.push(status.to_string());
            param_index += 1;
        }

        if let Some(start_timestamp) = request.created_time_start {
            conditions.push(format!("fe.created_time >= @P{}", param_index));
            let start_time = chrono::Utc.timestamp_opt(start_timestamp, 0)
                .single()
                .unwrap_or_else(|| chrono::Utc::now());
            params.push(start_time.format("%Y-%m-%d %H:%M:%S").to_string());
            param_index += 1;
        }

        if let Some(end_timestamp) = request.created_time_end {
            conditions.push(format!("fe.created_time <= @P{}", param_index));
            let end_time = chrono::Utc.timestamp_opt(end_timestamp, 0)
                .single()
                .unwrap_or_else(|| chrono::Utc::now());
            params.push(end_time.format("%Y-%m-%d %H:%M:%S").to_string());
            param_index += 1;
        }

        if let Some(creator_psn_num) = &request.creator_psn_num {
            conditions.push(format!("fe.creator_psn_num = @P{}", param_index));
            params.push(creator_psn_num.clone());
            param_index += 1;
        }

        if let Some(approver_psn_num) = &request.approver_psn_num {
            conditions.push(format!("fe.approver_psn_num = @P{}", param_index));
            params.push(approver_psn_num.clone());
            param_index += 1;
        }

        if let Some(assigned_member_psn_num) = &request.assigned_member_psn_num {
            conditions.push(format!("fe.assigned_member_psn_num = @P{}", param_index));
            params.push(assigned_member_psn_num.clone());
            param_index += 1;
        }

        if let Some(assigned_member_name) = &request.assigned_member_name {
            conditions.push(format!("p3.cPsn_Name LIKE @P{}", param_index));
            params.push(format!("%{}%", assigned_member_name));
        }

        let where_clause = if conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", conditions.join(" AND "))
        };

        Ok((where_clause, params))
    }

    /// 构建优化的COUNT查询（避免不必要的person表关联）
    async fn build_optimized_count_query(
        &self,
        request: &FlexibleEntryQueryRequest,
        user_psn_num: Option<&str>,
        user_role: Option<&str>,
    ) -> AppResult<(String, Vec<String>)> {
        let mut conditions = Vec::new();
        let mut params = Vec::new();
        let mut param_index = 1;

        // 添加工作中心权限控制
        if let (Some(psn_num), Some(role)) = (user_psn_num, user_role) {
            match role {
                "admin" => {
                    // 管理员不需要限制
                }
                "manager" => {
                    // 负责人需要检查工作中心
                    let user_dept = self.get_user_department(psn_num).await?;
                    if let Some(dept_code) = user_dept {
                        conditions.push(format!(
                            "EXISTS (SELECT 1 FROM person p WHERE p.cpsn_num = fe.creator_psn_num AND p.cDept_num = @P{})",
                            param_index
                        ));
                        params.push(dept_code);
                        param_index += 1;
                    }
                }
                _ => {
                    // 其他角色只能看到自己创建的
                    conditions.push(format!("fe.creator_psn_num = @P{}", param_index));
                    params.push(psn_num.to_string());
                    param_index += 1;
                }
            }
        }

        // 添加基本查询条件（不需要person表的）
        if let Some(inventory_id) = &request.inventory_id {
            conditions.push(format!("fe.inventory_id LIKE @P{}", param_index));
            params.push(format!("%{}%", inventory_id));
            param_index += 1;
        }

        if let Some(operation_id) = &request.operation_id {
            conditions.push(format!("fe.operation_id LIKE @P{}", param_index));
            params.push(format!("%{}%", operation_id));
            param_index += 1;
        }

        if let Some(status) = request.status {
            conditions.push(format!("fe.status = @P{}", param_index));
            params.push(status.to_string());
            param_index += 1;
        }

        if let Some(start_timestamp) = request.created_time_start {
            conditions.push(format!("fe.created_time >= @P{}", param_index));
            let start_time = chrono::Utc.timestamp_opt(start_timestamp, 0)
                .single()
                .unwrap_or_else(|| chrono::Utc::now());
            params.push(start_time.format("%Y-%m-%d %H:%M:%S").to_string());
            param_index += 1;
        }

        if let Some(end_timestamp) = request.created_time_end {
            conditions.push(format!("fe.created_time <= @P{}", param_index));
            let end_time = chrono::Utc.timestamp_opt(end_timestamp, 0)
                .single()
                .unwrap_or_else(|| chrono::Utc::now());
            params.push(end_time.format("%Y-%m-%d %H:%M:%S").to_string());
            param_index += 1;
        }

        if let Some(creator_psn_num) = &request.creator_psn_num {
            conditions.push(format!("fe.creator_psn_num = @P{}", param_index));
            params.push(creator_psn_num.clone());
            param_index += 1;
        }

        if let Some(approver_psn_num) = &request.approver_psn_num {
            conditions.push(format!("fe.approver_psn_num = @P{}", param_index));
            params.push(approver_psn_num.clone());
            param_index += 1;
        }

        if let Some(assigned_member_psn_num) = &request.assigned_member_psn_num {
            conditions.push(format!("fe.assigned_member_psn_num = @P{}", param_index));
            params.push(assigned_member_psn_num.clone());
            param_index += 1;
        }

        // 如果需要按人员姓名筛选，则需要关联person表
        if let Some(assigned_member_name) = &request.assigned_member_name {
            conditions.push(format!(
                "EXISTS (SELECT 1 FROM person p WHERE p.cpsn_num = fe.assigned_member_psn_num AND p.cPsn_Name LIKE @P{})",
                param_index
            ));
            params.push(format!("%{}%", assigned_member_name));
        }

        let where_clause = if conditions.is_empty() {
            String::new()
        } else {
            format!("WHERE {}", conditions.join(" AND "))
        };

        let count_sql = format!(
            "SELECT COUNT(*) as total FROM FlexibleEntries fe {}",
            where_clause
        );



        Ok((count_sql, params))
    }

    /// 绑定查询参数
    fn bind_parameters<'a>(&self, query: &mut Query<'a>, params: &'a [String]) {
        for param in params {
            query.bind(param);
        }
    }

    /// 将数据库行映射为FlexibleEntryResponse
    fn map_row_to_flexible_entry_response(&self, row: &tiberius::Row) -> AppResult<FlexibleEntryResponse> {
        let id = row.get::<i64, _>(0).unwrap_or(0);
        let inventory_id = row.get::<&str, _>(1).unwrap_or("").to_string();
        let operation_id = row.get::<&str, _>(2).unwrap_or("").to_string();
        let flexible_quantity = row.get::<i32, _>(3).unwrap_or(0);
        let approved_quantity = row.get::<i32, _>(4);
        let creator_psn_num = row.get::<&str, _>(5).unwrap_or("").to_string();
        let approver_psn_num = row.get::<&str, _>(6).map(|s| s.to_string());
        let assigned_member_psn_num = row.get::<&str, _>(7).map(|s| s.to_string());

        // 时间字段处理 - 数据库存储的是本地时间(CST)，直接当作UTC处理
        let created_time = row.get::<chrono::DateTime<chrono::Utc>, _>(8)
            .unwrap_or_else(|| Utc::now());
        let approved_time = row.get::<chrono::DateTime<chrono::Utc>, _>(9);

        let approval_remarks = row.get::<&str, _>(10).map(|s| s.to_string());
        let status_value = row.get::<u8, _>(11).unwrap_or(0) as i32;
        let status = FlexibleEntryStatus::from_db_value(status_value);

        let created_at = row.get::<chrono::DateTime<chrono::Utc>, _>(12)
            .unwrap_or_else(|| Utc::now());
        let updated_at = row.get::<chrono::DateTime<chrono::Utc>, _>(13)
            .unwrap_or_else(|| Utc::now());

        // 关联数据
        let cinvname = row.get::<&str, _>(14).map(|s| s.to_string());
        let cinv_std = row.get::<&str, _>(15).map(|s| s.to_string());
        let operation_name = row.get::<&str, _>(16).map(|s| s.to_string());
        let creator_name = row.get::<&str, _>(17).map(|s| s.to_string());
        let approver_name = row.get::<&str, _>(18).map(|s| s.to_string());
        let assigned_member_name = row.get::<&str, _>(19).map(|s| s.to_string());
        let work_center_name = row.get::<&str, _>(20).map(|s| s.to_string());

        let entry = FlexibleEntry {
            id,
            inventory_id,
            operation_id,
            flexible_quantity,
            approved_quantity,
            creator_psn_num,
            approver_psn_num,
            assigned_member_psn_num,
            created_time,
            approved_time,
            approval_remarks,
            status,
            created_at,
            updated_at,
        };

        Ok(FlexibleEntryResponse {
            entry,
            cinvname,
            cinv_std,
            operation_name,
            creator_name,
            approver_name,
            assigned_member_name,
            work_center_name,
        })
    }

    /// 班长更新零活数量（仅班长可操作）
    pub async fn update_flexible_quantity(
        &self,
        entry_id: i64,
        quantity: i32,
        user_psn_num: &str,
        user_role: Option<String>,
    ) -> AppResult<()> {
        // 参数验证
        if quantity < 1 || quantity > 10000 {
            return Err(AppError::Validation("数量必须在1-10000之间".to_string()));
        }

        let mut client = self.db_config.get_app_connection().await?;

        // 检查零活录入是否存在
        let check_sql = r#"
            SELECT fe.id, fe.status, fe.creator_psn_num
            FROM FlexibleEntries fe
            WHERE fe.id = @P1
        "#;

        let mut check_query = Query::new(check_sql);
        check_query.bind(entry_id);

        let check_stream = check_query.query(&mut client).await?;
        let check_rows: Vec<_> = check_stream.into_first_result().await?;

        let entry_row = check_rows.first()
            .ok_or_else(|| AppError::Business("零活录入不存在".to_string()))?;

        let creator_psn_num: String = entry_row.get::<&str, _>(2).unwrap_or("").to_string();

        // 权限检查：只有班长角色才能更新
        if user_role != Some("team_leader".to_string()) && user_role != Some("admin".to_string()) {
            return Err(AppError::Business("权限不足，只有班长才能更新零活数量".to_string()));
        }

        // 如果不是管理员，检查是否为创建人
        if user_role != Some("admin".to_string()) && creator_psn_num != user_psn_num {
            return Err(AppError::Business("只能更新自己创建的零活录入".to_string()));
        }

        // 更新flexible_quantity字段
        let update_sql = r#"
            UPDATE FlexibleEntries
            SET flexible_quantity = @P1,
                updated_at = GETDATE()
            WHERE id = @P2
        "#;

        let mut update_query = Query::new(update_sql);
        update_query.bind(quantity);
        update_query.bind(entry_id);

        update_query.execute(&mut client).await?;

        info!(
            "班长零活数量更新成功: 零活录入ID={}, 数量={}, 操作人={}",
            entry_id, quantity, user_psn_num
        );

        Ok(())
    }

    /// 负责人更新审核数量（仅负责人可操作）
    pub async fn update_approved_quantity(
        &self,
        entry_id: i64,
        quantity: i32,
        user_psn_num: &str,
        user_role: Option<String>,
    ) -> AppResult<()> {
        // 参数验证
        if quantity < 1 || quantity > 10000 {
            return Err(AppError::Validation("数量必须在1-10000之间".to_string()));
        }

        let mut client = self.db_config.get_app_connection().await?;

        // 检查零活录入是否存在
        let check_sql = r#"
            SELECT fe.id, fe.status, fe.creator_psn_num, p.cDept_num as creator_dept
            FROM FlexibleEntries fe
            LEFT JOIN person p ON fe.creator_psn_num = p.cpsn_num
            WHERE fe.id = @P1
        "#;

        let mut check_query = Query::new(check_sql);
        check_query.bind(entry_id);

        let check_stream = check_query.query(&mut client).await?;
        let check_rows: Vec<_> = check_stream.into_first_result().await?;

        let entry_row = check_rows.first()
            .ok_or_else(|| AppError::Business("零活录入不存在".to_string()))?;

        let creator_dept = entry_row.get::<&str, _>(3).map(|s| s.to_string());

        // 权限检查：只有负责人角色才能更新
        if user_role != Some("manager".to_string()) && user_role != Some("admin".to_string()) {
            return Err(AppError::Business("权限不足，只有负责人才能更新审核数量".to_string()));
        }

        // 如果不是管理员，检查工作中心权限
        if user_role != Some("admin".to_string()) {
            let user_dept_code = self.get_user_dept_code(user_psn_num).await?;
            if user_dept_code != creator_dept {
                return Err(AppError::Business("只能更新自己工作中心的零活录入".to_string()));
            }
        }

        // 更新approved_quantity字段
        let update_sql = r#"
            UPDATE FlexibleEntries
            SET approved_quantity = @P1,
                updated_at = GETDATE()
            WHERE id = @P2
        "#;

        let mut update_query = Query::new(update_sql);
        update_query.bind(quantity);
        update_query.bind(entry_id);

        update_query.execute(&mut client).await?;

        info!(
            "负责人审核数量更新成功: 零活录入ID={}, 数量={}, 操作人={}",
            entry_id, quantity, user_psn_num
        );

        Ok(())
    }

    /// 获取用户的部门代码
    async fn get_user_dept_code(&self, user_psn_num: &str) -> AppResult<Option<String>> {
        let mut client = self.db_config.get_app_connection().await?;

        let query_sql = r#"
            SELECT p.cDept_num
            FROM person p
            WHERE p.cpsn_num = @P1
        "#;

        let mut query = Query::new(query_sql);
        query.bind(user_psn_num);

        let stream = query.query(&mut client).await?;
        let rows: Vec<_> = stream.into_first_result().await?;

        Ok(rows.first().and_then(|row| row.get::<&str, _>(0).map(|s| s.to_string())))
    }
}
