use crate::{
    config::DatabaseConfig,
    models::{CreateInstructionCardBorrowRelationRequest},
    utils::{AppResult, AppError},
};
use tiberius::Query;

/// 指令卡借用关联服务
#[derive(Clone)]
pub struct InstructionCardBorrowRelationService {
    db_config: DatabaseConfig,
}

impl InstructionCardBorrowRelationService {
    /// 创建新的服务实例
    pub fn new(db_config: DatabaseConfig) -> Self {
        Self { db_config }
    }

    /// 创建指令卡借用关联
    pub async fn create_relation(
        &self,
        request: &CreateInstructionCardBorrowRelationRequest,
    ) -> AppResult<i32> {
        let mut client = self.db_config.get_app_connection().await?;

        let insert_sql = r#"
            INSERT INTO InstructionCardBorrowRelations 
            (instruction_card_id, borrow_id)
            VALUES (@P1, @P2);
            SELECT SCOPE_IDENTITY() as id;
        "#;

        let mut query = Query::new(insert_sql);
        query.bind(request.instruction_card_id);
        query.bind(request.borrow_id);

        let result = query.query(&mut client).await?;
        let rows: Vec<_> = result.into_first_result().await?;

        if let Some(row) = rows.first() {
            // 处理SQL Server返回的Numeric类型，先获取为Numeric再转换为i32
            use tiberius::numeric::Numeric;
            let id: i32 = if let Ok(Some(numeric_val)) = row.try_get::<Numeric, _>(0) {
                let f64_val: f64 = numeric_val.into();
                f64_val as i32
            } else {
                0
            };
            if id > 0 {
                Ok(id)
            } else {
                Err(AppError::Database("获取的关联ID无效".to_string()))
            }
        } else {
            Err(AppError::Database("创建指令卡借用关联失败".to_string()))
        }
    }

    /// 根据指令卡ID查询关联的借用记录
    pub async fn get_borrow_by_instruction_card(
        &self,
        instruction_card_id: i64,
    ) -> AppResult<Option<i32>> {
        let mut client = self.db_config.get_app_connection().await?;

        let query_sql = r#"
            SELECT borrow_id 
            FROM InstructionCardBorrowRelations 
            WHERE instruction_card_id = @P1
        "#;

        let mut query = Query::new(query_sql);
        query.bind(instruction_card_id);

        let result = query.query(&mut client).await?;
        if let Some(row) = result.into_row().await? {
            let borrow_id: i32 = row.get(0).unwrap_or(0);
            Ok(Some(borrow_id))
        } else {
            Ok(None)
        }
    }

    /// 根据借用ID查询关联的指令卡
    pub async fn get_instruction_cards_by_borrow(
        &self,
        borrow_id: i32,
    ) -> AppResult<Vec<i64>> {
        let mut client = self.db_config.get_app_connection().await?;

        let query_sql = r#"
            SELECT instruction_card_id 
            FROM InstructionCardBorrowRelations 
            WHERE borrow_id = @P1
        "#;

        let mut query = Query::new(query_sql);
        query.bind(borrow_id);

        let result = query.query(&mut client).await?;
        let rows: Vec<_> = result.into_first_result().await?;
        let mut instruction_card_ids = Vec::new();

        for row in rows {
            let card_id: i64 = row.get(0).unwrap_or(0);
            instruction_card_ids.push(card_id);
        }

        Ok(instruction_card_ids)
    }

    /// 删除指令卡借用关联
    pub async fn delete_relation_by_instruction_card(
        &self,
        instruction_card_id: i64,
    ) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        let delete_sql = r#"
            DELETE FROM InstructionCardBorrowRelations 
            WHERE instruction_card_id = @P1
        "#;

        let mut query = Query::new(delete_sql);
        query.bind(instruction_card_id);

        query.execute(&mut client).await?;
        Ok(())
    }

    /// 查询指定人员在指定班组的最新有效借用记录
    pub async fn find_latest_borrow_for_person_in_team(
        &self,
        member_psn_num: &str,
        team_id: i32,
    ) -> AppResult<Option<i32>> {
        let mut client = self.db_config.get_app_connection().await?;

        let query_sql = r#"
            SELECT TOP 1 BorrowID 
            FROM TeamMemberBorrows 
            WHERE Member_psn_num COLLATE SQL_Latin1_General_CP1_CI_AS = @P1 COLLATE SQL_Latin1_General_CP1_CI_AS
              AND BorrowTeamID = @P2 
              AND BorrowStatus = 1
              AND GETDATE() BETWEEN StartDate AND EndDate
            ORDER BY StartDate DESC
        "#;

        let mut query = Query::new(query_sql);
        query.bind(member_psn_num);
        query.bind(team_id);

        let result = query.query(&mut client).await?;
        if let Some(row) = result.into_row().await? {
            let borrow_id: i32 = row.get(0).unwrap_or(0);
            Ok(Some(borrow_id))
        } else {
            Ok(None)
        }
    }
}
