use crate::config::DatabaseConfig;
use crate::models::{CustomPermission, CreateCustomPermissionRequest};
use crate::utils::{AppResult, AppError};
use anyhow::anyhow;
use tiberius::Query;

pub struct CustomPermissionService {
    db_config: DatabaseConfig,
}

impl CustomPermissionService {
    pub fn new(db_config: DatabaseConfig) -> Self {
        Self { db_config }
    }

    /// 创建自定义权限资源（存储到统一的权限定义表）
    pub async fn create_custom_permission(&self, request: &CreateCustomPermissionRequest, _created_by: i64) -> AppResult<CustomPermission> {
        let mut client = self.db_config.get_app_connection().await?;

        // 验证资源名称是否已存在
        let mut query = tiberius::Query::new("SELECT COUNT(*) as count FROM permission_definitions WHERE resource_name = @P1");
        query.bind(&request.resource_name);
        let stream = query.query(&mut *client).await
            .map_err(|e| anyhow!("检查权限资源名称失败: {}", e))?;

        let rows: Vec<tiberius::Row> = stream.into_first_result().await
            .map_err(|e| anyhow!("获取检查结果失败: {}", e))?;

        if let Some(row) = rows.first() {
            let count: i32 = row.get::<i32, _>(0).unwrap_or(0);
            if count > 0 {
                return Err(AppError::Business(format!("权限资源名称 '{}' 已存在", request.resource_name)));
            }
        }

        // 序列化actions
        let actions_json = serde_json::to_string(&request.actions)
            .map_err(|e| anyhow!("序列化权限操作失败: {}", e))?;

        // 插入新的自定义权限到统一的权限定义表
        let mut query = tiberius::Query::new(r#"
            INSERT INTO permission_definitions (resource_name, resource_label, description, category, icon, actions, is_system, is_active)
            OUTPUT INSERTED.id, INSERTED.resource_name, INSERTED.resource_label, INSERTED.description,
                   INSERTED.category, INSERTED.icon, INSERTED.actions, INSERTED.is_active,
                   INSERTED.created_at, INSERTED.updated_at
            VALUES (@P1, @P2, @P3, @P4, @P5, @P6, 0, 1)
        "#);
        query.bind(&request.resource_name);
        query.bind(&request.resource_label);
        query.bind(request.description.as_deref().unwrap_or(""));
        query.bind(&request.category);
        query.bind(request.icon.as_deref().unwrap_or(""));
        query.bind(&actions_json);

        let stream = query.query(&mut *client).await
            .map_err(|e| anyhow!("创建自定义权限失败: {}", e))?;

        let rows: Vec<tiberius::Row> = stream.into_first_result().await
            .map_err(|e| anyhow!("获取创建结果失败: {}", e))?;

        if let Some(row) = rows.first() {
            Ok(CustomPermission {
                id: row.get::<i64, _>(0).unwrap_or(0),
                resource_name: row.get::<&str, _>(1).unwrap_or("").to_string(),
                resource_label: row.get::<&str, _>(2).unwrap_or("").to_string(),
                description: row.get::<&str, _>(3).map(|s| s.to_string()),
                category: row.get::<&str, _>(4).unwrap_or("").to_string(),
                icon: row.get::<&str, _>(5).unwrap_or("").to_string(),
                actions: row.get::<&str, _>(6).unwrap_or("").to_string(),
                is_active: row.get::<bool, _>(7).unwrap_or(true),
                created_at: row.get::<chrono::DateTime<chrono::Utc>, _>(8).unwrap_or_else(|| chrono::Utc::now()),
                updated_at: row.get::<chrono::DateTime<chrono::Utc>, _>(9).unwrap_or_else(|| chrono::Utc::now()),
                created_by: None,
            })
        } else {
            Err(AppError::Business("创建自定义权限失败：未返回结果".to_string()))
        }
    }

    /// 获取所有活跃的自定义权限（从数据库查询）
    pub async fn get_all_active_custom_permissions(&self) -> AppResult<Vec<CustomPermission>> {
        let mut client = self.db_config.get_app_connection().await?;

        // 根据新的表结构查询：关联category表获取真实分类名称
        let stream = client.simple_query("SELECT pd.id, pd.resource, pd.action, pd.description, COALESCE(pc.name, '未分类') as category_name, pd.created_at, pd.updated_at FROM permission_definitions pd LEFT JOIN permission_categories pc ON pd.category_id = pc.id ORDER BY pd.category_id, pd.resource").await
            .map_err(|e| anyhow!("查询自定义权限失败: {}", e))?;

        let rows: Vec<tiberius::Row> = stream.into_first_result().await
            .map_err(|e| anyhow!("获取查询结果失败: {}", e))?;

        let mut result = Vec::new();
        for row in rows {
            let permission = CustomPermission {
                id: row.get::<i64, _>(0).unwrap_or(0),
                resource_name: row.get::<&str, _>(1).unwrap_or("").to_string(), // resource字段
                resource_label: row.get::<&str, _>(1).unwrap_or("").to_string(), // 使用resource作为label
                description: row.get::<&str, _>(3).map(|s| s.to_string()), // description字段
                category: row.get::<&str, _>(4).unwrap_or("未分类").to_string(), // 从数据库查询真实分类名称
                icon: "".to_string(), // 新表结构没有icon字段
                actions: format!("[\"{}\"]", row.get::<&str, _>(2).unwrap_or("")), // action字段转为JSON数组
                is_active: true, // 默认为活跃状态
                created_at: row.get::<chrono::DateTime<chrono::Utc>, _>(5).unwrap_or_else(|| chrono::Utc::now()),
                updated_at: row.get::<chrono::DateTime<chrono::Utc>, _>(6).unwrap_or_else(|| chrono::Utc::now()),
                created_by: None,
            };
            result.push(permission);
        }

        Ok(result)
    }

    /// 根据资源名称获取自定义权限（从数据库查询）
    pub async fn get_custom_permission_by_name(&self, resource_name: &str) -> AppResult<Option<CustomPermission>> {
        let mut client = self.db_config.get_app_connection().await?;

        // 根据新的表结构查询：关联category表获取真实分类名称
        let mut query = Query::new("SELECT pd.id, pd.resource, pd.action, pd.description, COALESCE(pc.name, '未分类') as category_name, pd.created_at, pd.updated_at FROM permission_definitions pd LEFT JOIN permission_categories pc ON pd.category_id = pc.id WHERE pd.resource = @P1");
        query.bind(resource_name);

        let stream = query.query(&mut *client).await
            .map_err(|e| anyhow!("查询自定义权限失败: {}", e))?;

        let rows: Vec<tiberius::Row> = stream.into_first_result().await
            .map_err(|e| anyhow!("获取查询结果失败: {}", e))?;

        if let Some(row) = rows.first() {
            let permission = CustomPermission {
                id: row.get::<i64, _>(0).unwrap_or(0),
                resource_name: row.get::<&str, _>(1).unwrap_or("").to_string(), // resource字段
                resource_label: row.get::<&str, _>(1).unwrap_or("").to_string(), // 使用resource作为label
                description: row.get::<&str, _>(3).map(|s| s.to_string()), // description字段
                category: row.get::<&str, _>(4).unwrap_or("未分类").to_string(), // 从数据库查询真实分类名称
                icon: "".to_string(), // 新表结构没有icon字段
                actions: format!("[\"{}\"]", row.get::<&str, _>(2).unwrap_or("")), // action字段转为JSON数组
                is_active: true, // 默认为活跃状态
                created_at: row.get::<chrono::DateTime<chrono::Utc>, _>(5).unwrap_or_else(|| chrono::Utc::now()),
                updated_at: row.get::<chrono::DateTime<chrono::Utc>, _>(6).unwrap_or_else(|| chrono::Utc::now()),
                created_by: None,
            };
            Ok(Some(permission))
        } else {
            Ok(None)
        }
    }

    /// 检查自定义权限是否支持指定操作
    pub async fn check_custom_permission(&self, resource_name: &str, action_name: &str) -> AppResult<bool> {
        if let Some(permission) = self.get_custom_permission_by_name(resource_name).await? {
            Ok(permission.has_action(action_name))
        } else {
            Ok(false)
        }
    }

    /// 删除自定义权限资源（从数据库删除）- 按资源名称
    pub async fn delete_custom_permission(&self, resource_name: &str) -> AppResult<()> {
        // 检查是否为核心权限（不可删除）
        if self.is_core_permission(resource_name).await? {
            return Err(AppError::Business(format!("核心权限 '{}' 不可删除", resource_name)));
        }

        let mut client = self.db_config.get_app_connection().await?;

        // 检查权限是否存在且为自定义权限
        let mut query = Query::new("SELECT COUNT(*) as count FROM permission_definitions WHERE resource_name = @P1 AND is_system = 0");
        query.bind(resource_name);

        let stream = query.query(&mut *client).await
            .map_err(|e| anyhow!("检查权限存在性失败: {}", e))?;

        let rows: Vec<tiberius::Row> = stream.into_first_result().await
            .map_err(|e| anyhow!("获取检查结果失败: {}", e))?;

        if let Some(row) = rows.first() {
            let count: i32 = row.get::<i32, _>(0).unwrap_or(0);
            if count == 0 {
                return Err(AppError::Business(format!("自定义权限资源 '{}' 不存在", resource_name)));
            }
        }

        // 删除权限
        let mut query = Query::new("DELETE FROM permission_definitions WHERE resource_name = @P1 AND is_system = 0");
        query.bind(resource_name);

        query.execute(&mut *client).await
            .map_err(|e| anyhow!("删除自定义权限失败: {}", e))?;

        Ok(())
    }

    /// 删除自定义权限资源（从数据库删除）- 按ID
    pub async fn delete_custom_permission_by_id(&self, id: i64) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        // 先获取权限信息，检查是否为自定义权限和核心权限
        let mut query = Query::new("SELECT resource_name, is_system FROM permission_definitions WHERE id = @P1");
        query.bind(id);

        let stream = query.query(&mut *client).await
            .map_err(|e| anyhow!("查询权限信息失败: {}", e))?;

        let rows: Vec<tiberius::Row> = stream.into_first_result().await
            .map_err(|e| anyhow!("获取查询结果失败: {}", e))?;

        if let Some(row) = rows.first() {
            let resource_name: &str = row.get::<&str, _>(0).unwrap_or("");
            let is_system: bool = row.get::<bool, _>(1).unwrap_or(true);

            // 检查是否为系统权限
            if is_system {
                return Err(AppError::Business(format!("系统权限不可删除")));
            }

            // 检查是否为核心权限（不可删除）
            if self.is_core_permission(resource_name).await? {
                return Err(AppError::Business(format!("核心权限 '{}' 不可删除", resource_name)));
            }
        } else {
            return Err(AppError::Business(format!("自定义权限资源 ID '{}' 不存在", id)));
        }

        // 删除权限
        let mut query = Query::new("DELETE FROM permission_definitions WHERE id = @P1 AND is_system = 0");
        query.bind(id);

        let affected_rows = query.execute(&mut *client).await
            .map_err(|e| anyhow!("删除自定义权限失败: {}", e))?;

        if affected_rows.rows_affected()[0] == 0 {
            return Err(AppError::Business(format!("删除失败，权限可能已被删除或不存在")));
        }

        Ok(())
    }

    /// 检查是否为核心权限（不可删除的系统权限）
    async fn is_core_permission(&self, resource_name: &str) -> AppResult<bool> {
        // 从数据库查询系统核心权限资源
        let mut client = self.db_config.get_app_connection().await?;

        let query = tiberius::Query::new(
            "SELECT COUNT(*) FROM permission_definitions WHERE resource_name = @P1 AND is_system = 1"
        );

        let mut query = query;
        query.bind(resource_name);

        let result = query.query(&mut client).await?;
        let row = result.into_row().await?;

        if let Some(row) = row {
            let count: i32 = row.get(0).unwrap_or(0);
            Ok(count > 0)
        } else {
            // 如果数据库查询失败，使用后备方案保护核心功能
            Ok(matches!(resource_name, "users" | "roles" | "auth"))
        }
    }

    // 以下方法已废弃，使用更好的替代方法：
    // get_all_custom_permissions -> 使用 get_all_active_custom_permissions
    // deactivate_custom_permission -> 使用 delete_custom_permission

    /// 初始化系统核心权限到数据库（仅包含实际存在的功能）
    pub async fn init_core_permissions(&self) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        // 只初始化系统实际存在的核心权限
        let core_permissions = vec![
            (
                "users",
                "用户管理",
                "管理系统用户账户",
                "系统管理",
                "user",
                r#"[
                    {"name": "read", "label": "查看用户", "description": "查看用户列表和详情"},
                    {"name": "create", "label": "创建用户", "description": "创建新用户账户"},
                    {"name": "update", "label": "更新用户", "description": "更新用户信息"},
                    {"name": "delete", "label": "删除用户", "description": "删除用户账户"}
                ]"#
            ),
            (
                "roles",
                "角色管理",
                "管理系统角色和权限",
                "系统管理",
                "shield",
                r#"[
                    {"name": "read", "label": "查看角色", "description": "查看角色列表和详情"},
                    {"name": "create", "label": "创建角色", "description": "创建新角色"},
                    {"name": "update", "label": "更新角色", "description": "更新角色信息"},
                    {"name": "delete", "label": "删除角色", "description": "删除角色"},
                    {"name": "assign", "label": "分配角色", "description": "为用户分配角色"}
                ]"#
            ),
            (
                "auth",
                "认证管理",
                "用户认证和会话管理",
                "系统管理",
                "key",
                r#"[
                    {"name": "login", "label": "登录", "description": "用户登录系统"},
                    {"name": "logout", "label": "登出", "description": "用户登出系统"},
                    {"name": "verify", "label": "验证", "description": "验证用户身份"},
                    {"name": "profile", "label": "个人资料", "description": "管理个人资料"}
                ]"#
            )
        ];

        // 注意：由于新的表结构只有resource, action, description, category_id字段
        // 我们需要为每个资源的每个操作创建单独的记录
        for (resource, _label, description, _category, _icon, actions_json) in core_permissions {
            // 解析actions JSON
            if let Ok(actions_array) = serde_json::from_str::<Vec<serde_json::Value>>(actions_json) {
                for action_obj in actions_array {
                    if let Some(action_name) = action_obj.get("name").and_then(|v| v.as_str()) {
                        let action_description = action_obj.get("description")
                            .and_then(|v| v.as_str())
                            .unwrap_or(description);

                        let mut query = Query::new(r#"
                            IF NOT EXISTS (SELECT * FROM permission_definitions WHERE resource = @P1 AND action = @P2)
                            BEGIN
                                INSERT INTO permission_definitions (resource, action, description, category_id)
                                VALUES (@P1, @P2, @P3, 1);
                            END
                        "#);
                        query.bind(resource);
                        query.bind(action_name);
                        query.bind(action_description);

                        query.execute(&mut *client).await
                            .map_err(|e| anyhow!("插入核心权限 {}:{} 失败: {}", resource, action_name, e))?;
                    }
                }
            }
        }

        Ok(())
    }
}
