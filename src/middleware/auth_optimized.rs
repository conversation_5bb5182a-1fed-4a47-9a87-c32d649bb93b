// 简化的认证中间件 - 清理后版本
use axum::{
    extract::{Request, State},
    middleware::Next,
    response::{Response, IntoResponse},
};

use crate::config::{DatabaseConfig, Settings};
use crate::utils::{verify_jwt, extract_token_from_header, ApiResponse};

// 简化的认证中间件
pub async fn auth_middleware_compat(
    State((_db_config, settings)): State<(DatabaseConfig, Settings)>,
    mut request: Request,
    next: Next,
) -> Result<Response, Response> {
    // 从请求头提取token
    let auth_header = match request.headers().get("authorization") {
        Some(header) => match header.to_str() {
            Ok(header_str) => header_str,
            Err(_) => {
                return Err(ApiResponse::unauthorized("无效的认证头格式").into_response());
            }
        },
        None => {
            return Err(ApiResponse::unauthorized("缺少认证token").into_response());
        }
    };

    let token = match extract_token_from_header(auth_header) {
        Ok(token) => token,
        Err(_) => {
            return Err(ApiResponse::unauthorized("无效的token格式").into_response());
        }
    };

    // 验证token
    let claims = match verify_jwt(&token, &settings.jwt.secret) {
        Ok(claims) => claims,
        Err(_) => {
            return Err(ApiResponse::unauthorized("无效的认证token").into_response());
        }
    };

    // 将用户信息添加到请求扩展中
    request.extensions_mut().insert(claims);

    // 继续处理请求
    Ok(next.run(request).await)
}

// 管理员权限中间件
pub async fn admin_middleware_compat(
    State((_db_config, settings)): State<(DatabaseConfig, Settings)>,
    mut request: Request,
    next: Next,
) -> Result<Response, Response> {
    // 先进行基本认证
    let auth_header = match request.headers().get("authorization") {
        Some(header) => match header.to_str() {
            Ok(header_str) => header_str,
            Err(_) => {
                return Err(ApiResponse::unauthorized("无效的认证头格式").into_response());
            }
        },
        None => {
            return Err(ApiResponse::unauthorized("缺少认证token").into_response());
        }
    };

    let token = match extract_token_from_header(auth_header) {
        Ok(token) => token,
        Err(_) => {
            return Err(ApiResponse::unauthorized("无效的token格式").into_response());
        }
    };

    let claims = match verify_jwt(&token, &settings.jwt.secret) {
        Ok(claims) => claims,
        Err(_) => {
            return Err(ApiResponse::unauthorized("无效的认证token").into_response());
        }
    };

    // 检查管理员权限
    if !claims.is_admin {
        return Err(ApiResponse::forbidden("需要管理员权限").into_response());
    }

    // 将用户信息添加到请求扩展中
    request.extensions_mut().insert(claims);

    // 继续处理请求
    Ok(next.run(request).await)
}



