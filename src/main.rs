mod config;
mod extractors;
mod models;
mod handlers;
mod services;
mod middleware;
mod utils;
mod tasks;
mod constants;

use axum::{
    routing::{get, post, put, delete},
    Router,
    middleware::from_fn_with_state,
};
use tower::ServiceBuilder;
use tower_http::{
    cors::Cors<PERSON>ayer,
    trace::TraceLayer,
};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
use std::net::SocketAddr;
use tokio::signal;

use config::{Settings, DatabaseConfig, DatabaseInitializer};
use utils::{success, AppResult};
use handlers::{
    auth::{login_handler, logout_handler, profile_handler, refresh_token_handler, verify_token_handler, get_user_permissions_handler},
    mobile_auth::{mobile_login_handler, get_mobile_user_info_handler, check_mobile_permission_handler, mobile_logout_handler},
    user::{list_users_handler, get_user_handler, create_user_handler, update_user_handler, delete_user_handler, list_unassigned_users_handler},
    role::{list_roles_handler, get_role_handler, create_role_handler, update_role_handler, delete_role_handler, assign_role_handler, assign_role_with_options_handler, remove_role_handler, get_users_by_role_handler},
    product::{list_products_handler, get_product_handler},
    operation::{list_operations_handler, get_operation_handler},
    equipment::{list_equipments_handler, get_equipment_handler},
    team::{list_teams_handler, get_team_handler, create_team_handler, update_team_handler, delete_team_handler, get_team_leaders_handler, assign_team_leader_handler, remove_team_leader_handler, get_team_members_handler, add_team_member_handler, remove_team_member_handler, batch_add_team_members_handler, batch_assign_team_leaders_handler, get_user_team_info_handler, get_user_role_team_status_handler, get_teams_by_workcenter_handler, unified_assign_leader_handler, unified_remove_leader_handler, get_available_team_members_handler},
    team_binding::{create_team_product, batch_create_team_products, get_team_products, delete_team_product, update_team_product_status, create_team_equipment, batch_create_team_equipments, get_team_equipments, delete_team_equipment, update_team_equipment_status, create_team_operation, batch_create_team_operations, get_team_operations, delete_team_operation, update_team_operation_status, get_team_available_resources, get_team_available_products, get_team_available_equipments, get_team_available_operations, validate_team_resources},
    borrow::{create_borrow_request_handler, approve_borrow_request_handler, get_borrow_requests_handler, get_borrowed_out_members_handler, get_borrowed_in_members_handler, update_borrow_status_handler},
    permission::{get_permission_definitions_handler, get_permission_tree_handler, validate_permissions_handler, check_user_permission_handler, update_role_permissions_handler, get_role_permission_overview_handler, get_resource_actions_handler, add_custom_resource_handler, delete_custom_resource_handler, delete_custom_resource_by_id_handler, list_custom_resources_handler},
    instruction_card::{create_instruction_card, batch_create_instruction_cards, get_instruction_card, query_instruction_cards, submit_instruction_card, leader_review_instruction_card, manager_review_instruction_card, update_leader_production_count, update_manager_production_count, delete_instruction_card, get_my_instruction_cards, get_my_submitted_instruction_cards, get_team_instruction_cards, get_workcenter_instruction_cards, get_instruction_card_statistics, get_instruction_card_audit_logs, get_team_available_members, batch_leader_review_instruction_cards, batch_manager_review_instruction_cards, team_leader_update_count, manager_update_count},
    flexible_entry::{create_flexible_entry, approve_flexible_entry, batch_approve_flexible_entries, get_flexible_entry_by_id, get_flexible_entries, get_available_team_members, team_leader_update_flexible_quantity, manager_update_approved_quantity},
    pre_maintained_flexible_work::{query_pre_maintained_flexible_works_handler, create_pre_maintained_flexible_work_handler, get_pre_maintained_flexible_work_handler, update_pre_maintained_flexible_work_handler, delete_pre_maintained_flexible_work_handler},
    workcenter::{query_workcenters, get_all_workcenters, get_workcenter_by_code},
    android_upgrade_config::{get_android_upgrade_configs, get_latest_android_upgrade_config, create_android_upgrade_config, delete_android_upgrade_config},
};
use middleware::auth_optimized::{auth_middleware_compat as auth_middleware};

#[tokio::main]
async fn main() -> AppResult<()> {
    // 智能加载环境变量 - 开发环境和发布环境使用不同策略
    load_env_file_smart();

    // 初始化日志
    init_tracing();

    // 调试：检查环境变量
    tracing::info!("DATABASE_URL: {:?}", std::env::var("DATABASE_URL"));
    tracing::info!("SOURCE_DATABASE_NAME: {:?}", std::env::var("SOURCE_DATABASE_NAME"));

    // 加载配置
    let settings = Settings::new()?;
    tracing::info!("配置加载成功");

    // 安全检查：显示权限检查状态
    if settings.security.enable_permission_check {
        tracing::info!("🔒 权限检查已启用 - 系统安全模式");
    } else {
        tracing::warn!("⚠️  权限检查已禁用 - 仅限开发环境使用！");
    }

    // 初始化数据库连接
    let db_config = DatabaseConfig::new(
        &settings.database.url,
        &settings.database.source_database_name,
        settings.database.max_connections,
        settings.database.acquire_timeout,
    ).await?;

    // 健康检查
    db_config.health_check().await?;
    tracing::info!("数据库连接健康检查通过");

    // 初始化数据库表结构（包括示例自定义权限）
    let db_initializer = DatabaseInitializer::new(db_config.clone());
    db_initializer.initialize().await?;

    // 启动连接池监控任务 - 修复监控任务崩溃导致的请求拥塞问题
    let db_monitor = db_config.clone();
    let _monitor_handle = tokio::spawn(async move {
        let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(60));
        let mut error_count = 0u32;
        let mut last_log_time = std::time::Instant::now();
        let mut last_state: Option<(u32, u32)> = None;

        // 跳过第一次tick，避免立即执行
        interval.tick().await;

        tracing::info!("连接池监控任务已启动，监控间隔: 60秒");

        loop {
            // 等待下一个间隔
            tokio::select! {
                _ = interval.tick() => {
                    // 直接获取连接池状态，避免spawn_blocking可能的失败
                    let pool_state = db_monitor.app_pool.state();
                    let connections = pool_state.connections;
                    let idle_connections = pool_state.idle_connections;

                    // 重置错误计数（因为成功获取了状态）
                    error_count = 0;

                    let now = std::time::Instant::now();
                    let should_log = match last_state {
                        Some((last_conn, last_idle)) => {
                            // 状态变化或超过5分钟或连接池状态异常
                            (connections != last_conn || idle_connections != last_idle) ||
                            now.duration_since(last_log_time).as_secs() >= 300 ||
                            connections == 0 || idle_connections == 0
                        }
                        None => true, // 首次运行
                    };

                    if should_log {
                        let waiting_requests = if connections >= idle_connections {
                            connections - idle_connections
                        } else {
                            0
                        };

                        if connections == 0 || idle_connections == 0 {
                            tracing::warn!(
                                "连接池状态异常 - 总连接数: {}, 空闲连接: {}, 等待连接的请求: {}",
                                connections, idle_connections, waiting_requests
                            );

                            // 连接池异常时，尝试触发连接池重建
                            tracing::info!("尝试通过健康检查触发连接池恢复...");
                            if let Err(e) = db_monitor.health_check().await {
                                tracing::error!("连接池健康检查失败: {}", e);
                            }
                        } else {
                            tracing::info!(
                                "连接池状态监控 - 总连接数: {}, 空闲连接: {}, 等待连接的请求: {}",
                                connections, idle_connections, waiting_requests
                            );
                        }

                        last_log_time = now;
                        last_state = Some((connections, idle_connections));
                    }
                }
                _ = tokio::signal::ctrl_c() => {
                    tracing::info!("连接池监控任务收到停止信号，正在退出...");
                    break;
                }
            }
        }

        tracing::info!("连接池监控任务已停止");
    });

    // 构建应用路由
    let app = create_app(db_config.clone(), settings.clone()).await?;

    // 启动服务器
    let addr = SocketAddr::from(([0, 0, 0, 0], settings.server.port));
    tracing::info!("服务器启动在 http://{}", addr);

    let listener = tokio::net::TcpListener::bind(addr).await?;

    // 优雅关闭处理
    tracing::info!("服务器已启动，按 Ctrl+C 优雅关闭");
    axum::serve(listener, app)
        .with_graceful_shutdown(shutdown_signal())
        .await?;

    tracing::info!("服务器已优雅关闭");
    Ok(())
}

/// 优雅关闭信号处理
async fn shutdown_signal() {
    let ctrl_c = async {
        signal::ctrl_c()
            .await
            .expect("failed to install Ctrl+C handler");
    };

    #[cfg(unix)]
    let terminate = async {
        signal::unix::signal(signal::unix::SignalKind::terminate())
            .expect("failed to install signal handler")
            .recv()
            .await;
    };

    #[cfg(not(unix))]
    let terminate = std::future::pending::<()>();

    tokio::select! {
        _ = ctrl_c => {
            tracing::info!("收到 Ctrl+C 信号，开始优雅关闭...");
        },
        _ = terminate => {
            tracing::info!("收到终止信号，开始优雅关闭...");
        },
    }
}

async fn create_app(db_config: DatabaseConfig, settings: Settings) -> AppResult<Router> {
    // CORS 配置
    let cors = CorsLayer::new()
        .allow_origin(
            settings.cors.allowed_origins
                .iter()
                .map(|origin| origin.parse().unwrap())
                .collect::<Vec<_>>()
        )
        .allow_methods(
            settings.cors.allowed_methods
                .iter()
                .map(|method| method.parse().unwrap())
                .collect::<Vec<_>>()
        )
        .allow_headers(
            settings.cors.allowed_headers
                .iter()
                .map(|header| header.parse().unwrap())
                .collect::<Vec<_>>()
        );

    // 创建基础认证路由组（不需要特殊权限）
    let basic_auth_routes = Router::new()
        // 需要认证的认证相关路由
        .route("/api/auth/logout", post(logout_handler))
        .route("/api/auth/profile", get(profile_handler))
        .route("/api/auth/verify", get(verify_token_handler))
        .route("/api/auth/permissions", get(get_user_permissions_handler))
        .route_layer(from_fn_with_state(
            (db_config.clone(), settings.clone()),
            auth_middleware
        ));

    // 创建需要认证的路由组（暂时不使用权限检查中间件）
    let protected_routes = Router::new()
        // 用户管理路由
        .route("/api/users", get(list_users_handler))
        .route("/api/users", post(create_user_handler))
        .route("/api/users/:id", get(get_user_handler))
        .route("/api/users/:id", put(update_user_handler))
        .route("/api/users/:id", delete(delete_user_handler))
        .route("/api/users/unassigned", get(list_unassigned_users_handler))

        // 角色管理路由
        .route("/api/roles", get(list_roles_handler))
        .route("/api/roles", post(create_role_handler))
        .route("/api/roles/:id", get(get_role_handler))
        .route("/api/roles/:id", put(update_role_handler))
        .route("/api/roles/:id", delete(delete_role_handler))
        .route("/api/roles/:id/users", get(get_users_by_role_handler))  // 新增：根据角色ID获取用户列表
        .route("/api/users/:user_id/roles/:role_id", post(assign_role_handler))
        .route("/api/users/:user_id/roles/:role_id", delete(remove_role_handler))
        // 增强版角色分配接口（支持智能班组分配）
        .route("/api/roles/assign-with-options", post(assign_role_with_options_handler))

        // 产品管理路由
        .route("/api/products", get(list_products_handler))
        .route("/api/products/:cinvcode", get(get_product_handler))

        // 工序管理路由
        .route("/api/operations", get(list_operations_handler))
        .route("/api/operations/:opcode", get(get_operation_handler))

        // 设备管理路由
        .route("/api/equipments", get(list_equipments_handler))
        .route("/api/equipments/:ceqcode", get(get_equipment_handler))

        // 班组管理路由
        .route("/api/teams", get(list_teams_handler))
        .route("/api/teams", post(create_team_handler))
        .route("/api/teams/:id", get(get_team_handler))
        .route("/api/teams/:id", put(update_team_handler))
        .route("/api/teams/:id", delete(delete_team_handler))
        .route("/api/teams/:id/leaders", get(get_team_leaders_handler))
        .route("/api/teams/:id/leaders", post(assign_team_leader_handler))
        .route("/api/teams/:id/leaders/:leader_psn_num", delete(remove_team_leader_handler))
        .route("/api/teams/:id/members", get(get_team_members_handler))
        .route("/api/teams/:id/members", post(add_team_member_handler))
        .route("/api/teams/:id/members/:member_psn_num", delete(remove_team_member_handler))
        .route("/api/teams/:id/members/batch", post(batch_add_team_members_handler))
        .route("/api/teams/:id/leaders/batch", post(batch_assign_team_leaders_handler))
        .route("/api/users/:psn_num/teams", get(get_user_team_info_handler))
        .route("/api/users/:psn_num/role-team-status", get(get_user_role_team_status_handler))
        // 新的统一接口
        .route("/api/teams/by-workcenter/:dept_code", get(get_teams_by_workcenter_handler))
        .route("/api/teams/assign-leader", post(unified_assign_leader_handler))
        .route("/api/teams/remove-leader/:psn_num", delete(unified_remove_leader_handler))
        // 班级可用成员查询接口
        .route("/api/teams/available-members", get(get_available_team_members_handler))

        // 班组绑定管理路由
        // 班组产品绑定
        .route("/api/team-bindings/products", post(create_team_product))
        .route("/api/team-bindings/products/batch", post(batch_create_team_products))
        .route("/api/team-bindings/products", get(get_team_products))
        .route("/api/team-bindings/products/:id", delete(delete_team_product))
        .route("/api/team-bindings/products/:id/status", put(update_team_product_status))

        // 班组设备绑定
        .route("/api/team-bindings/equipments", post(create_team_equipment))
        .route("/api/team-bindings/equipments/batch", post(batch_create_team_equipments))
        .route("/api/team-bindings/equipments", get(get_team_equipments))
        .route("/api/team-bindings/equipments/:id", delete(delete_team_equipment))
        .route("/api/team-bindings/equipments/:id/status", put(update_team_equipment_status))

        // 班组工序绑定
        .route("/api/team-bindings/operations", post(create_team_operation))
        .route("/api/team-bindings/operations/batch", post(batch_create_team_operations))
        .route("/api/team-bindings/operations", get(get_team_operations))
        .route("/api/team-bindings/operations/:id", delete(delete_team_operation))
        .route("/api/team-bindings/operations/:id/status", put(update_team_operation_status))

        // 班组可用资源查询（用于指令卡创建）
        .route("/api/teams/:team_id/available-resources", get(get_team_available_resources))
        .route("/api/teams/:team_id/available-products", get(get_team_available_products))
        .route("/api/teams/:team_id/available-equipments", get(get_team_available_equipments))
        .route("/api/teams/:team_id/available-operations", get(get_team_available_operations))
        .route("/api/teams/:team_id/validate-resources/:inventory_id/:equipment_id/:operation_id", get(validate_team_resources))

        // 借用功能路由
        .route("/api/teams/:id/borrow-request", post(create_borrow_request_handler))
        .route("/api/teams/:id/borrow-request/:borrow_id/approve", put(approve_borrow_request_handler))
        .route("/api/teams/:id/borrow-requests", get(get_borrow_requests_handler))
        .route("/api/teams/:id/borrowed-out-members", get(get_borrowed_out_members_handler))
        .route("/api/teams/:id/borrowed-in-members", get(get_borrowed_in_members_handler))
        .route("/api/borrow-requests/:borrow_id/status", put(update_borrow_status_handler))



        // 权限管理路由
        .route("/api/permissions/definitions", get(get_permission_definitions_handler))
        .route("/api/permissions/tree", get(get_permission_tree_handler))
        .route("/api/permissions/validate", post(validate_permissions_handler))
        .route("/api/permissions/check", post(check_user_permission_handler))
        .route("/api/roles/:id/permissions", put(update_role_permissions_handler))
        .route("/api/roles/:id/permissions/overview", get(get_role_permission_overview_handler))
        .route("/api/permissions/resources/actions", get(get_resource_actions_handler))
        .route("/api/permissions/custom/resources", post(add_custom_resource_handler))
        .route("/api/permissions/custom/resources", get(list_custom_resources_handler))
        .route("/api/permissions/custom/resources/:resource_name", delete(delete_custom_resource_handler))
        .route("/api/permissions/custom/resources/id/:id", delete(delete_custom_resource_by_id_handler))

        // 指令卡管理路由
        .route("/api/instruction-cards", post(create_instruction_card))
        .route("/api/instruction-cards/batch", post(batch_create_instruction_cards))
        .route("/api/instruction-cards/:id", get(get_instruction_card))
        .route("/api/instruction-cards", get(query_instruction_cards))
        .route("/api/instruction-cards/:id", delete(delete_instruction_card))
        .route("/api/instruction-cards/:id/submit", put(submit_instruction_card))
        .route("/api/instruction-cards/:id/leader-review", put(leader_review_instruction_card))
        .route("/api/instruction-cards/:id/manager-review", put(manager_review_instruction_card))
        .route("/api/instruction-cards/batch/leader-review", put(batch_leader_review_instruction_cards))
        .route("/api/instruction-cards/batch/manager-review", put(batch_manager_review_instruction_cards))
        .route("/api/instruction-cards/:id/leader-count", put(update_leader_production_count))
        .route("/api/instruction-cards/:id/manager-count", put(update_manager_production_count))
        .route("/api/instruction-cards/my-assignments", get(get_my_instruction_cards))
        .route("/api/instruction-cards/my-submissions", get(get_my_submitted_instruction_cards))
        .route("/api/instruction-cards/team/:team_id", get(get_team_instruction_cards))
        .route("/api/instruction-cards/workcenter", get(get_workcenter_instruction_cards))
        .route("/api/instruction-cards/statistics", get(get_instruction_card_statistics))
        .route("/api/instruction-cards/:id/audit-logs", get(get_instruction_card_audit_logs))
        .route("/api/instruction-cards/team/:team_id/available-members", get(get_team_available_members))
        .route("/api/instruction-cards/:id/team_leader_update_count/:count", put(team_leader_update_count)) // 仅根据id更新leader_custom_count字段
        .route("/api/instruction-cards/:id/manager_update_count/:count", put(manager_update_count)) // 仅根据id更新 manager_custom_count 字段

        // 零活录入管理路由
        .route("/api/flexible-entries", post(create_flexible_entry))
        .route("/api/flexible-entries/:id", get(get_flexible_entry_by_id))
        .route("/api/flexible-entries", get(get_flexible_entries))
        .route("/api/flexible-entries/:id/approve", put(approve_flexible_entry))
        .route("/api/flexible-entries/batch/approve", put(batch_approve_flexible_entries))
        .route("/api/flexible-entries/available-members", get(get_available_team_members))
        .route("/api/flexible-entries/:id/team_leader_update_quantity/:quantity", put(team_leader_update_flexible_quantity)) // 班长更新零活数量
        .route("/api/flexible-entries/:id/manager_update_quantity/:quantity", put(manager_update_approved_quantity)) // 负责人更新审核数量

        // 预维护零活管理路由
        .route("/api/pre-maintained-works", get(query_pre_maintained_flexible_works_handler))
        .route("/api/pre-maintained-works", post(create_pre_maintained_flexible_work_handler))
        .route("/api/pre-maintained-works/:opcode", get(get_pre_maintained_flexible_work_handler))
        .route("/api/pre-maintained-works/:opcode", put(update_pre_maintained_flexible_work_handler))
        .route("/api/pre-maintained-works/:opcode", delete(delete_pre_maintained_flexible_work_handler))

        // 工作中心管理路由
        .route("/api/workcenters", get(query_workcenters))
        .route("/api/workcenters/all", get(get_all_workcenters))
        .route("/api/workcenters/:dept_code", get(get_workcenter_by_code))

        // 安卓升级配置管理路由
        .route("/api/android-upgrade-configs", get(get_android_upgrade_configs))
        .route("/api/android-upgrade-configs", post(create_android_upgrade_config))
        .route("/api/android-upgrade-configs/:id", delete(delete_android_upgrade_config))

        // 应用认证中间件到所有受保护的路由
        .route_layer(from_fn_with_state(
            (db_config.clone(), settings.clone()),
            auth_middleware
        ));

    // 创建需要管理员权限的路由组（暂时为空）
    let admin_routes = Router::new();

    // 创建移动端需要认证的路由组
    let mobile_protected_routes = Router::new()
        .route("/api/auth/mobile/user", get(get_mobile_user_info_handler))
        .route("/api/auth/mobile/permission/check", post(check_mobile_permission_handler))
        .route("/api/auth/mobile/logout", post(mobile_logout_handler))
        .route_layer(from_fn_with_state(
            (db_config.clone(), settings.clone()),
            auth_middleware
        ));

    // 组合所有路由
    let app = Router::new()
        .route("/", get(root_handler))
        .route("/health", get(health_handler))
        // 无需认证的路由
        .route("/api/auth/login", post(login_handler))
        .route("/api/auth/refresh", post(refresh_token_handler))
        .route("/api/android-upgrade-configs/latest", get(get_latest_android_upgrade_config))
        // 移动端无需认证的路由
        .route("/api/auth/mobile/login", post(mobile_login_handler))
        // 合并基础认证路由
        .merge(basic_auth_routes)
        // 合并需要认证的路由
        .merge(protected_routes)
        // 合并移动端需要认证的路由
        .merge(mobile_protected_routes)
        // 合并需要管理员权限的路由
        .merge(admin_routes)
        .layer(
            ServiceBuilder::new()
                .layer(TraceLayer::new_for_http())
                .layer(cors)
        )
        .with_state((db_config, settings));

    Ok(app)
}

async fn root_handler() -> impl axum::response::IntoResponse {
    success("U8 权限管理系统 API 服务正在运行")
}

async fn health_handler() -> impl axum::response::IntoResponse {
    success("服务健康")
}

/// 智能加载环境变量文件
/// 开发环境：从项目根目录加载.env文件
/// 发布环境：从exe同级目录加载.env文件
fn load_env_file_smart() {
    // 检测是否在开发环境中（通过检查Cargo.toml文件是否存在）
    let is_dev_env = std::path::Path::new("Cargo.toml").exists();

    if is_dev_env {
        // 开发环境：使用原有方式从当前工作目录加载
        if let Err(e) = dotenvy::dotenv() {
            eprintln!("Warning: 无法加载.env文件: {}", e);
        }
    } else {
        // 发布环境：优先从exe同级目录加载
        let mut loaded = false;

        // 尝试从exe同级目录加载
        if let Ok(exe_path) = std::env::current_exe() {
            if let Some(exe_dir) = exe_path.parent() {
                let env_file = exe_dir.join(".env");
                if env_file.exists() {
                    if let Ok(_) = dotenvy::from_path(&env_file) {
                        loaded = true;
                    }
                }
            }
        }

        // 如果exe同级目录没有.env文件，尝试从当前工作目录加载
        if !loaded {
            if let Err(e) = dotenvy::dotenv() {
                eprintln!("Warning: 无法加载.env文件: {}", e);
            }
        }
    }
}

fn init_tracing() {
    use tracing_subscriber::fmt::time::OffsetTime;
    use time::{UtcOffset, format_description};

    // 创建中国时区偏移 (+8小时)
    let offset = UtcOffset::from_hms(8, 0, 0).unwrap();

    // 创建时间格式 (yyyy-MM-dd HH:mm:ss)
    let time_format = format_description::parse("[year]-[month]-[day] [hour]:[minute]:[second]")
        .expect("时间格式解析失败");

    let timer = OffsetTime::new(offset, time_format);

    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "u8_extend=debug,tower_http=debug".into()),
        )
        .with(
            tracing_subscriber::fmt::layer()
                .with_timer(timer)
        )
        .init();
}
