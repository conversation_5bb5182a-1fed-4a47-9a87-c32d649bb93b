use axum::{
    extract::{Path, Query, State},
    response::<PERSON><PERSON>,
};

use crate::{
    config::{DatabaseConfig, Settings},
    models::android_upgrade_config::*,
    services::android_upgrade_config_service::AndroidUpgradeConfigService,
    utils::{ApiResponse, AppResult},
    extractors::auth::AuthenticatedUser,
};
use crate::utils::permission_check::check_permission;

type AppState = (DatabaseConfig, Settings);

/// 获取安卓升级配置列表
pub async fn get_android_upgrade_configs(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Query(request): Query<AndroidUpgradeConfigQueryRequest>,
) -> AppResult<Json<ApiResponse<AndroidUpgradeConfigPageResponse>>> {
    // 权限检查：需要 android_upgrade_configs:read 权限
    if !check_permission(&auth_user, "android_upgrade_configs", "read", &db_config, &settings).await {
        return Err(crate::utils::AppError::Forbidden("权限不足，需要 android_upgrade_configs:read 权限".to_string()));
    }

    let service = AndroidUpgradeConfigService::new(db_config);
    let result = service.get_configs(request).await?;
    Ok(Json(ApiResponse::success(result)))
}

/// 获取最新的安卓升级配置
pub async fn get_latest_android_upgrade_config(
    State((db_config, _settings)): State<AppState>,
) -> AppResult<Json<ApiResponse<Option<AndroidUpgradeConfigResponse>>>> {
    let service = AndroidUpgradeConfigService::new(db_config);
    let result = service.get_latest_config().await?;
    Ok(Json(ApiResponse::success(result)))
}

/// 创建安卓升级配置
pub async fn create_android_upgrade_config(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Json(request): Json<CreateAndroidUpgradeConfigRequest>,
) -> AppResult<Json<ApiResponse<AndroidUpgradeConfigResponse>>> {
    // 权限检查：需要 android_upgrade_configs:create 权限
    if !check_permission(&auth_user, "android_upgrade_configs", "create", &db_config, &settings).await {
        return Err(crate::utils::AppError::Forbidden("权限不足，需要 android_upgrade_configs:create 权限".to_string()));
    }

    let service = AndroidUpgradeConfigService::new(db_config);
    let result = service.create_config(request).await?;
    Ok(Json(ApiResponse::success(result)))
}

/// 删除安卓升级配置
pub async fn delete_android_upgrade_config(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Path(id): Path<i32>,
) -> AppResult<Json<ApiResponse<()>>> {
    // 权限检查：需要 android_upgrade_configs:delete 权限
    if !check_permission(&auth_user, "android_upgrade_configs", "delete", &db_config, &settings).await {
        return Err(crate::utils::AppError::Forbidden("权限不足，需要 android_upgrade_configs:delete 权限".to_string()));
    }

    let service = AndroidUpgradeConfigService::new(db_config);
    service.delete_config(id).await?;
    Ok(Json(ApiResponse::success(())))
}
