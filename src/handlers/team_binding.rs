use axum::{
    extract::{Path, Query, State},
    response::IntoResponse,
    Json,
};
use crate::config::{DatabaseConfig, Settings};
use crate::models::team_binding::*;
use crate::services::TeamBindingService;
use crate::utils::response::ApiResponse;
use crate::extractors::AuthenticatedUser;
use std::sync::Arc;

type AppState = (DatabaseConfig, Settings);

// 权限检查辅助函数 - 支持配置开关
async fn check_permission(
    auth_user: &AuthenticatedUser,
    resource: &str,
    action: &str,
    db_config: &DatabaseConfig,
    settings: &Settings,
) -> bool {
    crate::utils::check_permission(auth_user, resource, action, db_config, settings).await
}

/// 创建班组产品绑定
pub async fn create_team_product(
    State((db_config, settings)): State<AppState>,
    auth_user: Authenticated<PERSON><PERSON>,
    <PERSON><PERSON>(request): <PERSON><PERSON><CreateTeamProductRequest>,
) -> impl IntoResponse {
    // 权限检查：需要 team_bindings:create 权限
    if !check_permission(&auth_user, "team_bindings", "create", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 team_bindings:create 权限").into_response();
    }

    let user_id = auth_user.user_id_string();

    let team_binding_service = TeamBindingService::new(Arc::new(db_config));
    match team_binding_service.create_team_product(&request, &user_id).await {
        Ok(id) => ApiResponse::success_with_data(serde_json::json!({"id": id})).into_response(),
        Err(e) => e.into_response(),
    }
}

/// 批量创建班组产品绑定
pub async fn batch_create_team_products(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Json(request): Json<BatchCreateTeamProductRequest>,
) -> impl IntoResponse {
    // 权限检查：需要 team_bindings:create 权限
    if !check_permission(&auth_user, "team_bindings", "create", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 team_bindings:create 权限").into_response();
    }

    let user_id = auth_user.user_id_string();

    let team_binding_service = TeamBindingService::new(Arc::new(db_config));
    match team_binding_service.batch_create_team_products(&request, &user_id).await {
        Ok(ids) => ApiResponse::success_with_data(serde_json::json!({"ids": ids, "count": ids.len()})).into_response(),
        Err(e) => e.into_response(),
    }
}

/// 获取班组产品绑定列表
pub async fn get_team_products(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Query(query): Query<TeamBindingQueryRequest>,
) -> impl IntoResponse {
    // 权限检查：需要 team_bindings:read 权限
    if !check_permission(&auth_user, "team_bindings", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 team_bindings:read 权限").into_response();
    }

    let team_binding_service = TeamBindingService::new(Arc::new(db_config));
    match team_binding_service.get_team_products(&query).await {
        Ok(response) => ApiResponse::success_with_data(response).into_response(),
        Err(e) => e.into_response(),
    }
}

/// 删除班组产品绑定
pub async fn delete_team_product(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Path(id): Path<i64>,
) -> impl IntoResponse {
    // 权限检查：需要 team_bindings:delete 权限
    if !check_permission(&auth_user, "team_bindings", "delete", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 team_bindings:delete 权限").into_response();
    }

    let team_binding_service = TeamBindingService::new(Arc::new(db_config));
    match team_binding_service.delete_team_product(id).await {
        Ok(_) => ApiResponse::success_empty_with_message("删除成功").into_response(),
        Err(e) => e.into_response(),
    }
}

/// 更新班组产品绑定状态
pub async fn update_team_product_status(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Path(id): Path<i64>,
    Json(request): Json<UpdateTeamBindingStatusRequest>,
) -> impl IntoResponse {
    // 权限检查：需要 team_bindings:update 权限
    if !check_permission(&auth_user, "team_bindings", "update", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 team_bindings:update 权限").into_response();
    }

    let team_binding_service = TeamBindingService::new(Arc::new(db_config));
    match team_binding_service.update_team_product_status(id, request.status).await {
        Ok(_) => ApiResponse::success_empty_with_message("更新成功").into_response(),
        Err(e) => e.into_response(),
    }
}

/// 创建班组设备绑定
pub async fn create_team_equipment(
    State((db_config, _settings)): State<AppState>,
    AuthenticatedUser(user): AuthenticatedUser,
    Json(request): Json<CreateTeamEquipmentRequest>,
) -> impl IntoResponse {
    let team_binding_service = TeamBindingService::new(Arc::new(db_config));
    match team_binding_service.create_team_equipment(&request, &user.sub).await {
        Ok(id) => ApiResponse::success_with_data(serde_json::json!({"id": id})).into_response(),
        Err(e) => e.into_response(),
    }
}

/// 批量创建班组设备绑定
pub async fn batch_create_team_equipments(
    State((db_config, _settings)): State<AppState>,
    AuthenticatedUser(user): AuthenticatedUser,
    Json(request): Json<BatchCreateTeamEquipmentRequest>,
) -> impl IntoResponse {
    let team_binding_service = TeamBindingService::new(Arc::new(db_config));
    match team_binding_service.batch_create_team_equipments(&request, &user.sub).await {
        Ok(ids) => ApiResponse::success_with_data(serde_json::json!({"ids": ids, "count": ids.len()})).into_response(),
        Err(e) => e.into_response(),
    }
}

/// 获取班组设备绑定列表
pub async fn get_team_equipments(
    State((db_config, _settings)): State<AppState>,
    Query(query): Query<TeamBindingQueryRequest>,
) -> impl IntoResponse {
    let team_binding_service = TeamBindingService::new(Arc::new(db_config));
    match team_binding_service.get_team_equipments(&query).await {
        Ok(response) => ApiResponse::success_with_data(response).into_response(),
        Err(e) => e.into_response(),
    }
}

/// 删除班组设备绑定
pub async fn delete_team_equipment(
    State((db_config, _settings)): State<AppState>,
    Path(id): Path<i64>,
) -> impl IntoResponse {
    let team_binding_service = TeamBindingService::new(Arc::new(db_config));
    match team_binding_service.delete_team_equipment(id).await {
        Ok(_) => ApiResponse::success_empty_with_message("删除成功").into_response(),
        Err(e) => e.into_response(),
    }
}

/// 更新班组设备绑定状态
pub async fn update_team_equipment_status(
    State((db_config, _settings)): State<AppState>,
    Path(id): Path<i64>,
    Json(request): Json<UpdateTeamBindingStatusRequest>,
) -> impl IntoResponse {
    let team_binding_service = TeamBindingService::new(Arc::new(db_config));
    match team_binding_service.update_team_equipment_status(id, request.status).await {
        Ok(_) => ApiResponse::success_empty_with_message("更新成功").into_response(),
        Err(e) => e.into_response(),
    }
}

/// 创建班组工序绑定
pub async fn create_team_operation(
    State((db_config, _settings)): State<AppState>,
    AuthenticatedUser(user): AuthenticatedUser,
    Json(request): Json<CreateTeamOperationRequest>,
) -> impl IntoResponse {
    let team_binding_service = TeamBindingService::new(Arc::new(db_config));
    match team_binding_service.create_team_operation(&request, &user.sub).await {
        Ok(id) => ApiResponse::success_with_data(serde_json::json!({"id": id})).into_response(),
        Err(e) => e.into_response(),
    }
}

/// 批量创建班组工序绑定
pub async fn batch_create_team_operations(
    State((db_config, _settings)): State<AppState>,
    AuthenticatedUser(user): AuthenticatedUser,
    Json(request): Json<BatchCreateTeamOperationRequest>,
) -> impl IntoResponse {
    let team_binding_service = TeamBindingService::new(Arc::new(db_config));
    match team_binding_service.batch_create_team_operations(&request, &user.sub).await {
        Ok(ids) => ApiResponse::success_with_data(serde_json::json!({"ids": ids, "count": ids.len()})).into_response(),
        Err(e) => e.into_response(),
    }
}

/// 获取班组工序绑定列表
pub async fn get_team_operations(
    State((db_config, _settings)): State<AppState>,
    Query(query): Query<TeamBindingQueryRequest>,
) -> impl IntoResponse {
    let team_binding_service = TeamBindingService::new(Arc::new(db_config));
    match team_binding_service.get_team_operations(&query).await {
        Ok(response) => ApiResponse::success_with_data(response).into_response(),
        Err(e) => e.into_response(),
    }
}

/// 删除班组工序绑定
pub async fn delete_team_operation(
    State((db_config, _settings)): State<AppState>,
    Path(id): Path<i64>,
) -> impl IntoResponse {
    let team_binding_service = TeamBindingService::new(Arc::new(db_config));
    match team_binding_service.delete_team_operation(id).await {
        Ok(_) => ApiResponse::success_empty_with_message("删除成功").into_response(),
        Err(e) => e.into_response(),
    }
}

/// 更新班组工序绑定状态
pub async fn update_team_operation_status(
    State((db_config, _settings)): State<AppState>,
    Path(id): Path<i64>,
    Json(request): Json<UpdateTeamBindingStatusRequest>,
) -> impl IntoResponse {
    let team_binding_service = TeamBindingService::new(Arc::new(db_config));
    match team_binding_service.update_team_operation_status(id, request.status).await {
        Ok(_) => ApiResponse::success_empty_with_message("更新成功").into_response(),
        Err(e) => e.into_response(),
    }
}

/// 获取班组的所有可用资源（产品、设备、工序）
pub async fn get_team_available_resources(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Path(team_id): Path<i32>,
) -> impl IntoResponse {
    // 统一权限检查：需要 team_resources:read 权限
    if !check_permission(&auth_user, "team_resources", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 team_resources:read 权限").into_response();
    }

    let user_id = auth_user.user_id_string();

    let team_binding_service = TeamBindingService::new(Arc::new(db_config));

    // 班组权限验证：非管理员只能查看自己班组的资源
    if !auth_user.is_admin() {
        match team_binding_service.check_user_team_permission(&user_id, team_id).await {
            Ok(has_permission) => {
                if !has_permission {
                    return ApiResponse::error(403, "无权限查看该班组资源").into_response();
                }
            }
            Err(e) => return e.into_response(),
        }
    }

    match team_binding_service.get_team_available_resources(team_id).await {
        Ok(response) => ApiResponse::success_with_data(response).into_response(),
        Err(e) => e.into_response(),
    }
}

/// 获取班组的可用产品列表
pub async fn get_team_available_products(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Path(team_id): Path<i32>,
) -> impl IntoResponse {
    // 统一权限检查：需要 team_resources:read 权限
    if !check_permission(&auth_user, "team_resources", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 team_resources:read 权限").into_response();
    }

    let user_id = auth_user.user_id_string();

    let team_binding_service = TeamBindingService::new(Arc::new(db_config));

    // 班组权限验证：非管理员只能查看自己班组的资源
    if !auth_user.is_admin() {
        match team_binding_service.check_user_team_permission(&user_id, team_id).await {
            Ok(has_permission) => {
                if !has_permission {
                    return ApiResponse::error(403, "无权限查看该班组资源").into_response();
                }
            }
            Err(e) => return e.into_response(),
        }
    }

    match team_binding_service.get_team_available_products(team_id).await {
        Ok(products) => ApiResponse::success_with_data(products).into_response(),
        Err(e) => e.into_response(),
    }
}

/// 获取班组的可用设备列表
pub async fn get_team_available_equipments(
    State((db_config, _settings)): State<AppState>,
    AuthenticatedUser(user): AuthenticatedUser,
    Path(team_id): Path<i32>,
) -> impl IntoResponse {
    let team_binding_service = TeamBindingService::new(Arc::new(db_config));

    // 权限验证：非管理员只能查看自己班组的资源
    if !user.is_admin {
        match team_binding_service.check_user_team_permission(&user.sub, team_id).await {
            Ok(has_permission) => {
                if !has_permission {
                    return ApiResponse::error(403, "无权限查看该班组资源").into_response();
                }
            }
            Err(e) => return e.into_response(),
        }
    }

    match team_binding_service.get_team_available_equipments(team_id).await {
        Ok(equipments) => ApiResponse::success_with_data(equipments).into_response(),
        Err(e) => e.into_response(),
    }
}

/// 获取班组的可用工序列表
pub async fn get_team_available_operations(
    State((db_config, _settings)): State<AppState>,
    AuthenticatedUser(user): AuthenticatedUser,
    Path(team_id): Path<i32>,
) -> impl IntoResponse {
    let team_binding_service = TeamBindingService::new(Arc::new(db_config));

    // 权限验证：非管理员只能查看自己班组的资源
    if !user.is_admin {
        match team_binding_service.check_user_team_permission(&user.sub, team_id).await {
            Ok(has_permission) => {
                if !has_permission {
                    return ApiResponse::error(403, "无权限查看该班组资源").into_response();
                }
            }
            Err(e) => return e.into_response(),
        }
    }

    match team_binding_service.get_team_available_operations(team_id).await {
        Ok(operations) => ApiResponse::success_with_data(operations).into_response(),
        Err(e) => e.into_response(),
    }
}

/// 验证班组是否可以使用指定的产品、设备、工序
pub async fn validate_team_resources(
    State((db_config, _settings)): State<AppState>,
    AuthenticatedUser(user): AuthenticatedUser,
    Path((team_id, inventory_id, equipment_id, operation_id)): Path<(i32, String, String, String)>,
) -> impl IntoResponse {
    let team_binding_service = TeamBindingService::new(Arc::new(db_config));

    // 权限验证：非管理员只能验证自己班组的资源
    if !user.is_admin {
        match team_binding_service.check_user_team_permission(&user.sub, team_id).await {
            Ok(has_permission) => {
                if !has_permission {
                    return ApiResponse::error(403, "无权限验证该班组资源").into_response();
                }
            }
            Err(e) => return e.into_response(),
        }
    }

    match team_binding_service.validate_team_resources(team_id, &inventory_id, &equipment_id, &operation_id).await {
        Ok(is_valid) => ApiResponse::success_with_data(serde_json::json!({"valid": is_valid})).into_response(),
        Err(e) => e.into_response(),
    }
}