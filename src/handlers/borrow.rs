// 借用功能处理器
use axum::{
    extract::{Path, Query, State},
    response::IntoResponse,
    Json,
};
use crate::{
    config::DatabaseConfig,
    extractors::AuthenticatedUser,
    models::{CreateBorrowRequest, ApproveBorrowRequest, BorrowRequestQuery, UpdateBorrowStatusRequest},
    services::BorrowService,
    utils::ApiResponse,
};

type AppState = (DatabaseConfig, crate::config::Settings);

// 权限检查辅助函数 - 支持配置开关
async fn check_permission(
    auth_user: &AuthenticatedUser,
    resource: &str,
    action: &str,
    db_config: &DatabaseConfig,
    settings: &crate::config::Settings,
) -> bool {
    crate::utils::check_permission(auth_user, resource, action, db_config, settings).await
}

/// 发起借用申请
pub async fn create_borrow_request_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Path(team_id): Path<i32>,
    Json(request): <PERSON><PERSON><CreateBorrowRequest>,
) -> impl IntoResponse {
    // 权限检查：需要 teams:borrow 权限
    if !check_permission(&auth_user, "teams", "borrow", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 teams:borrow 权限").into_response();
    }

    // 验证权限：只有班长才能发起借用申请（业务逻辑检查）
    if !auth_user.is_admin() {
        // 检查用户是否是指定班组的班长
        let borrow_service = BorrowService::new(db_config.clone());
        let user_psn_num = auth_user.user_id_string();

        match borrow_service.is_team_leader(team_id, &user_psn_num).await {
            Ok(is_leader) => {
                if !is_leader {
                    return ApiResponse::error(403, "只有班长才能发起借用申请").into_response();
                }
            }
            Err(e) => {
                tracing::error!("检查班长权限失败: {}", e);
                return ApiResponse::error(500, "权限验证失败").into_response();
            }
        }
    }

    let borrow_service = BorrowService::new(db_config);
    let requested_by = auth_user.user_id_string();

    match borrow_service.create_borrow_request(team_id, &request, &requested_by).await {
        Ok(borrow_id) => {
            ApiResponse::success_with_message(
                serde_json::json!({ "borrow_id": borrow_id }),
                "借用申请创建成功"
            ).into_response()
        }
        Err(e) => {
            tracing::error!("创建借用申请失败: {}", e);
            match e {
                crate::utils::AppError::Validation(msg) => {
                    ApiResponse::error(400, &msg).into_response()
                }
                _ => {
                    ApiResponse::error(500, "创建借用申请失败").into_response()
                }
            }
        }
    }
}

/// 审批借用申请
pub async fn approve_borrow_request_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Path((team_id, borrow_id)): Path<(i32, i32)>,
    Json(request): Json<ApproveBorrowRequest>,
) -> impl IntoResponse {
    // 权限检查：需要 teams:approve 权限
    if !check_permission(&auth_user, "teams", "approve", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 teams:approve 权限").into_response();
    }

    let borrow_service = BorrowService::new(db_config.clone());

    // 验证借用申请是否属于指定的班组
    match borrow_service.validate_borrow_request_team(borrow_id, team_id).await {
        Ok(is_valid) => {
            if !is_valid {
                return ApiResponse::error(400, "借用申请与指定班组不匹配").into_response();
            }
        }
        Err(e) => {
            tracing::error!("验证借用申请班组失败: {}", e);
            return ApiResponse::error(500, "验证失败").into_response();
        }
    }

    // 验证权限：只有原班组的班长才能审批借用申请
    if !auth_user.is_admin() {
        let user_psn_num = auth_user.user_id_string();

        match borrow_service.is_original_team_leader(borrow_id, &user_psn_num).await {
            Ok(is_leader) => {
                if !is_leader {
                    return ApiResponse::error(403, "只有被借用人员的原班组班长才能审批借用申请").into_response();
                }
            }
            Err(e) => {
                tracing::error!("检查审批权限失败: {}", e);
                return ApiResponse::error(500, "权限验证失败").into_response();
            }
        }
    }

    let borrow_service = BorrowService::new(db_config);
    let approved_by = auth_user.user_id_string();

    match borrow_service.approve_borrow_request(borrow_id, &request, &approved_by).await {
        Ok(()) => {
            let action_text = if request.action == "approve" { "同意" } else { "拒绝" };
            ApiResponse::success_with_message(
                (),
                &format!("借用申请{}成功", action_text)
            ).into_response()
        }
        Err(e) => {
            tracing::error!("审批借用申请失败: {}", e);
            match e {
                crate::utils::AppError::Validation(msg) => {
                    ApiResponse::error(400, &msg).into_response()
                }
                _ => {
                    ApiResponse::error(500, "审批借用申请失败").into_response()
                }
            }
        }
    }
}

/// 查询借用申请列表
pub async fn get_borrow_requests_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Path(team_id): Path<i32>,
    Query(query): Query<BorrowRequestQuery>,
) -> impl IntoResponse {
    // 权限检查：需要 teams:read 权限
    if !check_permission(&auth_user, "teams", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 teams:read 权限").into_response();
    }

    let borrow_service = BorrowService::new(db_config);

    match borrow_service.get_borrow_requests(team_id, &query).await {
        Ok(response) => {
            ApiResponse::success_with_message(response, "获取借用申请列表成功").into_response()
        }
        Err(e) => {
            tracing::error!("获取借用申请列表失败: {}", e);
            ApiResponse::error(500, "获取借用申请列表失败").into_response()
        }
    }
}

/// 查询被借走的人员列表
pub async fn get_borrowed_out_members_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Path(team_id): Path<i32>,
) -> impl IntoResponse {
    // 权限检查：需要 teams:read 权限
    if !check_permission(&auth_user, "teams", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 teams:read 权限").into_response();
    }

    let borrow_service = BorrowService::new(db_config);

    match borrow_service.get_borrowed_out_members(team_id).await {
        Ok(members) => {
            ApiResponse::success_with_message(members, "获取被借走人员列表成功").into_response()
        }
        Err(e) => {
            tracing::error!("获取被借走人员列表失败: {}", e);
            ApiResponse::error(500, "获取被借走人员列表失败").into_response()
        }
    }
}

/// 查询借用来的人员列表
pub async fn get_borrowed_in_members_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Path(team_id): Path<i32>,
) -> impl IntoResponse {
    // 权限检查：需要 teams:read 权限
    if !check_permission(&auth_user, "teams", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 teams:read 权限").into_response();
    }

    let borrow_service = BorrowService::new(db_config);

    match borrow_service.get_borrowed_in_members(team_id).await {
        Ok(members) => {
            ApiResponse::success_with_message(members, "获取借用人员列表成功").into_response()
        }
        Err(e) => {
            tracing::error!("获取借用人员列表失败: {}", e);
            ApiResponse::error(500, "获取借用人员列表失败").into_response()
        }
    }
}

/// 修改借用状态（主动归还）
pub async fn update_borrow_status_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Path(borrow_id): Path<i32>,
    Json(request): Json<UpdateBorrowStatusRequest>,
) -> impl IntoResponse {
    // 权限检查：需要 teams:update 权限
    if !check_permission(&auth_user, "teams", "update", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 teams:update 权限").into_response();
    }

    // 业务权限验证：只有管理员或原班组班长才能修改借用状态
    if !auth_user.is_admin() {
        let borrow_service = BorrowService::new(db_config.clone());
        let user_psn_num = auth_user.user_id_string();

        match borrow_service.is_original_team_leader(borrow_id, &user_psn_num).await {
            Ok(is_leader) => {
                if !is_leader {
                    return ApiResponse::error(403, "只有原班组的班长才能修改借用状态").into_response();
                }
            }
            Err(e) => {
                tracing::error!("检查班长权限失败: {}", e);
                return ApiResponse::error(500, "权限验证失败").into_response();
            }
        }
    }

    let borrow_service = BorrowService::new(db_config);
    let updated_by = auth_user.user_id_string();

    match borrow_service.update_borrow_status(borrow_id, &request, &updated_by).await {
        Ok(_) => {
            ApiResponse::success_with_message(
                serde_json::json!({"borrow_id": borrow_id}),
                "借用状态修改成功"
            ).into_response()
        }
        Err(e) => {
            tracing::error!("修改借用状态失败: {}", e);
            match e {
                crate::utils::AppError::Validation(msg) => {
                    ApiResponse::error(400, &msg).into_response()
                }
                _ => {
                    ApiResponse::error(500, "修改借用状态失败").into_response()
                }
            }
        }
    }
}
