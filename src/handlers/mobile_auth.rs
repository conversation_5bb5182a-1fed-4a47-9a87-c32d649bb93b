// 移动端认证处理器
use axum::{
    extract::{Query, State},
    response::IntoResponse,
    Json,
};
use serde::{Deserialize, Serialize};


use crate::config::{DatabaseConfig, Settings};
use crate::services::AuthService;
use crate::utils::{ApiResponse, RoleLevel};
use crate::models::LoginRequest;

#[derive(Debug, Serialize)]
struct MobilePermissionResult {
    allowed: bool,
    feature: String,
    action: Option<String>,
    role_level: u8,
    reason: String,
}

/// 移动端登录请求
#[derive(Debug, Deserialize)]
pub struct MobileLoginRequest {
    pub username: String,
    pub password: String,
    /// 设备信息
    pub device_info: Option<MobileDeviceInfo>,
}

/// 移动设备信息
#[derive(Debug, Deserialize, Serialize)]
pub struct MobileDeviceInfo {
    pub device_id: Option<String>,
    pub device_type: Option<String>, // "ios", "android", "web"
    pub app_version: Option<String>,
    pub os_version: Option<String>,
    pub device_name: Option<String>,
}

/// 移动端登录响应
#[derive(Debug, Serialize)]
pub struct MobileLoginResponse {
    pub access_token: String,
    pub refresh_token: String,
    pub expires_in: i64,
    pub user_info: MobileUserInfo,
    pub permissions: Vec<MobilePermission>,
    pub mobile_features: Vec<String>,
}

/// 移动端用户信息
#[derive(Debug, Serialize)]
pub struct MobileUserInfo {
    pub user_id: String, // 改为String类型，对应cpsn_num
    pub username: String,
    pub nickname: Option<String>,
    pub role_name: String,
    pub role_level: u8,
    pub is_admin: bool,
    pub avatar_url: Option<String>,
    pub mobile_features: Vec<String>,
}

/// 移动端权限信息
#[derive(Debug, Serialize)]
pub struct MobilePermission {
    pub resource: String,
    pub actions: Vec<String>,
    pub display_name: String,
}

/// 移动端登录处理器
pub async fn mobile_login_handler(
    State((db_config, settings)): State<(DatabaseConfig, Settings)>,
    Json(request): Json<MobileLoginRequest>,
) -> impl IntoResponse {
    // 创建认证服务
    let expires_in = settings.jwt.expires_in.parse::<i64>().unwrap_or(86400);
    let refresh_expires_in = settings.jwt.refresh_expires_in.parse::<i64>().unwrap_or(604800);
    let auth_service = AuthService::new(
        db_config.clone(),
        settings.jwt.secret.clone(),
        chrono::Duration::seconds(expires_in),
        chrono::Duration::seconds(refresh_expires_in)
    );

    // 转换为标准登录请求
    let login_request = LoginRequest {
        username: request.username.clone(),
        password: request.password.clone(),
    };

    // 执行登录验证
    match auth_service.login(&login_request).await {
        Ok(login_response) => {
            // 获取用户角色信息 - 从roles中获取第一个角色
            let role_name = if let Some(roles) = &login_response.user.roles {
                if let Some(first_role) = roles.first() {
                    first_role.name.clone()
                } else {
                    "user".to_string()
                }
            } else {
                "user".to_string()
            };

            let user_role_level = RoleLevel::from_role_name(&role_name);

            // 移动端允许所有角色登录
            // 不像Web端那样限制User角色

            // 根据角色级别确定移动端功能
            let mobile_features = get_mobile_features_by_role(&db_config, &user_role_level).await;

            // 创建简化的权限列表
            let mobile_permissions = create_mobile_permissions_by_role(&db_config, &user_role_level).await;

            // 保存role_level的数值用于后续使用
            let role_level_value = user_role_level.clone() as u8;

            // 构建移动端响应
            let token_expires_in = expires_in; // 使用配置的过期时间
            let mobile_response = MobileLoginResponse {
                access_token: login_response.token.clone(),
                refresh_token: login_response.token.clone(), // JWT无状态，使用相同token
                expires_in: token_expires_in,
                user_info: MobileUserInfo {
                    user_id: login_response.user.cpsn_num.clone(),
                    username: login_response.user.username.clone(),
                    nickname: Some(login_response.user.name.clone()),
                    role_name: role_name.clone(),
                    role_level: role_level_value,
                    is_admin: login_response.user.is_admin,
                    avatar_url: None, // 头像功能未实现
                    mobile_features: mobile_features.clone(),
                },
                permissions: mobile_permissions,
                mobile_features,
            };

            // 记录移动端登录日志
            if let Some(device_info) = &request.device_info {
                tracing::info!(
                    "移动端登录成功: 用户={}, 角色={}, 设备类型={:?}, 设备ID={:?}",
                    request.username,
                    user_role_level,
                    device_info.device_type,
                    device_info.device_id
                );
            } else {
                tracing::info!(
                    "移动端登录成功: 用户={}, 角色={}",
                    request.username,
                    user_role_level
                );
            }

            ApiResponse::success_with_data(mobile_response).into_response()
        }
        Err(e) => {
            tracing::warn!("移动端登录失败: 用户={}, 错误={}", request.username, e);
            ApiResponse::unauthorized(format!("登录失败: {}", e)).into_response()
        }
    }
}

/// 根据角色级别获取移动端功能列表（从数据库获取真实权限）
async fn get_mobile_features_by_role(db_config: &DatabaseConfig, role_level: &RoleLevel) -> Vec<String> {
    let mut features = Vec::new();

    // 从数据库查询角色的真实权限
    if let Ok(mut client) = db_config.get_app_connection().await {
        let role_name = match role_level {
            RoleLevel::Admin => "admin",
            RoleLevel::Manager => "manager",
            RoleLevel::TeamLeader => "team_leader",
            RoleLevel::User => "user",
        };

        let mut query = tiberius::Query::new("SELECT permissions FROM roles WHERE name = @P1");
        query.bind(role_name);

        if let Ok(stream) = query.query(&mut *client).await {
            if let Ok(rows) = stream.into_first_result().await {
                if let Some(row) = rows.first() {
                    if let Some(permissions_json) = row.get::<&str, _>(0) {
                        if let Ok(perms_value) = serde_json::from_str::<serde_json::Value>(permissions_json) {
                            if let Some(perms_array) = perms_value.get("permissions").and_then(|p| p.as_array()) {
                                for perm in perms_array {
                                    if let Some(resource) = perm.get("resource").and_then(|r| r.as_str()) {
                                        match resource {
                                            "users" => features.push("user_management".to_string()),
                                            "roles" => features.push("role_management".to_string()),
                                            "auth" => features.push("auth_management".to_string()),
                                            "permissions" => features.push("permission_management".to_string()),
                                            _ => {}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // 如果没有从数据库获取到权限，提供最基础的认证功能
    if features.is_empty() {
        features.push("auth_management".to_string());
    }

    features.sort();
    features.dedup();
    features
}

/// 根据角色创建移动端权限列表（从数据库获取真实权限）
async fn create_mobile_permissions_by_role(db_config: &DatabaseConfig, role_level: &RoleLevel) -> Vec<MobilePermission> {
    let mut permissions = Vec::new();

    // 从数据库查询角色的真实权限
    if let Ok(mut client) = db_config.get_app_connection().await {
        let role_name = match role_level {
            RoleLevel::Admin => "admin",
            RoleLevel::Manager => "manager",
            RoleLevel::TeamLeader => "team_leader",
            RoleLevel::User => "user",
        };

        let mut query = tiberius::Query::new("SELECT permissions FROM roles WHERE name = @P1");
        query.bind(role_name);

        if let Ok(stream) = query.query(&mut *client).await {
            if let Ok(rows) = stream.into_first_result().await {
                if let Some(row) = rows.first() {
                    if let Some(permissions_json) = row.get::<&str, _>(0) {
                        if let Ok(perms_value) = serde_json::from_str::<serde_json::Value>(permissions_json) {
                            if let Some(perms_array) = perms_value.get("permissions").and_then(|p| p.as_array()) {
                                for perm in perms_array {
                                    if let Some(perm_obj) = perm.as_object() {
                                        if let (Some(resource), Some(actions)) = (
                                            perm_obj.get("resource").and_then(|r| r.as_str()),
                                            perm_obj.get("actions").and_then(|a| a.as_array())
                                        ) {
                                            let action_strings: Vec<String> = actions.iter()
                                                .filter_map(|a| a.as_str())
                                                .map(|s| s.to_string())
                                                .collect();

                                            let display_name = match resource {
                                                "auth" => "认证管理",
                                                "users" => "用户管理",
                                                "roles" => "角色管理",
                                                "permissions" => "权限管理",
                                                _ => resource,
                                            };

                                            if !action_strings.is_empty() {
                                                permissions.push(MobilePermission {
                                                    resource: resource.to_string(),
                                                    actions: action_strings,
                                                    display_name: display_name.to_string(),
                                                });
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // 如果没有从数据库获取到权限，提供最基础的认证权限
    if permissions.is_empty() {
        permissions.push(MobilePermission {
            resource: "auth".to_string(),
            actions: vec!["login".to_string(), "logout".to_string(), "verify".to_string(), "profile".to_string()],
            display_name: "认证管理".to_string(),
        });
    }

    permissions
}

/// 移动端用户信息查询
#[derive(Debug, Deserialize)]
pub struct MobileUserQuery {
    // 预留字段，用于未来扩展
}

/// 获取移动端用户信息
pub async fn get_mobile_user_info_handler(
    auth_user: crate::extractors::AuthenticatedUser,
    State((db_config, _settings)): State<(DatabaseConfig, Settings)>,
    Query(_query): Query<MobileUserQuery>,
) -> impl IntoResponse {
    // 从认证中间件获取当前用户信息
    let user_claims = auth_user.claims();

    // 获取用户角色信息
    let role_service = crate::services::RoleService::new(db_config.clone());
    let user_roles = match role_service.get_user_roles(user_claims.sub.clone()).await {
        Ok(roles) => roles,
        Err(e) => {
            return ApiResponse::internal_error(format!("获取用户角色失败: {}", e)).into_response();
        }
    };

    // 获取第一个角色作为主要角色
    let (role_name, role_level_value) = if let Some(first_role) = user_roles.first() {
        let role_level = RoleLevel::from_level(first_role.level);
        (first_role.name.clone(), role_level as u8)
    } else {
        ("user".to_string(), RoleLevel::User as u8)
    };

    let role_level = RoleLevel::from_role_name(&role_name);

    let mobile_features = get_mobile_features_by_role(&db_config, &role_level).await;
    let user_info = MobileUserInfo {
        user_id: user_claims.sub.clone(),
        username: user_claims.username.clone(),
        nickname: Some(user_claims.username.clone()),
        role_name,
        role_level: role_level_value,
        is_admin: user_claims.is_admin,
        avatar_url: None,
        mobile_features,
    };

    ApiResponse::success_with_data(user_info).into_response()
}

/// 移动端权限检查
#[derive(Debug, Deserialize)]
pub struct MobilePermissionCheckRequest {
    pub feature: String,
    pub action: Option<String>,
}

/// 移动端权限检查处理器
pub async fn check_mobile_permission_handler(
    auth_user: crate::extractors::AuthenticatedUser,
    State((db_config, _settings)): State<(DatabaseConfig, Settings)>,
    Json(request): Json<MobilePermissionCheckRequest>,
) -> impl IntoResponse {
    // 从认证中间件获取用户信息
    let user_claims = auth_user.claims();

    // 获取用户角色信息
    let role_service = crate::services::RoleService::new(db_config.clone());
    let user_roles = match role_service.get_user_roles(user_claims.sub.clone()).await {
        Ok(roles) => roles,
        Err(e) => {
            return ApiResponse::internal_error(format!("获取用户角色失败: {}", e)).into_response();
        }
    };

    // 获取用户角色级别
    let role_level = if let Some(first_role) = user_roles.first() {
        RoleLevel::from_level(first_role.level)
    } else {
        RoleLevel::User
    };

    // 根据角色级别和功能进行权限检查（基于系统实际功能）
    let allowed = match request.feature.as_str() {
        "user_management" => {
            // 只有Admin和Manager可以管理用户
            matches!(role_level, RoleLevel::Admin | RoleLevel::Manager)
        },
        "auth_management" => {
            // 所有用户都可以进行认证相关操作
            true
        },
        "role_management" => {
            // 只有Admin可以管理角色
            matches!(role_level, RoleLevel::Admin)
        },
        _ => {
            // 未知功能，默认拒绝
            false
        }
    };

    let reason = if allowed {
        "权限验证通过"
    } else {
        match role_level {
            RoleLevel::Admin => "管理员权限验证失败",
            RoleLevel::Manager => "管理员权限不足",
            RoleLevel::TeamLeader => "团队领导权限不足",
            RoleLevel::User => "普通用户权限不足",
        }
    };

    let permission_result = MobilePermissionResult {
        allowed,
        feature: request.feature,
        action: request.action,
        role_level: role_level as u8,
        reason: reason.to_string(),
    };

    ApiResponse::success_with_data(permission_result).into_response()
}

/// 移动端登出处理器
pub async fn mobile_logout_handler(
    State((_db_config, _settings)): State<(DatabaseConfig, Settings)>,
) -> impl IntoResponse {
    // 移动端登出逻辑 - 当前为无状态JWT，无需特殊处理
    tracing::info!("移动端用户登出");

    ApiResponse::success_empty_with_message("登出成功").into_response()
}
