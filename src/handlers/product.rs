use crate::config::{DatabaseConfig, Settings};
use crate::models::product::ProductQueryRequest;
use crate::services::product_service::ProductService;
use crate::utils::ApiResponse;
use crate::extractors::AuthenticatedUser;
use axum::{
    extract::{Path, Query, State},
    response::IntoResponse,
};
use std::sync::Arc;

type AppState = (DatabaseConfig, Settings);

// 权限检查辅助函数 - 支持配置开关
async fn check_permission(
    auth_user: &AuthenticatedUser,
    resource: &str,
    action: &str,
    db_config: &DatabaseConfig,
    settings: &Settings,
) -> bool {
    crate::utils::check_permission(auth_user, resource, action, db_config, settings).await
}

/// 获取产品列表
pub async fn list_products_handler(
    State((db_config, _settings)): State<AppState>,
    Query(request): Query<ProductQueryRequest>,
) -> impl IntoResponse {
    let product_service = ProductService::new(Arc::new(db_config));

    match product_service.get_products(request).await {
        Ok(response) => {
            ApiResponse::success_with_message(response, "获取产品列表成功").into_response()
        }
        Err(e) => {
            tracing::error!("获取产品列表失败: {}", e);
            ApiResponse::internal_error("获取产品列表失败").into_response()
        }
    }
}

/// 根据物料编码获取单个产品
pub async fn get_product_handler(
    State((db_config, _settings)): State<AppState>,
    Path(cinvcode): Path<String>,
) -> impl IntoResponse {
    let product_service = ProductService::new(Arc::new(db_config));

    match product_service.get_product_by_code(&cinvcode).await {
        Ok(product) => {
            ApiResponse::success_with_message(product, "获取产品信息成功").into_response()
        }
        Err(e) => {
            tracing::error!("获取产品信息失败: {}", e);
            ApiResponse::internal_error("获取产品信息失败").into_response()
        }
    }
}
