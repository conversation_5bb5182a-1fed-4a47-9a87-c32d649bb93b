use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
};

use crate::{
    config::{DatabaseConfig, Settings},
    models::*,
    services::InstructionCardService,
    utils::{ApiResponse, AppResult},
    extractors::auth::AuthenticatedUser,
};

type AppState = (DatabaseConfig, Settings);

// 权限检查辅助函数 - 支持配置开关
async fn check_permission(
    auth_user: &AuthenticatedUser,
    resource: &str,
    action: &str,
    db_config: &DatabaseConfig,
    settings: &Settings,
) -> bool {
    crate::utils::check_permission(auth_user, resource, action, db_config, settings).await
}

/// 创建指令卡
pub async fn create_instruction_card(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    J<PERSON>(request): <PERSON><PERSON><CreateInstructionCardRequest>,
) -> AppResult<Json<ApiResponse<InstructionCardResponse>>> {
    // 权限检查：需要 instruction_cards:create 权限
    if !check_permission(&auth_user, "instruction_cards", "create", &db_config, &settings).await {
        return Err(crate::utils::AppError::Forbidden("权限不足，需要 instruction_cards:create 权限".to_string()));
    }
    let service = InstructionCardService::new(db_config);
    let result = service.create_instruction_card(request, auth_user.user_id_string().to_string()).await?;
    Ok(Json(ApiResponse::success(result)))
}

/// 批量创建指令卡
pub async fn batch_create_instruction_cards(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Json(request): Json<BatchCreateInstructionCardRequest>,
) -> AppResult<Json<ApiResponse<Vec<InstructionCardResponse>>>> {
    // 权限检查：需要 instruction_cards:create 权限
    if !check_permission(&auth_user, "instruction_cards", "create", &db_config, &settings).await {
        return Err(crate::utils::AppError::Forbidden("权限不足，需要 instruction_cards:create 权限".to_string()));
    }
    let service = InstructionCardService::new(db_config);
    let result = service.batch_create_instruction_cards(request, auth_user.user_id_string().to_string()).await?;
    Ok(Json(ApiResponse::success(result)))
}

/// 获取指令卡详情
pub async fn get_instruction_card(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Path(id): Path<i64>,
) -> AppResult<Json<ApiResponse<InstructionCardResponse>>> {
    // 权限检查：需要 instruction_cards:read 权限
    if !check_permission(&auth_user, "instruction_cards", "read", &db_config, &settings).await {
        return Err(crate::utils::AppError::Forbidden("权限不足，需要 instruction_cards:read 权限".to_string()));
    }

    let service = InstructionCardService::new(db_config);
    let result = service.get_instruction_card_by_id(id).await?;
    Ok(Json(ApiResponse::success(result)))
}

/// 查询指令卡列表
pub async fn query_instruction_cards(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Query(request): Query<InstructionCardQueryRequest>,
) -> AppResult<Json<ApiResponse<InstructionCardPageResponse>>> {
    // 权限检查：需要 instruction_cards:read 权限
    if !check_permission(&auth_user, "instruction_cards", "read", &db_config, &settings).await {
        return Err(crate::utils::AppError::Forbidden("权限不足，需要 instruction_cards:read 权限".to_string()));
    }

    let service = InstructionCardService::new(db_config);
    let result = service.query_instruction_cards(request).await?;
    Ok(Json(ApiResponse::success(result)))
}

/// 提交指令卡（员工填写完成数量）
pub async fn submit_instruction_card(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Path(id): Path<i64>,
    Json(request): Json<SubmitInstructionCardRequest>,
) -> AppResult<Json<ApiResponse<InstructionCardResponse>>> {
    // 权限检查：需要 instruction_cards:submit 权限
    if !check_permission(&auth_user, "instruction_cards", "submit", &db_config, &settings).await {
        return Err(crate::utils::AppError::Forbidden("权限不足，需要 instruction_cards:submit 权限".to_string()));
    }
    let service = InstructionCardService::new(db_config);
    let result = service.submit_instruction_card(id, request, auth_user.user_id_string().to_string()).await?;
    Ok(Json(ApiResponse::success(result)))
}

/// 班长审核指令卡
pub async fn leader_review_instruction_card(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Path(id): Path<i64>,
    Json(request): Json<ReviewInstructionCardRequest>,
) -> AppResult<Json<ApiResponse<InstructionCardResponse>>> {
    // 权限检查：需要 instruction_cards:review 权限
    if !check_permission(&auth_user, "instruction_cards", "review", &db_config, &settings).await {
        return Err(crate::utils::AppError::Forbidden("权限不足，需要 instruction_cards:review 权限".to_string()));
    }
    let service = InstructionCardService::new(db_config);
    let result = service.leader_review_instruction_card(id, request, auth_user.user_id_string().to_string()).await?;
    Ok(Json(ApiResponse::success(result)))
}

/// 负责人审核指令卡
pub async fn manager_review_instruction_card(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Path(id): Path<i64>,
    Json(request): Json<ReviewInstructionCardRequest>,
) -> AppResult<Json<ApiResponse<InstructionCardResponse>>> {
    // 权限检查：需要 instruction_cards:review 权限
    if !check_permission(&auth_user, "instruction_cards", "review", &db_config, &settings).await {
        return Err(crate::utils::AppError::Forbidden("权限不足，需要 instruction_cards:review 权限".to_string()));
    }
    let service = InstructionCardService::new(db_config);
    let result = service.manager_review_instruction_card(id, request, auth_user.user_id_string().to_string()).await?;
    Ok(Json(ApiResponse::success(result)))
}

/// 删除指令卡
pub async fn delete_instruction_card(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Path(id): Path<i64>,
) -> AppResult<StatusCode> {
    // 权限检查：需要 instruction_cards:delete 权限
    if !check_permission(&auth_user, "instruction_cards", "delete", &db_config, &settings).await {
        return Err(crate::utils::AppError::Forbidden("权限不足，需要 instruction_cards:delete 权限".to_string()));
    }
    let service = InstructionCardService::new(db_config);
    service.delete_instruction_card(id, auth_user.user_id_string().to_string()).await?;
    Ok(StatusCode::NO_CONTENT)
}

/// 班长修改班产数量
pub async fn update_leader_production_count(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Path(id): Path<i64>,
    Json(request): Json<UpdateProductionCountRequest>,
) -> AppResult<Json<ApiResponse<InstructionCardResponse>>> {
    // 权限检查：需要 instruction_cards:update 权限
    if !check_permission(&auth_user, "instruction_cards", "update", &db_config, &settings).await {
        return Err(crate::utils::AppError::Forbidden("权限不足，需要 instruction_cards:update 权限".to_string()));
    }
    let service = InstructionCardService::new(db_config);
    let result = service.update_leader_production_count(id, request, auth_user.user_id_string().to_string()).await?;
    Ok(Json(ApiResponse::success(result)))
}

/// 负责人修改班产数量
pub async fn update_manager_production_count(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Path(id): Path<i64>,
    Json(request): Json<UpdateProductionCountRequest>,
) -> AppResult<Json<ApiResponse<InstructionCardResponse>>> {
    // 权限检查：需要 instruction_cards:update 权限
    if !check_permission(&auth_user, "instruction_cards", "update", &db_config, &settings).await {
        return Err(crate::utils::AppError::Forbidden("权限不足，需要 instruction_cards:update 权限".to_string()));
    }
    let service = InstructionCardService::new(db_config);
    let result = service.update_manager_production_count(id, request, auth_user.user_id_string().to_string()).await?;
    Ok(Json(ApiResponse::success(result)))
}

/// 查询我的指令卡（员工查询分配给自己的，包括借用班组）
pub async fn get_my_instruction_cards(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Query(request): Query<InstructionCardQueryRequest>,
) -> AppResult<Json<ApiResponse<InstructionCardPageResponse>>> {
    // 权限检查：需要 instruction_cards:read 权限
    if !check_permission(&auth_user, "instruction_cards", "read", &db_config, &settings).await {
        return Err(crate::utils::AppError::Forbidden("权限不足，需要 instruction_cards:read 权限".to_string()));
    }
    let service = InstructionCardService::new(db_config);
    let result = service.query_my_instruction_cards(auth_user.user_id_string().to_string(), request).await?;
    Ok(Json(ApiResponse::success(result)))
}

/// 查询我提交的指令卡
pub async fn get_my_submitted_instruction_cards(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Query(mut request): Query<InstructionCardQueryRequest>,
) -> AppResult<Json<ApiResponse<InstructionCardPageResponse>>> {
    // 权限检查：需要 instruction_cards:read 权限
    if !check_permission(&auth_user, "instruction_cards", "read", &db_config, &settings).await {
        return Err(crate::utils::AppError::Forbidden("权限不足，需要 instruction_cards:read 权限".to_string()));
    }
    // 只查询已提交及以后的状态
    if request.status.is_none() {
        request.status = Some(1); // 默认查询已提交状态
    }

    let service = InstructionCardService::new(db_config);
    let result = service.query_my_instruction_cards(auth_user.user_id_string().to_string(), request).await?;
    Ok(Json(ApiResponse::success(result)))
}

/// 查询班组指令卡（班长查询）
pub async fn get_team_instruction_cards(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Path(team_id): Path<i32>,
    Query(mut request): Query<InstructionCardQueryRequest>,
) -> AppResult<Json<ApiResponse<InstructionCardPageResponse>>> {
    // 权限检查：需要 instruction_cards:read 权限
    if !check_permission(&auth_user, "instruction_cards", "read", &db_config, &settings).await {
        return Err(crate::utils::AppError::Forbidden("权限不足，需要 instruction_cards:read 权限".to_string()));
    }
    // 强制设置查询条件为指定班组
    request.team_id = Some(team_id);

    let service = InstructionCardService::new(db_config);
    let result = service.query_instruction_cards(request).await?;
    Ok(Json(ApiResponse::success(result)))
}

/// 查询工作中心指令卡（负责人查询）
pub async fn get_workcenter_instruction_cards(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Query(request): Query<InstructionCardQueryRequest>,
) -> AppResult<Json<ApiResponse<InstructionCardPageResponse>>> {
    // 权限检查：需要 instruction_cards:read 权限
    if !check_permission(&auth_user, "instruction_cards", "read", &db_config, &settings).await {
        return Err(crate::utils::AppError::Forbidden("权限不足，需要 instruction_cards:read 权限".to_string()));
    }

    let service = InstructionCardService::new(db_config);

    // 根据用户的工作中心权限来过滤数据
    let user_psn_num = auth_user.user_id_string();
    let result = service.query_workcenter_instruction_cards(user_psn_num, request).await?;
    Ok(Json(ApiResponse::success(result)))
}

/// 获取指令卡统计信息
pub async fn get_instruction_card_statistics(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Query(request): Query<InstructionCardQueryRequest>,
) -> AppResult<Json<ApiResponse<InstructionCardStatistics>>> {
    let service = InstructionCardService::new(db_config);
    let statistics = service.get_instruction_card_statistics(request).await?;
    Ok(Json(ApiResponse::success(statistics)))
}

/// 获取指令卡审核日志
pub async fn get_instruction_card_audit_logs(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Path(id): Path<i64>,
) -> AppResult<Json<ApiResponse<Vec<InstructionCardAuditLog>>>> {
    let service = InstructionCardService::new(db_config);
    let logs = service.get_instruction_card_audit_logs(id).await?;
    Ok(Json(ApiResponse::success(logs)))
}

/// 获取班组可选择的人员列表
pub async fn get_team_available_members(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Path(team_id): Path<i32>,
) -> AppResult<Json<ApiResponse<Vec<TeamMemberInfo>>>> {
    let service = InstructionCardService::new(db_config);
    let members = service.get_available_team_members(team_id).await?;
    Ok(Json(ApiResponse::success(members)))
}

/// 批量班长审核指令卡
pub async fn batch_leader_review_instruction_cards(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Json(request): Json<BatchReviewInstructionCardRequest>,
) -> AppResult<Json<ApiResponse<serde_json::Value>>> {
    // 权限检查：需要 instruction_cards:review 权限
    if !check_permission(&auth_user, "instruction_cards", "review", &db_config, &settings).await {
        return Err(crate::utils::AppError::Forbidden("权限不足，需要 instruction_cards:review 权限".to_string()));
    }

    let service = InstructionCardService::new(db_config);
    let (success_count, failed_count) = service.batch_leader_review_instruction_cards(&request, &auth_user.user_id_string()).await?;

    let result = serde_json::json!({
        "success_count": success_count,
        "failed_count": failed_count,
        "total_count": request.card_ids.len(),
        "message": format!("批量审核完成：成功 {} 个，失败 {} 个", success_count, failed_count)
    });

    Ok(Json(ApiResponse::success(result)))
}

/// 批量负责人审核指令卡
pub async fn batch_manager_review_instruction_cards(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Json(request): Json<BatchReviewInstructionCardRequest>,
) -> AppResult<Json<ApiResponse<serde_json::Value>>> {
    // 权限检查：需要 instruction_cards:review 权限
    if !check_permission(&auth_user, "instruction_cards", "review", &db_config, &settings).await {
        return Err(crate::utils::AppError::Forbidden("权限不足，需要 instruction_cards:review 权限".to_string()));
    }

    let service = InstructionCardService::new(db_config);
    let (success_count, failed_count) = service.batch_manager_review_instruction_cards(&request, &auth_user.user_id_string()).await?;

    let result = serde_json::json!({
        "success_count": success_count,
        "failed_count": failed_count,
        "total_count": request.card_ids.len(),
        "message": format!("批量审核完成：成功 {} 个，失败 {} 个", success_count, failed_count)
    });

    Ok(Json(ApiResponse::success(result)))
}

/// 获取用户主要角色的辅助函数
async fn get_user_primary_role(auth_user: &AuthenticatedUser, db_config: &DatabaseConfig) -> Option<String> {
    // 根据role_id查询角色名称
    if let Some(role_id) = auth_user.role_id() {
        match db_config.get_app_connection().await {
            Ok(mut client) => {
                let query_sql = "SELECT name FROM roles WHERE id = @P1";
                let mut query = tiberius::Query::new(query_sql);
                query.bind(role_id);

                match query.query(&mut client).await {
                    Ok(stream) => {
                        match stream.into_first_result().await {
                            Ok(rows) => {
                                if let Some(row) = rows.first() {
                                    if let Some(role_name) = row.get::<&str, _>(0) {
                                        return Some(role_name.to_string());
                                    }
                                }
                            }
                            Err(_) => return None,
                        }
                    }
                    Err(_) => return None,
                }
            }
            Err(_) => return None,
        }
    }
    None
}

/// 班长更新指令卡的leader_custom_count字段
pub async fn team_leader_update_count(
    State((db_config, _settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Path((card_id, count)): Path<(i64, i32)>,
) -> AppResult<Json<ApiResponse<String>>> {
    // 获取用户的人员编号
    let user_psn_num = auth_user.user_id_string();

    // 获取用户角色
    let user_role = get_user_primary_role(&auth_user, &db_config).await;

    // 创建服务实例
    let service = InstructionCardService::new(db_config);

    // 执行更新操作
    match service.update_leader_custom_count(card_id, count, user_psn_num, user_role.as_deref()).await {
        Ok(_) => {
            Ok(Json(ApiResponse::success("班长自定义数量更新成功".to_string())))
        }
        Err(e) => {
            tracing::error!("班长自定义数量更新失败: {}", e);
            Err(e)
        }
    }
}

/// 主管更新指令卡的manager_custom_count字段
pub async fn manager_update_count(
    State((db_config, _settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Path((card_id, count)): Path<(i64, i32)>,
) -> AppResult<Json<ApiResponse<String>>> {
    // 获取用户的人员编号
    let user_psn_num = auth_user.user_id_string();

    // 获取用户角色
    let user_role = get_user_primary_role(&auth_user, &db_config).await;

    // 创建服务实例
    let service = InstructionCardService::new(db_config);

    // 执行更新操作
    match service.update_manager_custom_count(card_id, count, user_psn_num, user_role.as_deref()).await {
        Ok(_) => {
            Ok(Json(ApiResponse::success("主管自定义数量更新成功".to_string())))
        }
        Err(e) => {
            tracing::error!("主管自定义数量更新失败: {}", e);
            Err(e)
        }
    }
}


