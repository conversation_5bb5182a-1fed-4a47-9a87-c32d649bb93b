use axum::{
    extract::{Path, Query, State},
    response::<PERSON><PERSON>,
};

use crate::{
    config::{DatabaseConfig, Settings},
    models::workcenter::{Workcenter, WorkcenterQueryRequest, WorkcenterPageResponse},
    services::WorkcenterService,
    utils::{ApiResponse, AppResult},
    extractors::auth::AuthenticatedUser,
};

type AppState = (DatabaseConfig, Settings);

/// 查询工作中心列表（分页）
pub async fn query_workcenters(
    State((db_config, _settings)): State<AppState>,
    _auth_user: AuthenticatedUser,
    Query(request): Query<WorkcenterQueryRequest>,
) -> AppResult<Json<ApiResponse<WorkcenterPageResponse>>> {
    // 暂时跳过权限检查，所有认证用户都可以查看工作中心
    let service = WorkcenterService::new(db_config);
    let result = service.query_workcenters(request).await?;
    Ok(Json(ApiResponse::success(result)))
}

/// 获取所有工作中心（不分页）
pub async fn get_all_workcenters(
    State((db_config, _settings)): State<AppState>,
    _auth_user: AuthenticatedUser,
) -> AppResult<Json<ApiResponse<Vec<Workcenter>>>> {
    // 暂时跳过权限检查，所有认证用户都可以查看工作中心
    let service = WorkcenterService::new(db_config);
    let result = service.get_all_workcenters().await?;
    Ok(Json(ApiResponse::success(result)))
}

/// 根据工作中心代码获取工作中心信息
pub async fn get_workcenter_by_code(
    State((db_config, _settings)): State<AppState>,
    _auth_user: AuthenticatedUser,
    Path(dept_code): Path<String>,
) -> AppResult<Json<ApiResponse<Option<Workcenter>>>> {
    // 暂时跳过权限检查，所有认证用户都可以查看工作中心
    let service = WorkcenterService::new(db_config);
    let result = service.get_workcenter_by_code(&dept_code).await?;
    Ok(Json(ApiResponse::success(result)))
}
