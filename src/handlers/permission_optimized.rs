// 优化后的权限处理器 - 使用新架构
// 基于 DDD 架构和依赖注入的权限管理

use axum::{
    extract::{Path, Query, State},
    response::{IntoResponse, Response},
    Json,
};
use serde::{Deserialize, Serialize};
use serde_json::json;

use crate::config::{DatabaseConfig, Settings};
use crate::extractors::AuthenticatedUser;
use crate::services::{CustomPermissionService, OptimizedPermissionService};
use crate::utils::ApiResponse;
use crate::models::custom_permission::{CreateCustomPermissionRequest};
use crate::models::permission::{CheckPermissionRequest};

// 权限检查辅助函数 - 支持配置开关
async fn check_permission(
    auth_user: &AuthenticatedUser,
    resource: &str,
    action: &str,
    db_config: &DatabaseConfig,
    settings: &Settings,
) -> bool {
    crate::utils::check_permission(auth_user, resource, action, db_config, settings).await
}

// ============================================================================
// 请求/响应模型
// ============================================================================



#[derive(Debug, Serialize)]
pub struct CheckPermissionResponse {
    pub allowed: bool,
    pub reason: Option<String>,
    #[serde(with = "crate::utils::local_timestamp")]    
    pub checked_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Deserialize)]
pub struct UpdateRolePermissionsRequest {
    pub permissions: Vec<String>,
    pub role_level: Option<u8>,
}

#[derive(Debug, Deserialize)]
pub struct ValidatePermissionsRequest {
    pub permissions: Vec<String>,
    pub role_level: u8,
}

#[derive(Debug, Serialize)]
pub struct ValidationResult {
    pub valid: bool,
    pub errors: Vec<String>,
    pub warnings: Vec<String>,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct CustomActionRequest {
    pub name: String,
    pub label: String,
    pub description: String,
}

#[derive(Debug, Deserialize)]
pub struct GetResourceActionsQuery {
    pub resource: String,
}

// ============================================================================
// 权限定义处理器
// ============================================================================

/// 获取权限定义 - 需要 permissions:read 权限
pub async fn get_permission_definitions_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<(DatabaseConfig, Settings)>,
) -> impl IntoResponse {
    // 权限检查：需要 permissions:read 权限
    if !check_permission(&auth_user, "permissions", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 permissions:read 权限").into_response();
    }

    // 创建服务
    let permission_service = OptimizedPermissionService::new(db_config.clone());
    let custom_service = CustomPermissionService::new(db_config);
    let mut resources = serde_json::Map::new();

    // 获取基础权限定义
    match permission_service.get_permission_definitions().await {
        Ok(definitions) => {
            for (resource_name, resource) in definitions.resources {
                let actions: Vec<String> = resource.actions.iter().map(|a| a.name.clone()).collect();
                let resource_data = serde_json::json!({
                    "label": resource.description,
                    "actions": actions,
                    "category": resource.category
                });
                resources.insert(resource_name, resource_data);
            }
        }
        Err(e) => {
            tracing::warn!("获取权限定义失败，使用空定义: {}", e);
        }
    }

    // 获取自定义权限并添加到资源列表
    match custom_service.get_all_active_custom_permissions().await {
        Ok(custom_permissions) => {
            tracing::info!("获取到 {} 个自定义权限", custom_permissions.len());
            for permission in custom_permissions {
                match permission.get_actions() {
                    Ok(actions) => {
                        let action_names: Vec<String> = actions.iter().map(|a| a.name.clone()).collect();
                        tracing::info!("添加自定义权限: {} -> {:?}", permission.resource_name, action_names);
                        resources.insert(permission.resource_name.clone(), json!({
                            "label": permission.resource_label,
                            "actions": action_names,
                            "category": permission.category,
                            "description": permission.description,
                            "icon": permission.icon,
                            "is_custom": true
                        }));
                    }
                    Err(e) => {
                        tracing::warn!("解析自定义权限 {} 的actions失败: {}, actions内容: {}",
                            permission.resource_name, e, permission.actions);
                    }
                }
            }
        }
        Err(e) => {
            tracing::warn!("获取自定义权限失败: {}", e);
        }
    }

    tracing::info!("最终resources Map包含 {} 个资源", resources.len());
    for (key, value) in &resources {
        tracing::info!("资源: {} -> {}", key, value);
    }

    let data = json!({
        "resources": resources,
        "version": "2.0.0",
        "last_updated": chrono::Utc::now()
    });

    ApiResponse::success_with_message(data, "获取权限定义成功").into_response()
}

/// 获取权限树结构 - 需要 permissions:read 权限
pub async fn get_permission_tree_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<(DatabaseConfig, Settings)>,
) -> impl IntoResponse {
    // 权限检查：需要 permissions:read 权限
    if !check_permission(&auth_user, "permissions", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 permissions:read 权限").into_response();
    }
    // 获取用户认证信息
    let user_claims = auth_user.claims();

    // 从数据库获取权限树结构
    let tree = match build_permission_tree_from_db(&db_config, user_claims.is_admin).await {
        Ok(tree_data) => tree_data,
        Err(e) => {
            tracing::error!("构建权限树失败: {}", e);
            // 返回基础权限树作为后备
            json!({
                "users": {
                    "label": "用户管理",
                    "children": {
                        "read": { "label": "查看用户", "level": 1 }
                    }
                }
            })
        }
    };

    ApiResponse::success_with_message(tree, "获取权限树成功").into_response()
}

/// 验证权限配置 - 需要 permissions:validate 权限
pub async fn validate_permissions_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<(DatabaseConfig, Settings)>,
    Json(request): Json<ValidatePermissionsRequest>,
) -> impl IntoResponse {
    // 权限检查：需要 permissions:validate 权限
    if !check_permission(&auth_user, "permissions", "validate", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 permissions:validate 权限").into_response();
    }
    // 简化的权限验证逻辑
    let mut errors = Vec::new();
    let warnings = Vec::new();

    // 验证权限格式
    for permission in &request.permissions {
        if !permission.contains(':') {
            errors.push(format!("权限格式错误: {}", permission));
        }
    }

    // 验证角色级别
    if request.role_level > 4 {
        errors.push("角色级别不能超过4".to_string());
    }

    let validation = ValidationResult {
        valid: errors.is_empty(),
        errors,
        warnings,
    };

    ApiResponse::success_with_message(validation, "权限验证完成").into_response()
}

/// 检查用户权限
pub async fn check_user_permission_handler(
    State((db_config, settings)): State<(DatabaseConfig, Settings)>,
    Json(request): Json<CheckPermissionRequest>,
) -> impl IntoResponse {
    // 检查权限校验开关
    if !settings.security.enable_permission_check {
        let result = CheckPermissionResponse {
            allowed: true,
            reason: Some("权限校验已禁用，允许所有操作".to_string()),
            checked_at: chrono::Utc::now(),
        };
        return ApiResponse::success_with_message(result, "权限检查完成").into_response();
    }

    // 创建权限服务
    let permission_service = OptimizedPermissionService::new(db_config.clone());
    let custom_service = CustomPermissionService::new(db_config);

    // 首先检查是否为管理员（通过数据库查询用户的is_admin字段）
    let is_admin = match permission_service.check_user_is_admin(request.user_id.as_str()).await {
        Ok(admin_status) => admin_status,
        Err(_) => false,
    };

    let (allowed, reason) = if is_admin {
        (true, Some("管理员拥有所有权限".to_string()))
    } else {
        // 检查标准权限
        match permission_service.check_user_permission_cached(request.user_id.as_str(), &request.resource, &request.action).await {
            Ok(standard_allowed) => {
                if standard_allowed {
                    (true, Some("标准权限验证通过".to_string()))
                } else {
                    // 检查自定义权限
                    match custom_service.check_custom_permission(&request.resource, &request.action).await {
                        Ok(custom_allowed) => {
                            (custom_allowed, Some(if custom_allowed {
                                "自定义权限验证通过".to_string()
                            } else {
                                "权限不足".to_string()
                            }))
                        }
                        Err(e) => {
                            (false, Some(format!("权限检查失败: {}", e)))
                        }
                    }
                }
            }
            Err(e) => {
                (false, Some(format!("权限检查失败: {}", e)))
            }
        }
    };

    let result = CheckPermissionResponse {
        allowed,
        reason,
        checked_at: chrono::Utc::now(),
    };

    ApiResponse::success_with_message(result, "权限检查完成").into_response()
}

/// 更新角色权限
pub async fn update_role_permissions_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<(DatabaseConfig, Settings)>,
    Path(role_id): Path<i64>,
    Json(update_request): Json<UpdateRolePermissionsRequest>,
) -> impl IntoResponse {
    // 权限检查：需要 roles:update 权限
    if !check_permission(&auth_user, "roles", "update", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 roles:update 权限").into_response();
    }

    // 禁止修改admin角色
    if role_id == 1 {
        return ApiResponse::forbidden("禁止修改admin角色的权限").into_response();
    }

    // 现在update_request已经通过Json提取器自动解析了

    // 执行角色权限更新
    match update_role_permissions_in_db(&db_config, role_id, &update_request).await {
        Ok(updated_count) => {
            let data = serde_json::json!({
                "role_id": role_id,
                "updated_permissions": updated_count,
                "role_level": update_request.role_level
            });
            ApiResponse::success_with_message(data, format!("角色 {} 权限更新成功", role_id)).into_response()
        }
        Err(e) => {
            ApiResponse::internal_error(format!("更新角色权限失败: {}", e)).into_response()
        }
    }
}

/// 获取角色权限概览
pub async fn get_role_permission_overview_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<(DatabaseConfig, Settings)>,
    Path(role_id): Path<i64>,
) -> impl IntoResponse {
    // 权限检查：需要 roles:read 权限
    if !check_permission(&auth_user, "roles", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 roles:read 权限").into_response();
    }
    match get_role_permissions_from_db(&db_config, role_id).await {
        Ok(permissions) => {
            let data = serde_json::json!({
                "role_id": role_id,
                "permissions": permissions,
                "total_count": permissions.len()
            });
            ApiResponse::success_with_message(data, "获取角色权限概览成功").into_response()
        }
        Err(e) => {
            ApiResponse::<()>::error(500, &format!("获取角色权限概览失败: {}", e)).into_response()
        }
    }
}

/// 从数据库获取角色权限
async fn get_role_permissions_from_db(db_config: &DatabaseConfig, role_id: i64) -> Result<Vec<serde_json::Value>, Box<dyn std::error::Error>> {
    let mut client = db_config.get_app_connection().await?;

    // 根据新的表结构查询权限定义
    let mut query = tiberius::Query::new(r#"
        SELECT
            pd.resource,
            pd.action,
            pd.description,
            pc.name as category_name
        FROM role_permissions rp
        INNER JOIN permission_definitions pd ON rp.permission_id = pd.id
        LEFT JOIN permission_categories pc ON pd.category_id = pc.id
        WHERE rp.role_id = @P1
        ORDER BY pc.name, pd.resource, pd.action
    "#);
    query.bind(role_id);

    let stream = query.query(&mut *client).await?;
    let rows: Vec<tiberius::Row> = stream.into_first_result().await?;

    let mut permissions = Vec::new();
    let mut current_resource = String::new();
    let mut current_actions = Vec::new();
    let mut current_resource_info: Option<serde_json::Value> = None;

    for row in rows {
        let resource_name: String = row.get::<&str, _>("resource").unwrap_or("").to_string();
        let resource_label: String = row.get::<&str, _>("resource").unwrap_or("").to_string(); // 使用resource作为label
        let category: String = row.get::<&str, _>("category_name").unwrap_or("系统管理").to_string();
        let action_name: String = row.get::<&str, _>("action").unwrap_or("").to_string();
        let action_label: String = row.get::<&str, _>("action").unwrap_or("").to_string(); // 使用action作为label
        let action_description: String = row.get::<&str, _>("description").unwrap_or("").to_string();

        if resource_name != current_resource {
            // 保存前一个资源的信息
            if let Some(info) = current_resource_info.take() {
                let mut resource_data = info;
                resource_data["actions"] = serde_json::Value::Array(current_actions.clone());
                permissions.push(resource_data);
                current_actions.clear();
            }

            // 开始新资源
            current_resource = resource_name.clone();
            current_resource_info = Some(json!({
                "resource_name": resource_name,
                "resource_label": resource_label,
                "category": category
            }));
        }

        // 添加操作
        current_actions.push(json!({
            "action_name": action_name,
            "action_label": action_label,
            "description": action_description
        }));
    }

    // 保存最后一个资源的信息
    if let Some(info) = current_resource_info {
        let mut resource_data = info;
        resource_data["actions"] = serde_json::Value::Array(current_actions);
        permissions.push(resource_data);
    }

    Ok(permissions)
}

/// 获取资源操作
pub async fn get_resource_actions_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<(DatabaseConfig, Settings)>,
    Query(query): Query<GetResourceActionsQuery>,
) -> impl IntoResponse {
    // 权限检查：需要 permissions:read 权限
    if !check_permission(&auth_user, "permissions", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 permissions:read 权限").into_response();
    }
    // 从数据库查询资源的可用操作
    match get_resource_actions_from_db(&db_config, &query.resource).await {
        Ok(actions) => {
            ApiResponse::success_with_message(actions, "获取资源操作成功").into_response()
        }
        Err(e) => {
            ApiResponse::internal_error(&format!("获取资源操作失败: {}", e)).into_response()
        }
    }
}

/// 在数据库中更新角色权限
async fn update_role_permissions_in_db(
    db_config: &DatabaseConfig,
    role_id: i64,
    request: &UpdateRolePermissionsRequest
) -> Result<usize, Box<dyn std::error::Error>> {
    let mut client = db_config.get_app_connection().await?;

    // 注意：tiberius 不支持显式事务，我们使用单独的操作

    // 1. 删除现有的角色权限
    let mut delete_query = tiberius::Query::new("DELETE FROM role_permissions WHERE role_id = @P1");
    delete_query.bind(role_id);
    delete_query.execute(&mut *client).await?;

    // 2. 插入新的权限
    let mut inserted_count = 0;
    for permission_str in &request.permissions {
        // 解析权限字符串 (格式: "resource:action")
        let parts: Vec<&str> = permission_str.split(':').collect();
        if parts.len() != 2 {
            continue; // 跳过格式错误的权限
        }

        let resource = parts[0];
        let action = parts[1];

        // 查找权限定义ID
        let mut find_permission_query = tiberius::Query::new(r#"
            SELECT pd.id
            FROM permission_definitions pd
            INNER JOIN permission_actions pa ON pd.resource_name = pa.resource_name
            WHERE pd.resource_name = @P1 AND pa.action_name = @P2
        "#);
        find_permission_query.bind(resource);
        find_permission_query.bind(action);

        let stream = find_permission_query.query(&mut *client).await?;
        let rows: Vec<tiberius::Row> = stream.into_first_result().await?;

        if let Some(row) = rows.first() {
            let permission_id: i64 = row.get("id").unwrap_or(0);

            // 插入角色权限关联
            let mut insert_query = tiberius::Query::new(
                "INSERT INTO role_permissions (role_id, permission_id) VALUES (@P1, @P2)"
            );
            insert_query.bind(role_id);
            insert_query.bind(permission_id);
            insert_query.execute(&mut *client).await?;

            inserted_count += 1;
        }
    }

    // 3. 更新角色级别（如果提供）
    if let Some(role_level) = request.role_level {
        let mut update_role_query = tiberius::Query::new(
            "UPDATE roles SET role_level = @P1, updated_at = GETDATE() WHERE id = @P2"
        );
        update_role_query.bind(role_level as i32);
        update_role_query.bind(role_id);
        update_role_query.execute(&mut *client).await?;
    }

    // 操作完成

    Ok(inserted_count)
}

/// 从数据库获取资源的可用操作
async fn get_resource_actions_from_db(db_config: &DatabaseConfig, resource: &str) -> Result<Vec<String>, Box<dyn std::error::Error>> {
    let mut client = db_config.get_app_connection().await?;

    let mut query = tiberius::Query::new(
        "SELECT DISTINCT action_name FROM permission_actions WHERE resource_name = @P1 ORDER BY sort_order"
    );
    query.bind(resource);

    let stream = query.query(&mut *client).await?;
    let rows: Vec<tiberius::Row> = stream.into_first_result().await?;

    let actions: Vec<String> = rows.iter()
        .filter_map(|row| row.get::<&str, _>("action_name").map(|s| s.to_string()))
        .collect();

    // 如果数据库中没有找到，返回空操作列表
    Ok(actions)
}

/// 从数据库构建权限树 - 查询真实的权限数据
async fn build_permission_tree_from_db(db_config: &DatabaseConfig, is_admin: bool) -> Result<serde_json::Value, Box<dyn std::error::Error>> {
    let mut client = db_config.get_app_connection().await?;
    let mut tree = serde_json::Map::new();

    // 根据新的表结构查询权限定义
    let permission_query = tiberius::Query::new(r#"
        SELECT
            resource,
            action,
            description
        FROM permission_definitions
        ORDER BY resource, action
    "#);

    match permission_query.query(&mut *client).await {
        Ok(permission_stream) => {
            match permission_stream.into_first_result().await {
                Ok(permission_rows) => {
                    // 成功查询到权限定义，构建真实的权限树
                    // 新的表结构：每行一个权限，需要按resource分组
                    let mut resource_actions: std::collections::HashMap<String, Vec<(String, String)>> = std::collections::HashMap::new();

                    for row in permission_rows {
                        if let (Some(resource_name), Some(action_name)) = (
                            row.get::<&str, _>("resource"),
                            row.get::<&str, _>("action")
                        ) {
                            let description = row.get::<&str, _>("description").unwrap_or("");
                            resource_actions.entry(resource_name.to_string())
                                .or_insert_with(Vec::new)
                                .push((action_name.to_string(), description.to_string()));
                        }
                    }

                    // 构建权限树
                    for (resource_name, actions) in resource_actions {
                        let mut children = serde_json::Map::new();
                        for (action_name, description) in actions {
                            let level = match action_name.as_str() {
                                "read" => 1,
                                "create" | "update" => 2,
                                "delete" => 3,
                                _ => 2,
                            };

                            // 如果不是管理员，过滤高级别权限
                            if !is_admin && level > 2 {
                                continue;
                            }

                            children.insert(action_name.clone(), json!({
                                "label": action_name,
                                "description": description,
                                "level": level
                            }));
                        }

                        if !children.is_empty() {
                            tree.insert(resource_name.clone(), json!({
                                "label": resource_name,
                                "children": children
                            }));
                        }
                    }
                }
                Err(e) => {
                    tracing::warn!("查询权限定义失败: {}, 使用基础权限树", e);
                    return Ok(get_fallback_permission_tree(is_admin));
                }
            }
        }
        Err(e) => {
            tracing::warn!("权限定义表不存在: {}, 使用基础权限树", e);
            return Ok(get_fallback_permission_tree(is_admin));
        }
    }

    // 如果没有查询到任何权限，使用基础权限树
    if tree.is_empty() {
        return Ok(get_fallback_permission_tree(is_admin));
    }

    Ok(serde_json::Value::Object(tree))
}

/// 获取基础权限树（当数据库表不存在时的后备方案）
fn get_fallback_permission_tree(is_admin: bool) -> serde_json::Value {
    let mut tree = serde_json::Map::new();

    // 基础权限树结构 - 只包含系统实际存在的功能
    tree.insert("users".to_string(), json!({
        "label": "用户管理",
        "children": {
            "read": { "label": "查看用户", "level": 1 }
        }
    }));

    if is_admin {
        // 管理员可以看到更多权限
        tree.insert("users".to_string(), json!({
            "label": "用户管理",
            "children": {
                "read": { "label": "查看用户", "level": 1 },
                "create": { "label": "创建用户", "level": 2 },
                "update": { "label": "更新用户", "level": 2 },
                "delete": { "label": "删除用户", "level": 3 }
            }
        }));

        tree.insert("roles".to_string(), json!({
            "label": "角色管理",
            "children": {
                "read": { "label": "查看角色", "level": 1 },
                "create": { "label": "创建角色", "level": 3 },
                "update": { "label": "更新角色", "level": 3 },
                "delete": { "label": "删除角色", "level": 4 }
            }
        }));

        tree.insert("auth".to_string(), json!({
            "label": "认证管理",
            "children": {
                "login": { "label": "登录", "level": 1 },
                "logout": { "label": "登出", "level": 1 },
                "verify": { "label": "验证", "level": 1 },
                "profile": { "label": "个人资料", "level": 1 }
            }
        }));
    }

    serde_json::Value::Object(tree)
}

// ============================================================================
// 自定义权限处理器（暂时保持简化实现）
// ============================================================================

/// 添加自定义权限资源
pub async fn add_custom_resource_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<(DatabaseConfig, Settings)>,
    Json(create_request): Json<CreateCustomPermissionRequest>,
) -> Response {
    // 权限检查：需要 permissions:create 权限
    if !check_permission(&auth_user, "permissions", "create", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 permissions:create 权限").into_response();
    }

    // 现在create_request已经通过Json提取器自动解析了

    // 创建自定义权限服务
    let custom_service = CustomPermissionService::new(db_config);

    // 将用户ID从字符串转换为i64
    let user_id = auth_user.claims().sub.parse::<i64>().unwrap_or(1);

    match custom_service.create_custom_permission(&create_request, user_id).await {
        Ok(permission) => {
            ApiResponse::success_with_message(permission, "创建自定义权限成功").into_response()
        }
        Err(e) => {
            ApiResponse::bad_request(format!("创建自定义权限失败: {}", e)).into_response()
        }
    }
}

/// 删除自定义权限资源
pub async fn delete_custom_resource_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<(DatabaseConfig, Settings)>,
    Path(resource_name): Path<String>,
) -> impl IntoResponse {
    // 权限检查：需要 permissions:delete 权限
    if !check_permission(&auth_user, "permissions", "delete", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 permissions:delete 权限").into_response();
    }
    // 创建自定义权限服务
    let custom_service = CustomPermissionService::new(db_config);

    match custom_service.delete_custom_permission(&resource_name).await {
        Ok(_) => {
            ApiResponse::<()>::success_with_message((), &format!("删除自定义权限 '{}' 成功", resource_name)).into_response()
        }
        Err(e) => {
            ApiResponse::<()>::error(400, &format!("删除自定义权限失败: {}", e)).into_response()
        }
    }
}

/// 按ID删除自定义权限资源
pub async fn delete_custom_resource_by_id_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<(DatabaseConfig, Settings)>,
    Path(id): Path<i64>,
) -> impl IntoResponse {
    // 权限检查：需要 permissions:delete 权限
    if !check_permission(&auth_user, "permissions", "delete", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 permissions:delete 权限").into_response();
    }
    // 创建自定义权限服务
    let custom_service = CustomPermissionService::new(db_config);

    match custom_service.delete_custom_permission_by_id(id).await {
        Ok(_) => {
            ApiResponse::<()>::success_with_message((), &format!("删除自定义权限 ID:{} 成功", id)).into_response()
        }
        Err(e) => {
            ApiResponse::<()>::error(400, &format!("删除自定义权限失败: {}", e)).into_response()
        }
    }
}

/// 列出自定义权限资源
pub async fn list_custom_resources_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<(DatabaseConfig, Settings)>,
) -> impl IntoResponse {
    // 权限检查：需要 permissions:read 权限
    if !check_permission(&auth_user, "permissions", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 permissions:read 权限").into_response();
    }
    // 创建自定义权限服务
    let custom_service = CustomPermissionService::new(db_config);

    match custom_service.get_all_active_custom_permissions().await {
        Ok(permissions) => {
            ApiResponse::success_with_message(permissions, "获取自定义权限列表成功").into_response()
        }
        Err(e) => {
            ApiResponse::internal_error(format!("获取自定义权限列表失败: {}", e)).into_response()
        }
    }
}
