use crate::config::DatabaseConfig;
use crate::models::operation::OperationQueryRequest;
use crate::services::operation_service::OperationService;
use crate::utils::ApiResponse;
use axum::{
    extract::{Path, Query, State},
    response::IntoResponse,
};
use std::sync::Arc;

type AppState = (DatabaseConfig, crate::config::Settings);

/// 获取工序列表
pub async fn list_operations_handler(
    State((db_config, _settings)): State<AppState>,
    Query(request): Query<OperationQueryRequest>,
) -> impl IntoResponse {
    let operation_service = OperationService::new(Arc::new(db_config));
    
    match operation_service.get_operations(request).await {
        Ok(response) => {
            ApiResponse::success_with_message(response, "获取工序列表成功").into_response()
        }
        Err(e) => {
            tracing::error!("获取工序列表失败: {}", e);
            ApiResponse::internal_error("获取工序列表失败").into_response()
        }
    }
}

/// 根据工序号获取单个工序
pub async fn get_operation_handler(
    State((db_config, _settings)): State<AppState>,
    Path(opcode): Path<String>,
) -> impl IntoResponse {
    let operation_service = OperationService::new(Arc::new(db_config));
    
    match operation_service.get_operation_by_code(&opcode).await {
        Ok(operation) => {
            ApiResponse::success_with_message(operation, "获取工序信息成功").into_response()
        }
        Err(e) => {
            tracing::error!("获取工序信息失败: {}", e);
            ApiResponse::internal_error("获取工序信息失败").into_response()
        }
    }
}
