use axum::{
    extract::{Path, Query, State},
    response::IntoResponse,
    Json,
};
use crate::{
    config::DatabaseConfig,
    extractors::AuthenticatedUser,
    models::team::{
        CreateTeamRequest, UpdateTeamRequest, AssignLeaderRequest, AddMemberRequest,
        BatchAssignLeadersRequest, BatchAddMembersRequest, TeamListQuery, TeamMemberQuery, UnifiedAssignLeaderRequest,
        AvailableTeamMemberQuery,
    },
    services::TeamService,
    utils::ApiResponse,
};

type AppState = (DatabaseConfig, crate::config::Settings);

// 权限检查辅助函数 - 支持配置开关
async fn check_permission(
    auth_user: &AuthenticatedUser,
    resource: &str,
    action: &str,
    db_config: &DatabaseConfig,
    settings: &crate::config::Settings,
) -> bool {
    crate::utils::check_permission(auth_user, resource, action, db_config, settings).await
}

/// 获取班组列表
pub async fn list_teams_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Query(query): Query<TeamListQuery>,
) -> impl IntoResponse {
    // 权限检查：需要 teams:read 权限
    if !check_permission(&auth_user, "teams", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 teams:read 权限").into_response();
    }

    let team_service = TeamService::new(db_config);

    match team_service.get_teams(&query).await {
        Ok(response) => {
            ApiResponse::success_with_message(response, "获取班组列表成功").into_response()
        }
        Err(e) => {
            tracing::error!("获取班组列表失败: {}", e);
            ApiResponse::error(500, "获取班组列表失败").into_response()
        }
    }
}

/// 获取班组详情
pub async fn get_team_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Path(team_id): Path<i32>,
) -> impl IntoResponse {
    // 权限检查：需要 teams:read 权限
    if !check_permission(&auth_user, "teams", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 teams:read 权限").into_response();
    }

    let team_service = TeamService::new(db_config);

    match team_service.get_team_by_id(team_id).await {
        Ok(Some(team)) => {
            ApiResponse::success_with_message(team, "获取班组详情成功").into_response()
        }
        Ok(None) => {
            ApiResponse::error(404, "班组不存在").into_response()
        }
        Err(e) => {
            tracing::error!("获取班组详情失败: {}", e);
            ApiResponse::error(500, "获取班组详情失败").into_response()
        }
    }
}

/// 创建班组
pub async fn create_team_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Json(create_request): Json<CreateTeamRequest>,
) -> impl IntoResponse {
    // 权限检查：需要 teams:create 权限
    if !check_permission(&auth_user, "teams", "create", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 teams:create 权限").into_response();
    }

    let team_service = TeamService::new(db_config);
    let created_by = auth_user.user_id_string();

    match team_service.create_team(&create_request, &created_by).await {
        Ok(team) => {
            ApiResponse::success_with_message(team, "创建班组成功").into_response()
        }
        Err(e) => {
            tracing::error!("创建班组失败: {}", e);
            ApiResponse::error(500, "创建班组失败").into_response()
        }
    }
}

/// 更新班组
pub async fn update_team_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Path(team_id): Path<i32>,
    Json(update_request): Json<UpdateTeamRequest>,
) -> impl IntoResponse {
    // 权限检查：需要 teams:update 权限
    if !check_permission(&auth_user, "teams", "update", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 teams:update 权限").into_response();
    }

    let team_service = TeamService::new(db_config);

    match team_service.update_team(team_id, &update_request).await {
        Ok(()) => {
            ApiResponse::success_with_message((), "更新班组成功").into_response()
        }
        Err(e) => {
            tracing::error!("更新班组失败: {}", e);
            if e.to_string().contains("不存在") {
                ApiResponse::error(404, "班组不存在").into_response()
            } else {
                ApiResponse::error(500, "更新班组失败").into_response()
            }
        }
    }
}

/// 删除班组
pub async fn delete_team_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Path(team_id): Path<i32>,
) -> impl IntoResponse {
    // 权限检查：需要 teams:delete 权限
    if !check_permission(&auth_user, "teams", "delete", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 teams:delete 权限").into_response();
    }

    let team_service = TeamService::new(db_config);

    match team_service.delete_team(team_id).await {
        Ok(()) => {
            ApiResponse::success_with_message((), "删除班组成功").into_response()
        }
        Err(e) => {
            tracing::error!("删除班组失败: {}", e);
            if e.to_string().contains("不存在") {
                ApiResponse::error(404, "班组不存在").into_response()
            } else {
                ApiResponse::error(500, "删除班组失败").into_response()
            }
        }
    }
}

/// 获取班组班长列表
pub async fn get_team_leaders_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Path(team_id): Path<i32>,
) -> impl IntoResponse {
    // 权限检查：需要 teams:read 权限
    if !check_permission(&auth_user, "teams", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 teams:read 权限").into_response();
    }

    let team_service = TeamService::new(db_config);

    match team_service.get_team_leaders(team_id).await {
        Ok(leaders) => {
            ApiResponse::success_with_message(leaders, "获取班组班长列表成功").into_response()
        }
        Err(e) => {
            tracing::error!("获取班组班长列表失败: {}", e);
            ApiResponse::error(500, "获取班组班长列表失败").into_response()
        }
    }
}

/// 任命班组长
pub async fn assign_team_leader_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Path(team_id): Path<i32>,
    Json(assign_request): Json<AssignLeaderRequest>,
) -> impl IntoResponse {
    // 权限检查：需要 teams:manage 权限
    if !check_permission(&auth_user, "teams", "manage", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 teams:manage 权限").into_response();
    }

    let team_service = TeamService::new(db_config);
    let assigned_by = auth_user.user_id_string();

    match team_service.assign_leader(team_id, &assign_request, &assigned_by).await {
        Ok(()) => {
            ApiResponse::success_with_message((), "任命班组长成功").into_response()
        }
        Err(e) => {
            tracing::error!("任命班组长失败: {}", e);
            ApiResponse::error(500, "任命班组长失败").into_response()
        }
    }
}

/// 移除班组长
pub async fn remove_team_leader_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Path((team_id, leader_psn_num)): Path<(i32, String)>,
) -> impl IntoResponse {
    // 权限检查：需要 teams:manage 权限
    if !check_permission(&auth_user, "teams", "manage", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 teams:manage 权限").into_response();
    }

    let team_service = TeamService::new(db_config);

    match team_service.remove_leader(team_id, &leader_psn_num).await {
        Ok(()) => {
            ApiResponse::success_with_message((), "移除班组长成功").into_response()
        }
        Err(e) => {
            tracing::error!("移除班组长失败: {}", e);
            if e.to_string().contains("不存在") {
                ApiResponse::error(404, "班组长不存在").into_response()
            } else {
                ApiResponse::error(500, "移除班组长失败").into_response()
            }
        }
    }
}

/// 获取班组成员列表
pub async fn get_team_members_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Path(team_id): Path<i32>,
    Query(query): Query<TeamMemberQuery>,
) -> impl IntoResponse {
    // 权限检查：需要 teams:read 权限
    if !check_permission(&auth_user, "teams", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 teams:read 权限").into_response();
    }

    let team_service = TeamService::new(db_config);

    match team_service.get_team_members_with_query(team_id, &query).await {
        Ok(members) => {
            ApiResponse::success_with_message(members, "获取班组成员列表成功").into_response()
        }
        Err(e) => {
            tracing::error!("获取班组成员列表失败: {}", e);
            ApiResponse::error(500, "获取班组成员列表失败").into_response()
        }
    }
}

/// 获取班级内且未被借出的可用成员列表
pub async fn get_available_team_members_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Query(query): Query<AvailableTeamMemberQuery>,
) -> impl IntoResponse {
    // 权限检查：需要 teams:read 权限
    if !check_permission(&auth_user, "teams", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 teams:read 权限").into_response();
    }

    let team_service = TeamService::new(db_config);

    match team_service.get_available_team_members(&query).await {
        Ok(response) => {
            ApiResponse::success_with_message(response, "获取可用班组成员列表成功").into_response()
        }
        Err(e) => {
            tracing::error!("获取可用班组成员列表失败: {}", e);
            ApiResponse::error(500, "获取可用班组成员列表失败").into_response()
        }
    }
}

/// 添加班组成员
pub async fn add_team_member_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Path(team_id): Path<i32>,
    Json(add_request): Json<AddMemberRequest>,
) -> impl IntoResponse {
    // 权限检查：需要 teams:manage 权限
    if !check_permission(&auth_user, "teams", "manage", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 teams:manage 权限").into_response();
    }

    let team_service = TeamService::new(db_config);
    let joined_by = auth_user.user_id_string();

    match team_service.add_member(team_id, &add_request, &joined_by).await {
        Ok(()) => {
            ApiResponse::success_with_message((), "添加班组成员成功").into_response()
        }
        Err(e) => {
            tracing::error!("添加班组成员失败: {}", e);
            match e {
                crate::utils::AppError::Validation(msg) => {
                    ApiResponse::error(400, &msg).into_response()
                }
                _ => {
                    ApiResponse::error(500, "添加班组成员失败").into_response()
                }
            }
        }
    }
}

/// 移除班组成员
pub async fn remove_team_member_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Path((team_id, member_psn_num)): Path<(i32, String)>,
) -> impl IntoResponse {
    // 权限检查：需要 teams:manage 权限
    if !check_permission(&auth_user, "teams", "manage", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 teams:manage 权限").into_response();
    }

    let team_service = TeamService::new(db_config);

    match team_service.remove_member(team_id, &member_psn_num).await {
        Ok(()) => {
            ApiResponse::success_with_message((), "移除班组成员成功").into_response()
        }
        Err(e) => {
            tracing::error!("移除班组成员失败: {}", e);
            if e.to_string().contains("不存在") {
                ApiResponse::error(404, "班组成员不存在").into_response()
            } else {
                ApiResponse::error(500, "移除班组成员失败").into_response()
            }
        }
    }
}

/// 批量添加班组成员
pub async fn batch_add_team_members_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Path(team_id): Path<i32>,
    Json(batch_request): Json<BatchAddMembersRequest>,
) -> impl IntoResponse {
    // 权限检查：需要 teams:manage 权限
    if !check_permission(&auth_user, "teams", "manage", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 teams:manage 权限").into_response();
    }

    let team_service = TeamService::new(db_config);
    let joined_by = auth_user.user_id_string();

    // 逐个添加成员
    let mut success_count = 0;
    let mut errors = Vec::new();

    for member_psn_num in &batch_request.member_psn_nums {
        let add_request = AddMemberRequest {
            member_psn_num: member_psn_num.clone(),
            remarks: batch_request.remarks.clone(),
        };

        match team_service.add_member(team_id, &add_request, &joined_by).await {
            Ok(()) => success_count += 1,
            Err(e) => {
                let error_msg = format!("添加成员 {} 失败: {}", member_psn_num, e);
                tracing::error!("批量添加班组成员错误: {}", error_msg);
                errors.push(error_msg);
            }
        }
    }

    if errors.is_empty() {
        ApiResponse::success_with_message(
            format!("成功添加 {} 个班组成员", success_count),
            "批量添加班组成员成功"
        ).into_response()
    } else {
        ApiResponse::error(
            500,
            &format!("部分操作失败，成功: {}, 失败: {}", success_count, errors.len())
        ).into_response()
    }
}

/// 批量任命班组长
pub async fn batch_assign_team_leaders_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Path(team_id): Path<i32>,
    Json(batch_request): Json<BatchAssignLeadersRequest>,
) -> impl IntoResponse {
    // 权限检查：需要 teams:manage 权限
    if !check_permission(&auth_user, "teams", "manage", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 teams:manage 权限").into_response();
    }

    let team_service = TeamService::new(db_config);
    let assigned_by = auth_user.user_id_string();

    // 逐个任命班组长
    let mut success_count = 0;
    let mut errors = Vec::new();

    for leader_psn_num in &batch_request.leader_psn_nums {
        let assign_request = AssignLeaderRequest {
            leader_psn_num: leader_psn_num.clone(),
            remarks: batch_request.remarks.clone(),
        };

        match team_service.assign_leader(team_id, &assign_request, &assigned_by).await {
            Ok(()) => success_count += 1,
            Err(e) => errors.push(format!("任命班组长 {} 失败: {}", leader_psn_num, e)),
        }
    }

    if errors.is_empty() {
        ApiResponse::success_with_message(
            format!("成功任命 {} 个班组长", success_count),
            "批量任命班组长成功"
        ).into_response()
    } else {
        ApiResponse::error(
            500,
            &format!("部分操作失败，成功: {}, 失败: {}", success_count, errors.len())
        ).into_response()
    }
}

/// 获取用户班组信息
pub async fn get_user_team_info_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Path(psn_num): Path<String>,
) -> impl IntoResponse {
    // 权限检查：需要 teams:read 权限
    if !check_permission(&auth_user, "teams", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 teams:read 权限").into_response();
    }

    let team_service = TeamService::new(db_config);

    match team_service.get_user_team_info(&psn_num).await {
        Ok(Some(user_team_info)) => {
            ApiResponse::success_with_message(user_team_info, "获取用户班组信息成功").into_response()
        }
        Ok(None) => {
            ApiResponse::error(404, "用户不存在").into_response()
        }
        Err(e) => {
            tracing::error!("获取用户班组信息失败: {}", e);
            ApiResponse::error(500, "获取用户班组信息失败").into_response()
        }
    }
}

/// 获取用户的角色和班组协同状态
pub async fn get_user_role_team_status_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Path(psn_num): Path<String>,
) -> impl IntoResponse {
    // 权限检查：需要 teams:read 权限
    if !check_permission(&auth_user, "teams", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 teams:read 权限").into_response();
    }

    let team_service = TeamService::new(db_config);

    match team_service.get_user_role_team_status(&psn_num).await {
        Ok(Some(status)) => {
            ApiResponse::success_with_message(status, "获取用户角色和班组协同状态成功").into_response()
        }
        Ok(None) => {
            ApiResponse::error(404, "用户不存在").into_response()
        }
        Err(e) => {
            tracing::error!("获取用户角色和班组协同状态失败: {}", e);
            ApiResponse::error(500, "获取用户角色和班组协同状态失败").into_response()
        }
    }
}

/// 根据工作中心获取班组列表
pub async fn get_teams_by_workcenter_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Path(dept_code): Path<String>,
) -> impl IntoResponse {
    // 权限检查：需要 teams:read 权限
    if !check_permission(&auth_user, "teams", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 teams:read 权限").into_response();
    }

    let team_service = TeamService::new(db_config);

    match team_service.get_teams_by_workcenter(&dept_code).await {
        Ok(teams) => {
            ApiResponse::success_with_message(teams, "获取工作中心班组列表成功").into_response()
        }
        Err(e) => {
            tracing::error!("获取工作中心班组列表失败: {}", e);
            ApiResponse::error(500, "获取工作中心班组列表失败").into_response()
        }
    }
}

/// 统一任命班组长（一步到位）
pub async fn unified_assign_leader_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Json(request): Json<UnifiedAssignLeaderRequest>,
) -> impl IntoResponse {
    // 权限检查：需要 teams:manage 权限
    if !check_permission(&auth_user, "teams", "manage", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 teams:manage 权限").into_response();
    }

    let team_service = TeamService::new(db_config);

    match team_service.unified_assign_leader(&request, &auth_user.0.username).await {
        Ok(()) => {
            ApiResponse::success_with_message(
                (),
                &format!("成功任命 {} 为班组长", request.leader_psn_num)
            ).into_response()
        }
        Err(e) => {
            tracing::error!("统一任命班组长失败: {}", e);
            match e {
                crate::utils::AppError::Validation(msg) => {
                    ApiResponse::error(400, &msg).into_response()
                }
                crate::utils::AppError::Business(msg) => {
                    ApiResponse::error(400, &msg).into_response()
                }
                _ => {
                    ApiResponse::error(500, "任命班组长失败").into_response()
                }
            }
        }
    }
}

/// 统一移除班组长（一步到位）
pub async fn unified_remove_leader_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Path(psn_num): Path<String>,
) -> impl IntoResponse {
    // 权限检查：需要 teams:manage 权限
    if !check_permission(&auth_user, "teams", "manage", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 teams:manage 权限").into_response();
    }

    let team_service = TeamService::new(db_config);

    match team_service.unified_remove_leader(&psn_num).await {
        Ok(()) => {
            ApiResponse::success_with_message(
                (),
                &format!("成功移除 {} 的所有班组长任命", psn_num)
            ).into_response()
        }
        Err(e) => {
            tracing::error!("统一移除班组长失败: {}", e);
            match e {
                crate::utils::AppError::Validation(msg) => {
                    ApiResponse::error(400, &msg).into_response()
                }
                crate::utils::AppError::Business(msg) => {
                    ApiResponse::error(400, &msg).into_response()
                }
                _ => {
                    ApiResponse::error(500, "移除班组长失败").into_response()
                }
            }
        }
    }
}
