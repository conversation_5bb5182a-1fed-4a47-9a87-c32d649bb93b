// 认证相关的处理器
use axum::{
    extract::State,
    response::IntoResponse,
    Json,
};
use crate::config::{DatabaseConfig, Settings};
use crate::models::{LoginRequest, UserPermissionsResponse, user::{PermissionItem, EnhancedLoginResponse, EnhancedUserResponse}};
use crate::services::{AuthService, RoleService};

use crate::utils::{verify_jwt, ApiResponse};
use chrono::Duration;
use serde::Serialize;


#[derive(Debug, Serialize)]
struct UserProfileData {
    id: String,
    username: String,
    is_admin: bool,
    role_id: Option<i64>,
    expires_at: i64,
}

#[derive(Debug, Serialize)]
struct TokenValidationResult {
    valid: bool,
    user_id: Option<String>,
    username: Option<String>,
    is_admin: Option<bool>,
    role_id: Option<i64>,
    expires_at: Option<i64>,
}

#[derive(Debug, Serialize)]
struct PermissionTreeNode {
    label: String,
    permissions: Vec<String>,
}

type AppState = (DatabaseConfig, Settings);

// 登录处理器 - 真实实现
pub async fn login_handler(
    State((db_config, settings)): State<AppState>,
    Json(request): Json<LoginRequest>,
) -> axum::response::Response {
    // 验证输入
    if request.username.is_empty() {
        return ApiResponse::bad_request("用户名不能为空").into_response();
    }

    if request.password.is_empty() {
        return ApiResponse::bad_request("密码不能为空").into_response();
    }

    // 创建认证服务
    let auth_service = AuthService::new(
        db_config.clone(),
        settings.jwt.secret.clone(),
        Duration::hours(24), // 24小时过期
        Duration::days(7),   // 刷新Token 7天过期
    );

    // 执行登录
    match auth_service.login(&request).await {
        Ok(response) => {
            tracing::info!("用户 {} 登录成功", request.username);

            // 获取用户ID
            let user_id = response.user.cpsn_num.clone();

            // 生成简化权限格式和权限树
            let (simplified_permissions, permission_tree) = match generate_user_permissions_and_tree(user_id, &db_config, &settings).await {
                Ok((perms, tree)) => (perms, tree),
                Err(e) => {
                    tracing::warn!("获取用户权限失败: {}", e);
                    (Vec::new(), Vec::new())
                }
            };

            // 创建增强的用户响应
            let enhanced_user = EnhancedUserResponse {
                cpsn_num: response.user.cpsn_num,
                name: response.user.name,
                username: Some(response.user.username),
                employ_state: response.user.employ_state,
                sex: response.user.sex,
                dept_num: response.user.dept_num,
                status: response.user.status,
                is_admin: response.user.is_admin,
                last_login_at: response.user.last_login_at,
                created_at: response.user.created_at,
                updated_at: response.user.updated_at,
                roles: response.user.roles,
                permissions: simplified_permissions,
                permission_tree: permission_tree,
            };

            let enhanced_response = EnhancedLoginResponse {
                token: response.token,
                user: enhanced_user,
            };

            ApiResponse::success_with_message(enhanced_response, "登录成功").into_response()
        }
        Err(e) => {
            tracing::warn!("用户 {} 登录失败: {}", request.username, e);
            ApiResponse::unauthorized("用户名或密码错误").into_response()
        }
    }
}

// 登出处理器（简化版）
pub async fn logout_handler() -> impl IntoResponse {
    ApiResponse::success_with_message((), "登出成功")
}

// 获取当前用户信息处理器 - 真实实现
pub async fn profile_handler(
    State((db_config, settings)): State<AppState>,
    headers: axum::http::HeaderMap,
) -> axum::response::Response {
    // 获取Authorization头
    let auth_header = headers
        .get("authorization")
        .and_then(|h| h.to_str().ok())
        .unwrap_or("");

    if auth_header.is_empty() {
        return ApiResponse::unauthorized("缺少认证信息").into_response();
    }

    // 提取Token
    let token = if auth_header.starts_with("Bearer ") {
        &auth_header[7..]
    } else {
        return ApiResponse::unauthorized("无效的认证格式").into_response();
    };

    // 创建认证服务
    let auth_service = AuthService::new(
        db_config,
        settings.jwt.secret,
        Duration::hours(24),
        Duration::days(7),
    );

    // 验证Token并获取用户信息
    match auth_service.verify_token(token).await {
        Ok(claims) => {
            let user_info = UserProfileData {
                id: claims.sub,
                username: claims.username,
                is_admin: claims.is_admin,
                role_id: claims.role_id,
                expires_at: claims.exp,
            };

            ApiResponse::success_with_message(user_info, "获取用户信息成功").into_response()
        }
        Err(e) => {
            tracing::warn!("Token验证失败: {}", e);
            ApiResponse::unauthorized("认证失败").into_response()
        }
    }
}

// 刷新Token处理器 - 真实实现
pub async fn refresh_token_handler(
    State((db_config, settings)): State<AppState>,
    Json(request): Json<serde_json::Value>,
) -> impl IntoResponse {
    let refresh_token = request
        .get("refresh_token")
        .and_then(|v| v.as_str())
        .unwrap_or("");

    if refresh_token.is_empty() {
        return ApiResponse::bad_request("刷新Token不能为空").into_response();
    }

    // 创建认证服务
    let auth_service = AuthService::new(
        db_config,
        settings.jwt.secret,
        Duration::hours(24),
        Duration::days(7),
    );

    // 刷新Token
    match auth_service.refresh_token(refresh_token).await {
        Ok(response) => {
            tracing::info!("Token刷新成功");
            ApiResponse::success_with_data(response).into_response()
        }
        Err(e) => {
            tracing::warn!("Token刷新失败: {}", e);
            ApiResponse::unauthorized("Token刷新失败").into_response()
        }
    }
}

// Token验证处理器 - 真实实现
pub async fn verify_token_handler(
    State((db_config, settings)): State<AppState>,
    headers: axum::http::HeaderMap,
) -> impl IntoResponse {
    let auth_header = headers
        .get("authorization")
        .and_then(|h| h.to_str().ok())
        .unwrap_or("");

    if auth_header.is_empty() {
        return ApiResponse::unauthorized("缺少认证信息").into_response();
    }

    let token = if auth_header.starts_with("Bearer ") {
        &auth_header[7..]
    } else {
        return ApiResponse::unauthorized("无效的认证格式").into_response();
    };

    let auth_service = AuthService::new(
        db_config,
        settings.jwt.secret,
        Duration::hours(24),
        Duration::days(7),
    );

    match auth_service.verify_token(token).await {
        Ok(claims) => {
            let result = TokenValidationResult {
                valid: true,
                user_id: Some(claims.sub),
                username: Some(claims.username),
                is_admin: Some(claims.is_admin),
                role_id: claims.role_id,
                expires_at: Some(claims.exp),
            };
            ApiResponse::success_with_data(result).into_response()
        }
        Err(_) => {
            let result = TokenValidationResult {
                valid: false,
                user_id: None,
                username: None,
                is_admin: None,
                role_id: None,
                expires_at: None,
            };
            ApiResponse::success_with_data(result).into_response()
        }
    }
}

// 获取当前用户权限处理器
pub async fn get_user_permissions_handler(
    State((db_config, settings)): State<AppState>,
    headers: axum::http::HeaderMap,
) -> impl IntoResponse {
    // 获取Authorization头
    let auth_header = headers
        .get("authorization")
        .and_then(|h| h.to_str().ok())
        .unwrap_or("");

    if auth_header.is_empty() {
        return ApiResponse::unauthorized("缺少认证信息").into_response();
    }

    // 提取Token
    let token = if auth_header.starts_with("Bearer ") {
        &auth_header[7..]
    } else {
        return ApiResponse::unauthorized("无效的认证格式").into_response();
    };

    // 验证Token
    match verify_jwt(token, &settings.jwt.secret) {
        Ok(claims) => {
            let user_id = claims.sub; // 现在user_id是String类型（cpsn_num）

            // 获取用户角色和权限
            let role_service = RoleService::new(db_config.clone());
            match role_service.get_user_roles(user_id.clone()).await {
                Ok(user_roles) => {
                    let role_responses: Vec<crate::models::role::RoleResponse> = user_roles.iter().map(|role| crate::models::role::RoleResponse::from(role.clone())).collect();

                    // 从角色的permissions字段解析真实权限
                    let mut all_permissions = Vec::new();
                    for role in &user_roles {
                        if let Some(permissions_json) = &role.permissions {
                            match serde_json::from_str::<serde_json::Value>(permissions_json) {
                                Ok(perms_value) => {
                                    if let Some(perms_array) = perms_value.get("permissions").and_then(|p| p.as_array()) {
                                        for perm in perms_array {
                                            if let Some(perm_obj) = perm.as_object() {
                                                if let (Some(resource), Some(actions)) = (
                                                    perm_obj.get("resource").and_then(|r| r.as_str()),
                                                    perm_obj.get("actions").and_then(|a| a.as_array())
                                                ) {
                                                    let action_strings: Vec<String> = actions.iter()
                                                        .filter_map(|a| a.as_str())
                                                        .map(|s| s.to_string())
                                                        .collect();

                                                    if !action_strings.is_empty() {
                                                        all_permissions.push(PermissionItem {
                                                            resource: resource.to_string(),
                                                            actions: action_strings,
                                                            source_role: role.name.clone(),
                                                        });
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                Err(e) => {
                                    tracing::warn!("解析角色 {} 的权限失败: {}", role.name, e);
                                }
                            }
                        }
                    }

                    let permissions_response = UserPermissionsResponse {
                        cpsn_num: user_id.clone(),
                        username: Some(claims.username),
                        roles: role_responses,
                        permissions: all_permissions,
                    };

                    ApiResponse::success_with_message(permissions_response, "获取用户权限成功").into_response()
                }
                Err(e) => {
                    tracing::error!("获取用户角色失败: {}", e);
                    ApiResponse::internal_error("获取用户权限失败").into_response()
                }
            }
        }
        Err(e) => {
            tracing::warn!("Token验证失败: {}", e);
            ApiResponse::unauthorized("认证失败").into_response()
        }
    }
}

/// 生成用户的简化权限格式和权限树
async fn generate_user_permissions_and_tree(
    user_id: String,
    db_config: &DatabaseConfig,
    _settings: &Settings
) -> crate::utils::AppResult<(Vec<String>, Vec<serde_json::Value>)> {
    let role_service = RoleService::new(db_config.clone());
    let user_roles = role_service.get_user_roles(user_id).await?;

    let mut simplified_permissions = Vec::new();
    let permission_tree;

    // 检查是否是admin用户
    let is_admin = user_roles.iter().any(|role| {
        let role_level = crate::utils::RoleLevel::from_level(role.level);
        matches!(role_level, crate::utils::RoleLevel::Admin)
    });

    // 从数据库获取用户的真实权限

    // 从角色的permissions字段解析权限
    for role in &user_roles {
        if let Some(permissions_json) = &role.permissions {
            match serde_json::from_str::<serde_json::Value>(permissions_json) {
                Ok(perms_value) => {
                    if let Some(perms_array) = perms_value.get("permissions").and_then(|p| p.as_array()) {
                        for perm in perms_array {
                            if let Some(perm_obj) = perm.as_object() {
                                if let (Some(resource), Some(actions)) = (
                                    perm_obj.get("resource").and_then(|r| r.as_str()),
                                    perm_obj.get("actions").and_then(|a| a.as_array())
                                ) {
                                    for action in actions {
                                        if let Some(action_str) = action.as_str() {
                                            if resource == "*" && action_str == "*" {
                                                simplified_permissions.push("*:*".to_string());
                                            } else {
                                                simplified_permissions.push(format!("{}:{}", resource, action_str));
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                Err(e) => {
                    tracing::warn!("解析角色 {} 的权限失败: {}", role.name, e);
                }
            }
        }
    }

    // 去重并排序
    simplified_permissions.sort();
    simplified_permissions.dedup();

    // 构建权限树
    if is_admin {
        let admin_node = PermissionTreeNode {
            label: "管理员权限".to_string(),
            permissions: simplified_permissions.clone(),
        };
        permission_tree = vec![serde_json::json!({
            "admin": admin_node
        })];
    } else {
        let user_node = PermissionTreeNode {
            label: "用户权限".to_string(),
            permissions: simplified_permissions.clone(),
        };
        permission_tree = vec![serde_json::json!({
            "user": user_node
        })];
    }

    Ok((simplified_permissions, permission_tree))
}