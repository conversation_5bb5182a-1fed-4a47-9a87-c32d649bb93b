use axum::{
    extract::{Path, Query, State},
    response::IntoResponse,
    Json,
};
use crate::config::{DatabaseConfig, Settings};
use crate::extractors::AuthenticatedUser;
use crate::models::{
    CreateFlexibleEntryRequest, ApproveFlexibleEntryRequest,
    BatchApproveFlexibleEntryRequest, FlexibleEntryQueryRequest
};
use crate::services::{FlexibleEntryService, TeamService};
use crate::utils::ApiResponse;

type AppState = (DatabaseConfig, Settings);

/// 创建零活录入（仅班长可操作）
pub async fn create_flexible_entry(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Json(request): Json<CreateFlexibleEntryRequest>,
) -> impl IntoResponse {
    // 权限检查：只有班长可以创建零活录入
    if !check_permission(&auth_user, "flexible_entry", "create", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，只有班长才能创建零活录入").into_response();
    }

    // 额外的角色检查：只有班长及以上角色才能创建零活录入
    let user_role = get_user_primary_role(&auth_user, &db_config).await;
    println!("用户角色: {:?}", user_role);
    if let Some(role) = &user_role {
        match role.as_str() {
            "admin" | "manager" | "team_leader" => {
                // 允许创建
            }
            _ => {
                return ApiResponse::forbidden("权限不足，只有班长及以上角色才能创建零活录入").into_response();
            }
        }
    } else {
        return ApiResponse::forbidden("无法确定用户角色").into_response();
    }

    // 获取用户的人员编号
    let creator_psn_num = auth_user.user_id_string();

    // 创建服务实例
    let service = FlexibleEntryService::new(db_config);

    // 执行创建操作
    match service.create_flexible_entry(&request, creator_psn_num).await {
        Ok(entry_id) => {
            ApiResponse::created_with_message(
                serde_json::json!({ "id": entry_id }),
                "零活录入创建成功"
            ).into_response()
        }
        Err(e) => {
            tracing::error!("创建零活录入失败: {}", e);
            ApiResponse::error(400, &format!("创建零活录入失败: {}", e)).into_response()
        }
    }
}

/// 审核零活录入（仅负责人可操作）
pub async fn approve_flexible_entry(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Path(entry_id): Path<i64>,
    Json(request): Json<ApproveFlexibleEntryRequest>,
) -> impl IntoResponse {
    // 权限检查：只有负责人可以审核零活录入
    if !check_permission(&auth_user, "flexible_entry", "approve", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，只有负责人才能审核零活录入").into_response();
    }

    // 获取用户的人员编号
    let approver_psn_num = auth_user.user_id_string();

    // 获取用户角色用于权限控制
    let approver_role = get_user_primary_role(&auth_user, &db_config).await;

    // 创建服务实例
    let service = FlexibleEntryService::new(db_config);

    // 执行审核操作
    match service.approve_flexible_entry(entry_id, &request, approver_psn_num, approver_role.as_deref()).await {
        Ok(_) => {
            ApiResponse::success_with_message(
                serde_json::json!({ "entry_id": entry_id }),
                "零活录入审核成功"
            ).into_response()
        }
        Err(e) => {
            tracing::error!("审核零活录入失败: {}", e);
            ApiResponse::error(400, &format!("审核零活录入失败: {}", e)).into_response()
        }
    }
}

/// 获取零活录入详情
pub async fn get_flexible_entry_by_id(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Path(entry_id): Path<i64>,
) -> impl IntoResponse {
    // 权限检查：需要读取权限
    if !check_permission(&auth_user, "flexible_entry", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，无法查看零活录入详情").into_response();
    }

    // 创建服务实例
    let service = FlexibleEntryService::new(db_config);

    // 获取详情
    match service.get_flexible_entry_by_id(entry_id).await {
        Ok(entry) => {
            ApiResponse::success_with_data(entry).into_response()
        }
        Err(e) => {
            tracing::error!("获取零活录入详情失败: {}", e);
            ApiResponse::error(400, &format!("获取零活录入详情失败: {}", e)).into_response()
        }
    }
}

/// 分页查询零活录入列表
pub async fn get_flexible_entries(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Query(request): Query<FlexibleEntryQueryRequest>,
) -> impl IntoResponse {
    // 权限检查：需要读取权限
    if !check_permission(&auth_user, "flexible_entry", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，无法查看零活录入列表").into_response();
    }

    // 获取用户信息用于权限控制
    let user_psn_num = auth_user.user_id_string();
    let user_role = get_user_primary_role(&auth_user, &db_config).await;

    // 创建服务实例
    let service = FlexibleEntryService::new(db_config);

    // 执行查询
    match service.get_flexible_entries(&request, Some(user_psn_num), user_role.as_deref()).await {
        Ok(page_response) => {
            ApiResponse::success_with_data(page_response).into_response()
        }
        Err(e) => {
            tracing::error!("查询零活录入列表失败: {}", e);
            ApiResponse::error(400, &format!("查询零活录入列表失败: {}", e)).into_response()
        }
    }
}

/// 批量审核零活录入（仅负责人可操作）
pub async fn batch_approve_flexible_entries(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Json(request): Json<BatchApproveFlexibleEntryRequest>,
) -> impl IntoResponse {
    // 权限检查：只有负责人可以批量审核零活录入
    if !check_permission(&auth_user, "flexible_entry", "approve", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，只有负责人才能批量审核零活录入").into_response();
    }

    // 获取用户的人员编号
    let approver_psn_num = auth_user.user_id_string();

    // 获取用户角色用于权限控制
    let approver_role = get_user_primary_role(&auth_user, &db_config).await;

    // 创建服务实例
    let service = FlexibleEntryService::new(db_config);

    // 执行批量审核操作
    match service.batch_approve_flexible_entries(&request, approver_psn_num, approver_role.as_deref()).await {
        Ok(result) => {
            ApiResponse::success_with_data(result).into_response()
        }
        Err(e) => {
            tracing::error!("批量审核零活录入失败: {}", e);
            ApiResponse::error(400, &format!("批量审核零活录入失败: {}", e)).into_response()
        }
    }
}

/// 获取用户的主要角色
async fn get_user_primary_role(auth_user: &AuthenticatedUser, db_config: &DatabaseConfig) -> Option<String> {
    // 从用户的角色列表中获取主要角色
    // 优先级：admin > manager > team_leader > user
    if auth_user.is_admin() {
        return Some("admin".to_string());
    }

    // 从数据库获取用户角色
    let user_id = match auth_user.user_id() {
        Ok(id) => id,
        Err(_) => return None,
    };

    // 查询用户角色
    if let Ok(mut client) = db_config.get_app_connection().await {
        let sql = r#"
            SELECT r.name
            FROM user_roles ur
            INNER JOIN roles r ON ur.role_id = r.id
            WHERE ur.user_id = @P1
            ORDER BY
                CASE r.name
                    WHEN 'admin' THEN 1
                    WHEN 'manager' THEN 2
                    WHEN 'team_leader' THEN 3
                    WHEN 'user' THEN 4
                    ELSE 5
                END
        "#;

        let user_id_str = user_id.to_string();
        println!("get_user_primary_role: {}", user_id_str);
        let mut query = tiberius::Query::new(sql);
        query.bind(user_id_str);

        if let Ok(stream) = query.query(&mut client).await {
            if let Ok(rows) = stream.into_first_result().await {
                if let Some(row) = rows.first() {
                    if let Some(role_name) = row.get::<&str, _>(0) {
                        return Some(role_name.to_string());
                    }
                }
            }
        }
    }

    None
}

// 权限检查辅助函数 - 支持配置开关
async fn check_permission(
    auth_user: &AuthenticatedUser,
    resource: &str,
    action: &str,
    db_config: &DatabaseConfig,
    settings: &crate::config::Settings,
) -> bool {
    crate::utils::check_permission(auth_user, resource, action, db_config, settings).await
}

/// 获取可指派的班组成员列表
pub async fn get_available_team_members(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
) -> impl IntoResponse {
    // 权限检查：需要读取权限
    if !check_permission(&auth_user, "flexible_entry", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，无法查看班组成员列表").into_response();
    }

    // 创建服务实例
    let service = TeamService::new(db_config);

    // 获取所有活跃的班组成员
    match service.get_all_active_team_members().await {
        Ok(members) => {
            ApiResponse::success_with_data(members).into_response()
        }
        Err(e) => {
            tracing::error!("获取班组成员列表失败: {}", e);
            ApiResponse::error(400, &format!("获取班组成员列表失败: {}", e)).into_response()
        }
    }
}

/// 班长更新零活数量
pub async fn team_leader_update_flexible_quantity(
    State((db_config, _settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Path((entry_id, quantity)): Path<(i64, i32)>,
) -> impl IntoResponse {
    // 获取用户角色
    let user_role = get_user_primary_role(&auth_user, &db_config).await;

    // 创建服务实例
    let service = FlexibleEntryService::new(db_config);

    // 执行更新操作
    match service.update_flexible_quantity(
        entry_id,
        quantity,
        auth_user.user_id_string(),
        user_role,
    ).await {
        Ok(()) => {
            ApiResponse::success_with_message((), "零活数量更新成功").into_response()
        }
        Err(e) => {
            tracing::error!("更新零活数量失败: {}", e);
            ApiResponse::error(400, &format!("更新零活数量失败: {}", e)).into_response()
        }
    }
}

/// 负责人更新审核数量
pub async fn manager_update_approved_quantity(
    State((db_config, _settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Path((entry_id, quantity)): Path<(i64, i32)>,
) -> impl IntoResponse {
    // 获取用户角色
    let user_role = get_user_primary_role(&auth_user, &db_config).await;

    // 创建服务实例
    let service = FlexibleEntryService::new(db_config);

    // 执行更新操作
    match service.update_approved_quantity(
        entry_id,
        quantity,
        auth_user.user_id_string(),
        user_role,
    ).await {
        Ok(()) => {
            ApiResponse::success_with_message((), "审核数量更新成功").into_response()
        }
        Err(e) => {
            tracing::error!("更新审核数量失败: {}", e);
            ApiResponse::error(400, &format!("更新审核数量失败: {}", e)).into_response()
        }
    }
}
