// 简化的角色处理器 - 权限检查由中间件处理
use axum::{
    extract::{State, Path},
    response::IntoResponse,
    Json,
};
use crate::config::{DatabaseConfig, Settings};
use crate::extractors::AuthenticatedUser;
use crate::models::{CreateRoleRequest, UpdateRoleRequest};
use crate::services::{RoleService, TeamService};
use crate::utils::{RoleLevel, ApiResponse};

type AppState = (DatabaseConfig, Settings);

// 权限检查辅助函数 - 支持配置开关
async fn check_permission(
    auth_user: &AuthenticatedUser,
    resource: &str,
    action: &str,
    db_config: &DatabaseConfig,
    settings: &Settings,
) -> bool {
    crate::utils::check_permission(auth_user, resource, action, db_config, settings).await
}

// 获取角色列表 - 需要 roles:read 权限
pub async fn list_roles_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
) -> impl IntoResponse {
    // 权限检查：需要 roles:read 权限
    if !check_permission(&auth_user, "roles", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 roles:read 权限").into_response();
    }

    let role_service = RoleService::new(db_config);

    match role_service.get_roles().await {
        Ok(roles) => {
            ApiResponse::success_with_message(roles, "获取角色列表成功").into_response()
        }
        Err(e) => {
            tracing::error!("获取角色列表失败: {}", e);
            ApiResponse::internal_error("获取角色列表失败").into_response()
        }
    }
}

// 获取单个角色 - 需要 roles:read 权限
pub async fn get_role_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Path(role_id): Path<i64>,
) -> impl IntoResponse {
    // 权限检查：需要 roles:read 权限
    if !check_permission(&auth_user, "roles", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 roles:read 权限").into_response();
    }

    let role_service = RoleService::new(db_config);

    match role_service.get_role_by_id(role_id).await {
        Ok(role) => {
            ApiResponse::success_with_message(role, "获取角色信息成功").into_response()
        }
        Err(e) => {
            tracing::error!("获取角色信息失败: {}", e);
            ApiResponse::error(404, "角色不存在").into_response()
        }
    }
}

// 创建新角色 - 需要 roles:create 权限
pub async fn create_role_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Json(create_request): Json<CreateRoleRequest>,
) -> impl IntoResponse {
    // 权限检查：需要 roles:create 权限
    if !check_permission(&auth_user, "roles", "create", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 roles:create 权限").into_response();
    }
    let role_service = RoleService::new(db_config);

    match role_service.create_role(&create_request).await {
        Ok(role) => {
            ApiResponse::created_with_message(role, "创建角色成功").into_response()
        }
        Err(e) => {
            tracing::error!("创建角色失败: {}", e);
            ApiResponse::bad_request(format!("创建角色失败: {}", e)).into_response()
        }
    }
}

// 更新角色信息 - 需要 roles:update 权限
pub async fn update_role_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Path(role_id): Path<i64>,
    Json(update_request): Json<UpdateRoleRequest>,
) -> impl IntoResponse {
    // 权限检查：需要 roles:update 权限
    if !check_permission(&auth_user, "roles", "update", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 roles:update 权限").into_response();
    }
    let role_service = RoleService::new(db_config);

    match role_service.update_role(role_id, &update_request).await {
        Ok(role) => {
            ApiResponse::success_with_message(role, "更新角色信息成功").into_response()
        }
        Err(e) => {
            tracing::error!("更新角色信息失败: {}", e);
            ApiResponse::bad_request(format!("更新角色信息失败: {}", e)).into_response()
        }
    }
}

// 删除角色 - 需要 roles:delete 权限
pub async fn delete_role_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Path(role_id): Path<i64>,
) -> impl IntoResponse {
    // 权限检查：需要 roles:delete 权限
    if !check_permission(&auth_user, "roles", "delete", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 roles:delete 权限").into_response();
    }
    let role_service = RoleService::new(db_config);

    match role_service.delete_role(role_id).await {
        Ok(_) => {
            ApiResponse::success_empty_with_message("删除角色成功").into_response()
        }
        Err(e) => {
            tracing::error!("删除角色失败: {}", e);
            ApiResponse::bad_request(format!("删除角色失败: {}", e)).into_response()
        }
    }
}

// 为用户分配角色 - 需要 roles:assign 权限
pub async fn assign_role_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Path((user_id, role_id)): Path<(String, i64)>,
) -> impl IntoResponse {
    // 创建默认的分配请求（保持原有接口兼容性）
    let request = crate::models::AssignRoleRequest {
        user_id: user_id.clone(),
        role_id,
        overwrite: true,  // 默认覆盖，实现一对一关系
        auto_assign_team: None,
        target_team_id: None,
    };

    assign_role_internal(auth_user, State((db_config, settings)), request).await
}

/// 为用户分配角色（增强版本，支持智能班组分配）
pub async fn assign_role_with_options_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Json(request): Json<crate::models::AssignRoleRequest>,
) -> impl IntoResponse {
    assign_role_internal(auth_user, State((db_config, settings)), request).await
}

/// 内部函数：处理角色分配逻辑
async fn assign_role_internal(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    request: crate::models::AssignRoleRequest,
) -> impl IntoResponse {
    // 权限检查：需要 roles:assign 权限
    if !check_permission(&auth_user, "roles", "assign", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 roles:assign 权限").into_response();
    }
    let role_service = RoleService::new(db_config.clone());

    // 获取要分配的角色信息，检查权限级别
    let target_role = match role_service.get_role_by_id(request.role_id).await {
        Ok(role) => role,
        Err(e) => {
            return ApiResponse::bad_request(format!("获取目标角色失败: {}", e)).into_response();
        }
    };

    // 从角色名称推断角色级别
    let target_role_level = RoleLevel::from_role_name(&target_role.name);

    // 获取当前用户的角色级别进行权限检查
    let current_user_level = if auth_user.is_admin() {
        RoleLevel::Admin
    } else if let Some(role_id) = auth_user.role_id() {
        // 根据角色ID获取角色级别
        match role_service.get_role_by_id(role_id).await {
            Ok(role) => RoleLevel::from_role_name(&role.name),
            Err(_) => RoleLevel::User, // 默认为最低权限
        }
    } else {
        RoleLevel::User
    };

    // 检查是否有权限分配此角色
    if !current_user_level.can_assign_role(&target_role_level) {
        return ApiResponse::forbidden(format!("权限不足，无法分配 {} 角色", target_role_level)).into_response();
    }

    // 检查是否是 team_leader 角色，给出特殊提示
    let is_team_leader_role = target_role.name == "team_leader";

    // 使用请求中的参数创建分配角色请求
    let assign_request = crate::models::AssignRoleRequest {
        user_id: request.user_id.clone(),
        role_id: request.role_id,
        overwrite: request.overwrite,
        auto_assign_team: request.auto_assign_team,
        target_team_id: request.target_team_id,
    };

    match role_service.assign_role(&assign_request).await {
        Ok(_) => {
            if is_team_leader_role {
                // 对于 team_leader 角色，进行智能班组分配处理
                return handle_team_leader_assignment(&db_config, &request, &auth_user.0.username).await;
            } else {
                ApiResponse::success_empty_with_message(format!("成功分配 {} 角色", target_role_level)).into_response()
            }
        }
        Err(e) => {
            tracing::error!("分配角色失败: {}", e);
            ApiResponse::bad_request(format!("分配角色失败: {}", e)).into_response()
        }
    }
}

// 移除用户角色 - 需要 roles:remove 权限
pub async fn remove_role_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Path((user_id, role_id)): Path<(String, i64)>,
) -> impl IntoResponse {
    // 权限检查：需要 roles:remove 权限
    if !check_permission(&auth_user, "roles", "remove", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 roles:remove 权限").into_response();
    }
    let role_service = RoleService::new(db_config.clone());

    // 获取要移除的角色信息，检查权限级别
    let target_role = match role_service.get_role_by_id(role_id).await {
        Ok(role) => role,
        Err(e) => {
            return ApiResponse::bad_request(format!("获取目标角色失败: {}", e)).into_response();
        }
    };

    // 从角色名称推断角色级别
    let target_role_level = RoleLevel::from_role_name(&target_role.name);

    // 获取当前用户的角色级别进行权限检查
    let current_user_level = if auth_user.is_admin() {
        RoleLevel::Admin
    } else if let Some(role_id) = auth_user.role_id() {
        // 根据角色ID获取角色级别
        match role_service.get_role_by_id(role_id).await {
            Ok(role) => RoleLevel::from_role_name(&role.name),
            Err(_) => RoleLevel::User, // 默认为最低权限
        }
    } else {
        RoleLevel::User
    };

    // 检查是否有权限移除此角色
    if !current_user_level.can_remove_role(&target_role_level) {
        return ApiResponse::forbidden(format!("权限不足，无法移除 {} 角色", target_role_level)).into_response();
    }

    // 检查是否是 team_leader 角色
    let is_team_leader_role = target_role.name == "team_leader";

    match role_service.remove_user_role(user_id, role_id).await {
        Ok(_) => {
            if is_team_leader_role {
                // 对于 team_leader 角色，给出特殊提示
                ApiResponse::success_with_message(
                    serde_json::json!({
                        "message": "team_leader 角色移除成功",
                        "warning": "仅移除了角色权限，如果用户还担任班组长职务，建议使用统一移除接口",
                        "suggestion": "使用统一移除接口可以同时移除角色和班组任命",
                        "unified_api": "DELETE /api/teams/remove-leader/{psn_num}",
                        "unified_api_description": "该接口可以一步完成角色移除和班组任命移除"
                    }),
                    "team_leader 角色移除成功，建议检查班组任命状态"
                ).into_response()
            } else {
                ApiResponse::success_empty_with_message(format!("成功移除 {} 角色", target_role_level)).into_response()
            }
        }
        Err(e) => {
            tracing::error!("移除角色失败: {}", e);
            ApiResponse::bad_request(format!("移除角色失败: {}", e)).into_response()
        }
    }
}

// 查询参数结构体
#[derive(serde::Deserialize)]
pub struct RoleUsersQuery {
    pub page: Option<i32>,
    pub page_size: Option<i32>,
}

// 根据角色ID获取用户列表
pub async fn get_users_by_role_handler(
    State((db_config, settings)): State<AppState>,
    Path(role_id): Path<i64>,
    axum::extract::Query(query): axum::extract::Query<RoleUsersQuery>,
    auth_user: AuthenticatedUser,
) -> impl IntoResponse {
    // 权限检查：需要有读取用户的权限
    if !check_permission(&auth_user, "users", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，无法查看用户列表").into_response();
    }

    let role_service = RoleService::new(db_config);

    match role_service.get_users_by_role(role_id, query.page, query.page_size).await {
        Ok(users) => {
            tracing::info!("成功获取角色 {} 的用户列表，共 {} 个用户", role_id, users.total);
            ApiResponse::success_with_data(users).into_response()
        }
        Err(e) => {
            tracing::error!("获取角色用户列表失败: {}", e);
            ApiResponse::internal_error("获取角色用户列表失败").into_response()
        }
    }
}

/// 处理 team_leader 角色分配的智能班组分配逻辑
async fn handle_team_leader_assignment(
    db_config: &DatabaseConfig,
    request: &crate::models::AssignRoleRequest,
    assigned_by: &str,
) -> axum::response::Response {
    let team_service = TeamService::new(db_config.clone());

    // 1. 获取用户的工作中心信息
    let user_workcenter = match get_user_workcenter(db_config, &request.user_id).await {
        Ok(Some(dept_code)) => dept_code,
        Ok(None) => {
            return ApiResponse::success_with_message(
                serde_json::json!({
                    "message": "team_leader 角色分配成功",
                    "warning": "无法获取用户工作中心信息，无法提供班组建议",
                    "suggestion": "请手动使用统一任命接口完成班组任命",
                    "unified_api": "POST /api/teams/assign-leader"
                }),
                "team_leader 角色分配成功，请手动完成班组任命"
            ).into_response();
        }
        Err(e) => {
            tracing::error!("获取用户工作中心信息失败: {}", e);
            return ApiResponse::success_with_message(
                serde_json::json!({
                    "message": "team_leader 角色分配成功",
                    "warning": "获取用户工作中心信息失败，无法提供班组建议",
                    "suggestion": "请手动使用统一任命接口完成班组任命",
                    "unified_api": "POST /api/teams/assign-leader"
                }),
                "team_leader 角色分配成功，请手动完成班组任命"
            ).into_response();
        }
    };

    // 2. 获取该工作中心下的班组列表
    let available_teams = match team_service.get_teams_by_workcenter(&user_workcenter).await {
        Ok(teams) => teams,
        Err(e) => {
            tracing::error!("获取工作中心班组列表失败: {}", e);
            return ApiResponse::success_with_message(
                serde_json::json!({
                    "message": "team_leader 角色分配成功",
                    "warning": "获取工作中心班组列表失败，无法提供班组建议",
                    "suggestion": "请手动使用统一任命接口完成班组任命",
                    "unified_api": "POST /api/teams/assign-leader"
                }),
                "team_leader 角色分配成功，请手动完成班组任命"
            ).into_response();
        }
    };

    // 3. 处理不同的分配策略
    if let Some(target_team_id) = request.target_team_id {
        // 用户指定了具体的班组ID，尝试自动任命
        let target_team = available_teams.iter().find(|t| t.team_id == target_team_id);

        if let Some(team) = target_team {
            // 尝试自动任命到指定班组
            let assign_request = crate::models::team::UnifiedAssignLeaderRequest {
                team_id: target_team_id,
                leader_psn_num: request.user_id.clone(),
                workcenter_dept_code: user_workcenter.clone(),
                remarks: Some("通过角色分配接口自动任命".to_string()),
            };

            match team_service.unified_assign_leader(&assign_request, assigned_by).await {
                Ok(()) => {
                    return ApiResponse::success_with_message(
                        serde_json::json!({
                            "message": "team_leader 角色分配成功，并已自动任命到指定班组",
                            "team_info": {
                                "team_id": team.team_id,
                                "team_name": &team.team_name,
                                "workcenter_name": &team.workcenter_name
                            },
                            "auto_assigned": true
                        }),
                        &format!("成功分配 team_leader 角色并任命到班组：{}", team.team_name)
                    ).into_response();
                }
                Err(e) => {
                    let error_msg = e.to_string();

                    // 如果用户已经是班组长，返回成功信息
                    if error_msg.contains("该人员已经是此班组的班长") {
                        tracing::info!("用户 {} 已经是班组 {} 的班长，角色分配成功", request.user_id, target_team_id);
                        return ApiResponse::success_with_message(
                            serde_json::json!({
                                "message": "team_leader 角色分配成功",
                                "info": "用户已经是该班组的班长",
                                "available_teams": available_teams
                            }),
                            "team_leader 角色分配成功，用户已经是该班组的班长"
                        ).into_response();
                    }

                    // 其他错误情况
                    tracing::error!("自动任命到指定班组失败: {}", e);
                    return ApiResponse::success_with_message(
                        serde_json::json!({
                            "message": "team_leader 角色分配成功，但自动任命到指定班组失败",
                            "error": format!("任命失败: {}", e),
                            "suggestion": "请手动使用统一任命接口完成班组任命",
                            "available_teams": available_teams,
                            "unified_api": "POST /api/teams/assign-leader"
                        }),
                        "team_leader 角色分配成功，请手动完成班组任命"
                    ).into_response();
                }
            }
        } else {
            return ApiResponse::success_with_message(
                serde_json::json!({
                    "message": "team_leader 角色分配成功，但指定的班组不存在或不属于用户工作中心",
                    "available_teams": available_teams,
                    "suggestion": "请从可用班组中选择并使用统一任命接口",
                    "unified_api": "POST /api/teams/assign-leader"
                }),
                "team_leader 角色分配成功，请选择正确的班组完成任命"
            ).into_response();
        }
    } else if request.auto_assign_team.unwrap_or(false) && available_teams.len() == 1 {
        // 启用自动分配且只有一个班组，自动任命
        let team = &available_teams[0];
        let assign_request = crate::models::team::UnifiedAssignLeaderRequest {
            team_id: team.team_id,
            leader_psn_num: request.user_id.clone(),
            workcenter_dept_code: user_workcenter.clone(),
            remarks: Some("通过角色分配接口自动任命".to_string()),
        };

        match team_service.unified_assign_leader(&assign_request, assigned_by).await {
            Ok(()) => {
                return ApiResponse::success_with_message(
                    serde_json::json!({
                        "message": "team_leader 角色分配成功，并已自动任命到唯一可用班组",
                        "team_info": {
                            "team_id": team.team_id,
                            "team_name": &team.team_name,
                            "workcenter_name": &team.workcenter_name
                        },
                        "auto_assigned": true
                    }),
                    &format!("成功分配 team_leader 角色并自动任命到班组：{}", team.team_name)
                ).into_response();
            }
            Err(e) => {
                tracing::error!("自动任命到唯一班组失败: {}", e);
                return ApiResponse::success_with_message(
                    serde_json::json!({
                        "message": "team_leader 角色分配成功，但自动任命失败",
                        "error": format!("任命失败: {}", e),
                        "available_teams": available_teams,
                        "suggestion": "请手动使用统一任命接口完成班组任命",
                        "unified_api": "POST /api/teams/assign-leader"
                    }),
                    "team_leader 角色分配成功，请手动完成班组任命"
                ).into_response();
            }
        }
    } else {
        // 默认情况：提供班组建议
        let response_data = if available_teams.is_empty() {
            serde_json::json!({
                "message": "team_leader 角色分配成功",
                "warning": "用户工作中心下暂无可用班组",
                "workcenter_info": {
                    "dept_code": user_workcenter,
                    "available_teams_count": 0
                },
                "suggestion": "请联系管理员创建班组或使用统一任命接口指定其他班组",
                "unified_api": "POST /api/teams/assign-leader"
            })
        } else {
            serde_json::json!({
                "message": "team_leader 角色分配成功",
                "workcenter_info": {
                    "dept_code": user_workcenter,
                    "available_teams_count": available_teams.len()
                },
                "available_teams": available_teams,
                "suggestion": "请从可用班组中选择并使用统一任命接口完成班组任命",
                "unified_api": "POST /api/teams/assign-leader",
                "auto_assign_tip": "下次可以在请求中设置 auto_assign_team: true 来自动分配（仅当只有一个班组时）"
            })
        };

        ApiResponse::success_with_message(
            response_data,
            "team_leader 角色分配成功，请选择班组完成任命"
        ).into_response()
    }
}

/// 获取用户的工作中心信息
async fn get_user_workcenter(db_config: &DatabaseConfig, user_id: &str) -> Result<Option<String>, Box<dyn std::error::Error + Send + Sync>> {
    use tiberius::Query;

    let mut client = db_config.get_app_connection().await?;

    let sql = "SELECT cDept_num FROM person WHERE cpsn_num = @P1";
    let mut query = Query::new(sql);
    query.bind(user_id);
    let result = query.query(&mut client).await?;

    if let Some(row) = result.into_row().await? {
        let dept_code = row.get::<&str, _>(0).map(|s| s.to_string());
        Ok(dept_code)
    } else {
        Ok(None)
    }
}
