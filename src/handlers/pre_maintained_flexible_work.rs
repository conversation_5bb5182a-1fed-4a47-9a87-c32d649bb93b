use axum::{
    extract::{Path, Query, State},
    response::IntoResponse,
    Json,
};
use crate::config::{DatabaseConfig, Settings};
use crate::extractors::AuthenticatedUser;

type AppState = (DatabaseConfig, Settings);
use crate::models::{
    CreatePreMaintainedFlexibleWorkRequest, UpdatePreMaintainedFlexibleWorkRequest,
    PreMaintainedFlexibleWorkQueryRequest
};
use crate::services::PreMaintainedFlexibleWorkService;
use crate::utils::{ApiResponse, check_permission};

/// 查询预维护零活列表
pub async fn query_pre_maintained_flexible_works_handler(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Query(query): Query<PreMaintainedFlexibleWorkQueryRequest>,
) -> impl IntoResponse {
    // 权限检查：需要 flexible_entry:read 权限
    if !check_permission(&auth_user, "flexible_entry", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 flexible_entry:read 权限").into_response();
    }

    let service = PreMaintainedFlexibleWorkService::new(db_config);
    
    match service.query_pre_maintained_flexible_works(&query, &auth_user.user_id_string()).await {
        Ok(result) => ApiResponse::success_with_data(result).into_response(),
        Err(e) => {
            tracing::error!("查询预维护零活列表失败: {}", e);
            ApiResponse::internal_error("查询预维护零活列表失败").into_response()
        }
    }
}

/// 创建预维护零活
pub async fn create_pre_maintained_flexible_work_handler(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Json(request): Json<CreatePreMaintainedFlexibleWorkRequest>,
) -> impl IntoResponse {
    // 权限检查：需要 flexible_entry:create 权限
    if !check_permission(&auth_user, "flexible_entry", "create", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 flexible_entry:create 权限").into_response();
    }

    let service = PreMaintainedFlexibleWorkService::new(db_config);
    
    match service.create_pre_maintained_flexible_work(&request, &auth_user.user_id_string()).await {
        Ok(result) => {
            tracing::info!("用户 {} 创建预维护零活成功: {}", auth_user.user_id_string(), result.opcode);
            ApiResponse::success_with_data(result).into_response()
        }
        Err(e) => {
            tracing::error!("创建预维护零活失败: {}", e);
            ApiResponse::bad_request(format!("创建预维护零活失败: {}", e)).into_response()
        }
    }
}

/// 更新预维护零活
pub async fn update_pre_maintained_flexible_work_handler(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Path(opcode): Path<String>,
    Json(request): Json<UpdatePreMaintainedFlexibleWorkRequest>,
) -> impl IntoResponse {
    // 权限检查：需要 flexible_entry:update 权限
    if !check_permission(&auth_user, "flexible_entry", "update", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 flexible_entry:update 权限").into_response();
    }

    let service = PreMaintainedFlexibleWorkService::new(db_config);
    
    match service.update_pre_maintained_flexible_work(&opcode, &request, &auth_user.user_id_string()).await {
        Ok(result) => {
            tracing::info!("用户 {} 更新预维护零活成功: {}", auth_user.user_id_string(), result.opcode);
            ApiResponse::success_with_data(result).into_response()
        }
        Err(e) => {
            tracing::error!("更新预维护零活失败: {}", e);
            ApiResponse::bad_request(format!("更新预维护零活失败: {}", e)).into_response()
        }
    }
}

/// 删除预维护零活
pub async fn delete_pre_maintained_flexible_work_handler(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Path(opcode): Path<String>,
) -> impl IntoResponse {
    // 权限检查：需要 flexible_entry:delete 权限
    if !check_permission(&auth_user, "flexible_entry", "delete", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 flexible_entry:delete 权限").into_response();
    }

    let service = PreMaintainedFlexibleWorkService::new(db_config);
    
    match service.delete_pre_maintained_flexible_work(&opcode, &auth_user.user_id_string()).await {
        Ok(_) => {
            tracing::info!("用户 {} 删除预维护零活成功: {}", auth_user.user_id_string(), opcode);
            ApiResponse::success_empty_with_message("删除预维护零活成功").into_response()
        }
        Err(e) => {
            tracing::error!("删除预维护零活失败: {}", e);
            ApiResponse::bad_request(format!("删除预维护零活失败: {}", e)).into_response()
        }
    }
}

/// 获取预维护零活详情
pub async fn get_pre_maintained_flexible_work_handler(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Path(opcode): Path<String>,
) -> impl IntoResponse {
    // 权限检查：需要 flexible_entry:read 权限
    if !check_permission(&auth_user, "flexible_entry", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 flexible_entry:read 权限").into_response();
    }

    let service = PreMaintainedFlexibleWorkService::new(db_config);
    
    match service.get_pre_maintained_flexible_work_by_opcode(&opcode).await {
        Ok(Some(result)) => ApiResponse::success_with_data(result).into_response(),
        Ok(None) => ApiResponse::not_found("预维护零活不存在").into_response(),
        Err(e) => {
            tracing::error!("获取预维护零活详情失败: {}", e);
            ApiResponse::internal_error("获取预维护零活详情失败").into_response()
        }
    }
}
