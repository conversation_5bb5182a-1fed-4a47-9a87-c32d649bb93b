use crate::config::{DatabaseConfig, Settings};
use crate::models::equipment::EquipmentQueryRequest;
use crate::services::equipment_service::EquipmentService;
use crate::utils::ApiResponse;
use crate::extractors::AuthenticatedUser;
use axum::{
    extract::{Path, Query, State},
    response::IntoResponse,
};
use std::sync::Arc;

type AppState = (DatabaseConfig, Settings);

// 权限检查辅助函数 - 支持配置开关
async fn check_permission(
    auth_user: &AuthenticatedUser,
    resource: &str,
    action: &str,
    db_config: &DatabaseConfig,
    settings: &Settings,
) -> bool {
    crate::utils::check_permission(auth_user, resource, action, db_config, settings).await
}

/// 获取设备列表
pub async fn list_equipments_handler(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Query(request): Query<EquipmentQueryRequest>,
) -> impl IntoResponse {
    // 权限检查：需要 equipments:read 权限
    if !check_permission(&auth_user, "equipments", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 equipments:read 权限").into_response();
    }

    let equipment_service = EquipmentService::new(Arc::new(db_config));

    match equipment_service.get_equipments(request).await {
        Ok(response) => {
            ApiResponse::success_with_message(response, "获取设备列表成功").into_response()
        }
        Err(e) => {
            tracing::error!("获取设备列表失败: {}", e);
            ApiResponse::internal_error("获取设备列表失败").into_response()
        }
    }
}

/// 根据设备编码获取单个设备
pub async fn get_equipment_handler(
    State((db_config, settings)): State<AppState>,
    auth_user: AuthenticatedUser,
    Path(ceqcode): Path<String>,
) -> impl IntoResponse {
    // 权限检查：需要 equipments:read 权限
    if !check_permission(&auth_user, "equipments", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 equipments:read 权限").into_response();
    }

    let equipment_service = EquipmentService::new(Arc::new(db_config));

    match equipment_service.get_equipment_by_code(&ceqcode).await {
        Ok(equipment) => {
            ApiResponse::success_with_message(equipment, "获取设备信息成功").into_response()
        }
        Err(e) => {
            tracing::error!("获取设备信息失败: {}", e);
            ApiResponse::internal_error("获取设备信息失败").into_response()
        }
    }
}
