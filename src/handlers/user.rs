// 优化的用户处理器 - 使用新的权限架构
use axum::{
    extract::{Path, Query, State},
    response::IntoResponse,
    Json,
};
use crate::config::{DatabaseConfig, Settings};
use crate::extractors::AuthenticatedUser;
use crate::models::{UserQuery, UpdateUserRequest, CreateUserRequest};
use crate::services::UserService;
use crate::utils::ApiResponse;

type AppState = (DatabaseConfig, Settings);

// 权限检查辅助函数 - 支持配置开关
async fn check_permission(
    auth_user: &AuthenticatedUser,
    resource: &str,
    action: &str,
    db_config: &DatabaseConfig,
    settings: &Settings,
) -> bool {
    crate::utils::check_permission(auth_user, resource, action, db_config, settings).await
}

// 辅助函数：获取当前用户的角色名称
async fn get_current_user_role_name(auth_user: &AuthenticatedUser, db_config: &DatabaseConfig) -> Option<String> {
    if auth_user.is_admin() {
        return Some("admin".to_string());
    }

    if let Some(role_id) = auth_user.role_id() {
        let role_service = crate::services::RoleService::new(db_config.clone());
        if let Ok(role) = role_service.get_role_by_id(role_id).await {
            return Some(role.name);
        }
    }

    None
}

// 获取用户列表 - 需要 users:read 权限
pub async fn list_users_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Query(query): Query<UserQuery>,
) -> impl IntoResponse {
    // 权限检查：需要 users:read 权限
    if !check_permission(&auth_user, "users", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 users:read 权限").into_response();
    }

    let user_service = UserService::new(db_config.clone());

    // 获取当前用户角色名称用于过滤
    let current_user_role = get_current_user_role_name(&auth_user, &db_config).await;

    match user_service.get_users_with_role_filter(&query, current_user_role.as_deref()).await {
        Ok(users) => {
            ApiResponse::success_with_message(users, "获取用户列表成功").into_response()
        }
        Err(e) => {
            tracing::error!("获取用户列表失败: {}", e);
            match &e {
                crate::utils::AppError::Permission(msg) => ApiResponse::forbidden(msg.clone()).into_response(),
                _ => ApiResponse::internal_error("获取用户列表失败").into_response(),
            }
        }
    }
}

// 获取单个用户 - 需要 users:read 权限
pub async fn get_user_handler(
    State((db_config, _settings)): State<AppState>,
    Path(cpsn_num): Path<String>,
) -> impl IntoResponse {

    let user_service = UserService::new(db_config);

    match user_service.get_user_by_id(&cpsn_num).await {
        Ok(user) => {
            ApiResponse::success_with_message(user, "获取用户信息成功").into_response()
        }
        Err(e) => {
            tracing::error!("获取用户信息失败: {}", e);
            ApiResponse::error(404, "用户不存在").into_response()
        }
    }
}

// 创建新用户 - 需要 users:create 权限
pub async fn create_user_handler(
    auth_user: AuthenticatedUser,
    State((db_config, _settings)): State<AppState>,
    Json(create_request): Json<CreateUserRequest>,
) -> impl IntoResponse {
    tracing::info!("收到创建用户请求: cpsn_num={}, username={}", create_request.cpsn_num, create_request.username);

    // 现在我们有了真实的用户信息，不再需要硬编码！
    let _user_claims = auth_user.claims();

    let _role_service = crate::services::RoleService::new(db_config.clone());
    let user_service = UserService::new(db_config);

    // 获取当前用户的角色
    let current_user_id = auth_user.user_id_string();
    tracing::info!("当前用户ID: {}", current_user_id);

    // 权限检查：admin用户可以创建任何用户
    if !auth_user.is_admin() {
        return ApiResponse::forbidden("权限不足，只有管理员可以创建用户").into_response();
    }

    tracing::info!("权限检查通过，开始创建用户");

    // 执行创建用户
    match user_service.create_user(&create_request).await {
        Ok(user) => {
            ApiResponse::created_with_message(user, "创建用户成功").into_response()
        }
        Err(e) => {
            tracing::error!("创建用户失败: {}", e);
            ApiResponse::bad_request(format!("创建用户失败: {}", e)).into_response()
        }
    }
}

// 更新用户信息 - 需要 users:update 权限
pub async fn update_user_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Path(cpsn_num): Path<String>,
    Json(update_request): Json<UpdateUserRequest>,
) -> impl IntoResponse {
    // 权限检查：需要 users:update 权限
    if !check_permission(&auth_user, "users", "update", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 users:update 权限").into_response();
    }

    // 记录更新操作
    let has_password_update = update_request.password.is_some() &&
        !update_request.password.as_ref().unwrap().trim().is_empty();

    if has_password_update {
        tracing::info!("用户 {} 尝试更新用户 {} 的密码", auth_user.user_id_string(), cpsn_num);
    }

    let user_service = UserService::new(db_config);

    match user_service.update_user(&cpsn_num, &update_request).await {
        Ok(user) => {
            let message = if has_password_update {
                "更新用户信息和密码成功"
            } else {
                "更新用户信息成功"
            };
            ApiResponse::success_with_message(user, message).into_response()
        }
        Err(e) => {
            tracing::error!("更新用户信息失败: {}", e);
            ApiResponse::bad_request(format!("更新用户信息失败: {}", e)).into_response()
        }
    }
}

// 删除用户 - 需要 users:delete 权限
pub async fn delete_user_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Path(user_id): Path<String>,
) -> impl IntoResponse {
    // 权限检查：需要 users:delete 权限
    if !check_permission(&auth_user, "users", "delete", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 users:delete 权限").into_response();
    }

    // 安全检查1：用户不能删除自己
    if auth_user.user_id_string() == user_id {
        return ApiResponse::bad_request("不能删除自己的账户").into_response();
    }

    // 安全检查2：admin账户不可删除
    if user_id == "admin" {
        return ApiResponse::bad_request("admin账户不可删除").into_response();
    }

    // 安全检查3：检查目标用户是否为管理员
    let user_service = UserService::new(db_config.clone());
    match user_service.get_user_by_id(&user_id).await {
        Ok(target_user) => {
            // 如果目标用户是管理员，只有超级管理员才能删除
            if target_user.is_admin && !auth_user.is_admin() {
                return ApiResponse::forbidden("只有管理员才能删除管理员账户").into_response();
            }
        }
        Err(_) => {
            return ApiResponse::not_found("用户不存在").into_response();
        }
    }

    tracing::info!("用户 {} 尝试删除用户 {}", auth_user.user_id_string(), user_id);

    match user_service.delete_user(&user_id, auth_user.user_id_string()).await {
        Ok(_) => {
            tracing::info!("用户 {} 删除成功", user_id);
            ApiResponse::success_empty_with_message("删除用户成功").into_response()
        }
        Err(e) => {
            tracing::error!("删除用户失败: {}", e);
            ApiResponse::bad_request(format!("删除用户失败: {}", e)).into_response()
        }
    }
}

// 获取没有角色关联的用户列表 - 需要 users:read 权限
pub async fn list_unassigned_users_handler(
    auth_user: AuthenticatedUser,
    State((db_config, settings)): State<AppState>,
    Query(query): Query<UserQuery>,
) -> impl IntoResponse {
    // 权限检查：需要 users:read 权限
    if !check_permission(&auth_user, "users", "read", &db_config, &settings).await {
        return ApiResponse::forbidden("权限不足，需要 users:read 权限").into_response();
    }

    let user_service = UserService::new(db_config.clone());

    match user_service.get_unassigned_users(&query).await {
        Ok(users) => {
            ApiResponse::success_with_message(users, "获取未分配用户列表成功").into_response()
        }
        Err(e) => {
            tracing::error!("获取未分配用户列表失败: {}", e);
            match &e {
                crate::utils::AppError::Permission(msg) => ApiResponse::forbidden(msg.clone()).into_response(),
                _ => ApiResponse::internal_error("获取未分配用户列表失败").into_response(),
            }
        }
    }
}
