use jsonwebtoken::{decode, encode, Decod<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Val<PERSON><PERSON>, Algorithm};
use serde::{Deserialize, Serialize};
use chrono::{Duration, Utc};
use crate::utils::{AppError, AppResult};
use std::collections::HashSet;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,      // 用户ID
    pub username: String, // 用户名
    pub is_admin: bool,   // 是否管理员
    pub role_id: Option<i64>, // 角色ID
    pub exp: i64,         // 过期时间
    pub iat: i64,         // 签发时间
}

impl Claims {
    pub fn new(user_id: &str, username: &str, is_admin: bool, role_id: Option<i64>, expires_in: Duration) -> Self {
        let now = Utc::now();
        Self {
            sub: user_id.to_string(),
            username: username.to_string(),
            is_admin,
            role_id,
            exp: (now + expires_in).timestamp(),
            iat: now.timestamp(),
        }
    }
}

pub fn create_jwt(claims: &Claims, secret: &str) -> AppResult<String> {
    let token = encode(
        &Header::default(),
        claims,
        &EncodingKey::from_secret(secret.as_ref()),
    )?;
    Ok(token)
}

pub fn verify_jwt(token: &str, secret: &str) -> AppResult<Claims> {
    let mut validation = Validation::new(Algorithm::HS256);
    validation.required_spec_claims = HashSet::new(); // 允许自定义claims

    // 移除调试信息，避免敏感信息泄露
    tracing::debug!("验证JWT token");

    let token_data = decode::<Claims>(
        token,
        &DecodingKey::from_secret(secret.as_ref()),
        &validation,
    ).map_err(|e| {
        tracing::warn!("JWT解码失败: {}", e);
        AppError::Authentication("无效的Token格式".to_string())
    })?;

    // 检查token是否过期
    let now = Utc::now().timestamp();
    if token_data.claims.exp < now {
        tracing::debug!("Token已过期: exp={}, now={}", token_data.claims.exp, now);
        return Err(AppError::Authentication("Token已过期".to_string()));
    }

    tracing::debug!("Token验证成功，用户: {}", token_data.claims.username);
    Ok(token_data.claims)
}

pub fn extract_token_from_header(auth_header: &str) -> AppResult<&str> {
    if auth_header.starts_with("Bearer ") {
        let token = &auth_header[7..];
        if token.is_empty() {
            return Err(AppError::Authentication("Token不能为空".to_string()));
        }
        Ok(token)
    } else {
        Err(AppError::Authentication("无效的认证头格式，应为 'Bearer <token>'".to_string()))
    }
}

// create_refresh_token 已废弃，刷新令牌功能暂未完全实现

// 验证刷新Token
pub fn verify_refresh_token(token: &str, secret: &str) -> AppResult<RefreshTokenClaims> {
    let mut validation = Validation::new(Algorithm::HS256);
    validation.required_spec_claims = HashSet::new();

    let token_data = decode::<RefreshTokenClaims>(
        token,
        &DecodingKey::from_secret(secret.as_ref()),
        &validation,
    )?;

    // 检查token是否过期
    let now = Utc::now().timestamp();
    if token_data.claims.exp < now {
        return Err(AppError::Authentication("刷新Token已过期".to_string()));
    }

    Ok(token_data.claims)
}

// 刷新Token的Claims结构
#[derive(Debug, Serialize, Deserialize)]
pub struct RefreshTokenClaims {
    pub sub: String,      // 用户ID
    pub exp: i64,         // 过期时间
    pub iat: i64,         // 签发时间
    pub token_type: String, // token类型
}

// 以下刷新令牌和高级验证功能已废弃，暂未完全实现
// impl RefreshTokenClaims { ... }
// pub struct TokenValidationResult { ... }
// pub fn validate_token_detailed(...) -> AppResult<TokenValidationResult> { ... }
// pub fn is_token_expiring_soon(...) -> AppResult<bool> { ... }
