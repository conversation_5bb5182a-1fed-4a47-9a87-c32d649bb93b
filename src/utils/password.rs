use bcrypt::{hash, verify, DEFAULT_COST};
use crate::utils::AppResult;

pub fn hash_password(password: &str) -> AppResult<String> {
    let cost = std::env::var("BCRYPT_COST")
        .unwrap_or_else(|_| "12".to_string())
        .parse::<u32>()
        .unwrap_or(DEFAULT_COST);
    
    let hashed = hash(password, cost)?;
    Ok(hashed)
}

pub fn verify_password(password: &str, hash: &str) -> AppResult<bool> {
    let is_valid = verify(password, hash)?;
    Ok(is_valid)
}
