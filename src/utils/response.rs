use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    Json,
};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub code: u16,
    pub message: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub data: Option<T>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub error: Option<String>,
    #[serde(with = "crate::utils::local_timestamp")]
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PaginatedResponse<T> {
    pub items: Vec<T>,
    pub total: i64,
    pub page: i32,
    pub page_size: i32,
    pub total_pages: i32,
}

impl<T> ApiResponse<T>
where
    T: Serialize,
{
    /// 创建成功响应
    pub fn success(data: T) -> Self {
        Self {
            code: 200,
            message: "操作成功".to_string(),
            data: Some(data),
            error: None,
            timestamp: Utc::now(),
        }
    }

    /// 创建成功响应（自定义消息）
    pub fn success_with_message(data: T, message: impl Into<String>) -> Self {
        Self {
            code: 200,
            message: message.into(),
            data: Some(data),
            error: None,
            timestamp: Utc::now(),
        }
    }

    /// 创建成功响应（带数据）
    pub fn success_with_data(data: T) -> Self {
        Self::success(data)
    }

    /// 创建创建成功响应（自定义消息）
    pub fn created_with_message(data: T, message: impl Into<String>) -> Self {
        Self {
            code: 201,
            message: message.into(),
            data: Some(data),
            error: None,
            timestamp: Utc::now(),
        }
    }
}

impl ApiResponse<()> {
    /// 创建无数据的成功响应（自定义消息）
    pub fn success_empty_with_message(message: impl Into<String>) -> Self {
        Self {
            code: 200,
            message: message.into(),
            data: None,
            error: None,
            timestamp: Utc::now(),
        }
    }

    /// 创建错误响应
    pub fn error(code: u16, message: impl Into<String>) -> Self {
        Self {
            code,
            message: message.into(),
            data: None,
            error: None,
            timestamp: Utc::now(),
        }
    }

    /// 创建错误响应（带详细错误信息）
    pub fn error_with_details(
        code: u16,
        message: impl Into<String>,
        error: impl Into<String>,
    ) -> Self {
        Self {
            code,
            message: message.into(),
            data: None,
            error: Some(error.into()),
            timestamp: Utc::now(),
        }
    }

    /// 创建认证错误响应
    pub fn unauthorized(message: impl Into<String>) -> Self {
        Self {
            code: 401,
            message: message.into(),
            data: None,
            error: None,
            timestamp: Utc::now(),
        }
    }

    /// 创建权限错误响应
    pub fn forbidden(message: impl Into<String>) -> Self {
        Self {
            code: 403,
            message: message.into(),
            data: None,
            error: None,
            timestamp: Utc::now(),
        }
    }

    /// 创建资源不存在响应
    pub fn not_found(message: impl Into<String>) -> Self {
        Self {
            code: 404,
            message: message.into(),
            data: None,
            error: None,
            timestamp: Utc::now(),
        }
    }

    /// 创建请求错误响应
    pub fn bad_request(message: impl Into<String>) -> Self {
        Self {
            code: 400,
            message: message.into(),
            data: None,
            error: None,
            timestamp: Utc::now(),
        }
    }

    /// 创建服务器错误响应
    pub fn internal_error(message: impl Into<String>) -> Self {
        Self {
            code: 500,
            message: message.into(),
            data: None,
            error: None,
            timestamp: Utc::now(),
        }
    }
}

impl<T> IntoResponse for ApiResponse<T>
where
    T: Serialize,
{
    fn into_response(self) -> Response {
        let status_code =
            StatusCode::from_u16(self.code).unwrap_or(StatusCode::INTERNAL_SERVER_ERROR);
        (status_code, Json(self)).into_response()
    }
}

impl<T> PaginatedResponse<T> {
    pub fn new(items: Vec<T>, total: i64, page: i32, page_size: i32) -> Self {
        let total_pages = if total == 0 {
            0
        } else {
            ((total - 1) / page_size as i64 + 1) as i32
        };

        Self {
            items,
            total,
            page,
            page_size,
            total_pages,
        }
    }
}

// 便捷的响应构建函数
pub fn success<T: Serialize>(data: T) -> ApiResponse<T> {
    ApiResponse::success(data)
}

/// 便捷的响应构建宏
#[macro_export]
macro_rules! api_response {
    // 成功响应
    (success, $data:expr) => {
        $crate::utils::response::ApiResponse::success($data)
    };
    (success, $data:expr, $message:expr) => {
        $crate::utils::response::ApiResponse::success_with_message($data, $message)
    };

    // 创建响应
    (created, $data:expr) => {
        $crate::utils::response::ApiResponse::created($data)
    };
    (created, $data:expr, $message:expr) => {
        $crate::utils::response::ApiResponse::created_with_message($data, $message)
    };

    // 错误响应
    (error, $code:expr, $message:expr) => {
        $crate::utils::response::ApiResponse::error($code, $message)
    };
    (unauthorized, $message:expr) => {
        $crate::utils::response::ApiResponse::unauthorized($message)
    };
    (forbidden, $message:expr) => {
        $crate::utils::response::ApiResponse::forbidden($message)
    };
    (bad_request, $message:expr) => {
        $crate::utils::response::ApiResponse::bad_request($message)
    };
    (internal_error, $message:expr) => {
        $crate::utils::response::ApiResponse::internal_error($message)
    };

    // 空响应
    (empty) => {
        $crate::utils::response::ApiResponse::success_empty()
    };
    (empty, $message:expr) => {
        $crate::utils::response::ApiResponse::success_empty_with_message($message)
    };
}

// 自定义错误类型
#[derive(Debug, thiserror::Error)]
pub enum AppError {
    #[error("数据库错误: {0}")]
    Database(String),

    #[error("数据库错误: {0}")]
    DatabaseError(String),

    #[error("认证错误: {0}")]
    Authentication(String),

    #[error("权限错误: {0}")]
    Permission(String),

    #[error("权限不足: {0}")]
    Forbidden(String),

    #[error("资源不存在: {0}")]
    NotFound(String),

    #[error("请求错误: {0}")]
    BadRequest(String),

    // Authorization 变体已废弃，使用 Authentication 或 Permission 替代
    #[error("验证错误: {0}")]
    Validation(String),

    #[error("业务错误: {0}")]
    Business(String),

    #[error("配置错误: {0}")]
    Config(#[from] config::ConfigError),

    #[error("JWT错误: {0}")]
    Jwt(#[from] jsonwebtoken::errors::Error),

    #[error("密码错误: {0}")]
    Password(#[from] bcrypt::BcryptError),

    #[error("IO错误: {0}")]
    Io(#[from] std::io::Error),

    #[error("URL解析错误: {0}")]
    UrlParse(#[from] url::ParseError),

    #[error("Tiberius错误: {0}")]
    Tiberius(#[from] tiberius::error::Error),

    #[error("任务调度错误: {0}")]
    JobScheduler(#[from] tokio_cron_scheduler::JobSchedulerError),

    #[error("内部错误: {0}")]
    Internal(#[from] anyhow::Error),
}

impl IntoResponse for AppError {
    fn into_response(self) -> Response {
        let (status_code, message) = match &self {
            AppError::Permission(_) => (StatusCode::FORBIDDEN, "无权限访问"),
            AppError::Database(_) => (StatusCode::INTERNAL_SERVER_ERROR, "数据库操作失败"),
            AppError::DatabaseError(_) => (StatusCode::INTERNAL_SERVER_ERROR, "数据库操作失败"),
            AppError::Authentication(_) => (StatusCode::UNAUTHORIZED, "认证失败"),
            AppError::Forbidden(_) => (StatusCode::FORBIDDEN, "权限不足"),
            AppError::NotFound(_) => (StatusCode::NOT_FOUND, "资源不存在"),
            AppError::BadRequest(_) => (StatusCode::BAD_REQUEST, "请求错误"),
            // AppError::Authorization 已废弃
            AppError::Validation(_) => (StatusCode::BAD_REQUEST, "参数验证失败"),
            AppError::Business(_) => (StatusCode::BAD_REQUEST, "业务处理失败"),
            AppError::Config(_) => (StatusCode::INTERNAL_SERVER_ERROR, "配置错误"),
            AppError::Jwt(_) => (StatusCode::UNAUTHORIZED, "Token无效"),
            AppError::Password(_) => (StatusCode::INTERNAL_SERVER_ERROR, "密码处理失败"),
            AppError::Io(_) => (StatusCode::INTERNAL_SERVER_ERROR, "IO操作失败"),
            AppError::UrlParse(_) => (StatusCode::INTERNAL_SERVER_ERROR, "URL解析失败"),
            AppError::Tiberius(_) => (StatusCode::INTERNAL_SERVER_ERROR, "数据库连接失败"),
            AppError::JobScheduler(_) => (StatusCode::INTERNAL_SERVER_ERROR, "任务调度失败"),
            AppError::Internal(_) => (StatusCode::INTERNAL_SERVER_ERROR, "内部服务器错误"),
        };

        let error_response =
            ApiResponse::<()>::error_with_details(status_code.as_u16(), message, &self.to_string());

        (status_code, Json(error_response)).into_response()
    }
}

pub type AppResult<T> = Result<T, AppError>;
