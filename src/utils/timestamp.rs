// 时间戳序列化工具
use chrono::{DateTime, Utc, TimeZone};
use serde::{Deserialize, Deserializer, Serializer};

/// 将DateTime<Utc>序列化为时间戳（秒）
pub fn serialize<S>(date: &DateTime<Utc>, serializer: S) -> Result<S::Ok, S::Error>
where
    S: Serializer,
{
    serializer.serialize_i64(date.timestamp())
}

/// 将时间戳（秒）反序列化为DateTime<Utc>
pub fn deserialize<'de, D>(deserializer: D) -> Result<DateTime<Utc>, D::Error>
where
    D: Deserializer<'de>,
{
    let timestamp = i64::deserialize(deserializer)?;
    Ok(Utc.timestamp_opt(timestamp, 0).single().unwrap_or_else(|| Utc::now()))
}

/// 处理Option<DateTime<Utc>>的时间戳序列化
pub mod timestamp_option {
    use super::*;
    use serde::{Deserialize, Deserializer, Serializer};

    pub fn serialize<S>(date: &Option<DateTime<Utc>>, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        match date {
            Some(dt) => serializer.serialize_some(&dt.timestamp()),
            None => serializer.serialize_none(),
        }
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<Option<DateTime<Utc>>, D::Error>
    where
        D: Deserializer<'de>,
    {
        let opt: Option<i64> = Option::deserialize(deserializer)?;
        Ok(opt.map(|timestamp| {
            Utc.timestamp_opt(timestamp, 0).single().unwrap_or_else(|| Utc::now())
        }))
    }
}

/// 处理时间字符串输入（yyyy-MM-dd HH:mm:ss格式）
pub mod datetime_string {
    use super::*;
    use serde::{Deserialize, Deserializer, Serializer};
    use chrono::NaiveDateTime;

    pub fn serialize<S>(date: &DateTime<Utc>, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        let formatted = date.format("%Y-%m-%d %H:%M:%S").to_string();
        serializer.serialize_str(&formatted)
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<DateTime<Utc>, D::Error>
    where
        D: Deserializer<'de>,
    {
        let s = String::deserialize(deserializer)?;
        
        // 尝试解析 yyyy-MM-dd HH:mm:ss 格式
        if let Ok(naive_dt) = NaiveDateTime::parse_from_str(&s, "%Y-%m-%d %H:%M:%S") {
            return Ok(Utc.from_utc_datetime(&naive_dt));
        }

        // 尝试解析 yyyy-MM-dd 格式
        if let Ok(naive_date) = chrono::NaiveDate::parse_from_str(&s, "%Y-%m-%d") {
            let naive_dt = naive_date.and_hms_opt(0, 0, 0).unwrap_or_default();
            return Ok(Utc.from_utc_datetime(&naive_dt));
        }
        
        // 如果都解析失败，返回当前时间
        Ok(Utc::now())
    }
}

/// 处理Option<DateTime<Utc>>的字符串序列化
pub mod datetime_string_option {
    use super::*;
    use serde::{Deserialize, Deserializer, Serializer};
    use chrono::NaiveDateTime;

    pub fn serialize<S>(date: &Option<DateTime<Utc>>, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        match date {
            Some(dt) => {
                let formatted = dt.format("%Y-%m-%d %H:%M:%S").to_string();
                serializer.serialize_some(&formatted)
            }
            None => serializer.serialize_none(),
        }
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<Option<DateTime<Utc>>, D::Error>
    where
        D: Deserializer<'de>,
    {
        let opt: Option<String> = Option::deserialize(deserializer)?;
        Ok(opt.and_then(|s| {
            // 尝试解析 yyyy-MM-dd HH:mm:ss 格式
            if let Ok(naive_dt) = NaiveDateTime::parse_from_str(&s, "%Y-%m-%d %H:%M:%S") {
                return Some(Utc.from_utc_datetime(&naive_dt));
            }

            // 尝试解析 yyyy-MM-dd 格式
            if let Ok(naive_date) = chrono::NaiveDate::parse_from_str(&s, "%Y-%m-%d") {
                let naive_dt = naive_date.and_hms_opt(0, 0, 0).unwrap_or_default();
                return Some(Utc.from_utc_datetime(&naive_dt));
            }
            
            None
        }))
    }
}

/// 处理数据库本地时间的序列化（UTC+8时区）
/// 专门用于从数据库读取的本地时间，避免重复时区转换
pub mod local_timestamp {
    use super::*;
    use serde::{Deserialize, Deserializer, Serializer};

    pub fn serialize<S>(date: &DateTime<Utc>, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        // 数据库存储的是本地时间（UTC+8），但被当作UTC处理了
        // 所以我们需要减去8小时来得到正确的时间戳
        let local_timestamp = date.timestamp() - (8 * 3600);
        serializer.serialize_i64(local_timestamp)
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<DateTime<Utc>, D::Error>
    where
        D: Deserializer<'de>,
    {
        let timestamp = i64::deserialize(deserializer)?;
        // 反序列化时，将时间戳转换为UTC时间，然后加8小时来模拟本地时间存储
        let utc_time = Utc.timestamp_opt(timestamp + (8 * 3600), 0).single().unwrap_or_else(|| Utc::now());
        Ok(utc_time)
    }
}

/// 处理Option<DateTime<Utc>>的本地时间序列化
pub mod local_timestamp_option {
    use super::*;
    use serde::{Deserialize, Deserializer, Serializer};

    pub fn serialize<S>(date: &Option<DateTime<Utc>>, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        match date {
            Some(dt) => {
                // 数据库存储的是本地时间（UTC+8），但被当作UTC处理了
                let local_timestamp = dt.timestamp() - (8 * 3600);
                serializer.serialize_some(&local_timestamp)
            },
            None => serializer.serialize_none(),
        }
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<Option<DateTime<Utc>>, D::Error>
    where
        D: Deserializer<'de>,
    {
        let opt: Option<i64> = Option::deserialize(deserializer)?;
        Ok(opt.map(|timestamp| {
            let utc_time = Utc.timestamp_opt(timestamp + (8 * 3600), 0).single().unwrap_or_else(|| Utc::now());
            utc_time
        }))
    }
}
