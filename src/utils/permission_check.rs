use crate::config::{DatabaseConfig, Settings};
use crate::extractors::AuthenticatedUser;
use crate::services::OptimizedPermissionService;

/// 全局权限检查函数 - 支持配置开关
pub async fn check_permission_with_config(
    auth_user: &AuthenticatedUser,
    resource: &str,
    action: &str,
    db_config: &DatabaseConfig,
    settings: &Settings,
) -> bool {
    // 检查权限校验开关
    if !settings.security.enable_permission_check {
        tracing::info!("权限校验已禁用，允许所有操作: 资源={}, 操作={}", resource, action);
        return true;
    }

    // 管理员拥有所有权限
    if auth_user.is_admin() {
        return true;
    }

    // 获取用户ID
    let user_id = match auth_user.user_id() {
        Ok(id) => id,
        Err(_) => {
            tracing::error!("无法获取用户ID进行权限检查");
            return false;
        }
    };

    // 使用权限服务进行实际检查
    let permission_service = OptimizedPermissionService::new(db_config.clone());

    match permission_service.check_user_permission_cached(&user_id, resource, action).await {
        Ok(allowed) => {
            tracing::debug!(
                "权限检查结果: 用户ID={}, 资源={}, 操作={}, 允许={}",
                user_id, resource, action, allowed
            );
            allowed
        }
        Err(e) => {
            tracing::error!(
                "权限检查失败: 用户ID={}, 资源={}, 操作={}, 错误={}",
                user_id, resource, action, e
            );
            false
        }
    }
}

/// 兼容性函数 - 保持原有接口不变，但需要传入settings
pub async fn check_permission(
    auth_user: &AuthenticatedUser,
    resource: &str,
    action: &str,
    db_config: &DatabaseConfig,
    settings: &Settings,
) -> bool {
    check_permission_with_config(auth_user, resource, action, db_config, settings).await
}
