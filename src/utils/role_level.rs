// 简化的角色级别定义，替代废弃的permission模块
use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON>lone, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum RoleLevel {
    User = 1,
    TeamLeader = 2,
    Manager = 3,
    Admin = 4,
}

impl RoleLevel {
    /// 从权限字符串推断角色级别（简化版本）
    pub fn from_permissions(permissions: &str) -> Self {
        // 简化的角色级别判断逻辑
        let perm_lower = permissions.to_lowercase();
        if perm_lower.contains("admin") || perm_lower.contains("*") {
            RoleLevel::Admin
        } else if perm_lower.contains("manager") || perm_lower.contains("role") {
            RoleLevel::Manager
        } else if perm_lower.contains("team") || perm_lower.contains("leader") {
            RoleLevel::TeamLeader
        } else {
            RoleLevel::User
        }
    }

    /// 从角色名称推断角色级别
    pub fn from_role_name(role_name: &str) -> Self {
        match role_name.to_lowercase().as_str() {
            "admin" | "administrator" | "管理员" => RoleLevel::Admin,
            "manager" | "负责人" => RoleLevel::Manager,
            "team_leader" | "leader" | "班长" => RoleLevel::TeamLeader,
            _ => RoleLevel::User,
        }
    }

    /// 从数据库level字段推断角色级别
    pub fn from_level(level: i32) -> Self {
        match level {
            1 => RoleLevel::Admin,
            2 => RoleLevel::Manager,
            3 => RoleLevel::TeamLeader,
            _ => RoleLevel::User, // 4或其他值默认为User
        }
    }

    /// 检查是否可以分配指定角色
    pub fn can_assign_role(&self, target_role: &RoleLevel) -> bool {
        // 只能分配比自己级别低或相等的角色
        self >= target_role
    }

    /// 检查是否可以移除指定角色
    pub fn can_remove_role(&self, target_role: &RoleLevel) -> bool {
        // 只能移除比自己级别低的角色
        self > target_role
    }
}

impl Default for RoleLevel {
    fn default() -> Self {
        RoleLevel::User
    }
}

impl std::fmt::Display for RoleLevel {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            RoleLevel::Admin => write!(f, "管理员"),
            RoleLevel::Manager => write!(f, "负责人"),
            RoleLevel::TeamLeader => write!(f, "班长"),
            RoleLevel::User => write!(f, "普通用户"),
        }
    }
}
