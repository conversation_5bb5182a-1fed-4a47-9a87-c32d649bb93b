// 认证用户提取器 - 解决Axum提取器冲突问题
use axum::{
    async_trait,
    extract::FromRequestParts,
    http::request::Parts,
    response::{IntoResponse, Response},
};
use crate::utils::{Claims, ApiResponse};

/// 认证用户提取器
/// 
/// 这个提取器从请求扩展中获取由认证中间件注入的用户信息
/// 使用FromRequestParts trait避免与Json提取器冲突
#[derive(Debug, Clone)]
pub struct AuthenticatedUser(pub Claims);

impl AuthenticatedUser {
    /// 获取用户Claims
    pub fn claims(&self) -> &Claims {
        &self.0
    }
    
    /// 获取用户ID (字符串格式，如cpsn_num)
    pub fn user_id_string(&self) -> &str {
        &self.0.sub
    }

    /// 获取用户ID (尝试解析为i64，用于兼容旧代码)
    pub fn user_id(&self) -> Result<String, String> {
        self.0.sub.parse().map_err(|_| "无效的用户ID".to_string())
    }
    
    /// 检查是否为管理员
    pub fn is_admin(&self) -> bool {
        self.0.is_admin
    }
    
    /// 获取角色ID
    pub fn role_id(&self) -> Option<i64> {
        self.0.role_id
    }
}

#[async_trait]
impl<S> FromRequestParts<S> for AuthenticatedUser
where
    S: Send + Sync,
{
    type Rejection = Response;

    async fn from_request_parts(parts: &mut Parts, _state: &S) -> Result<Self, Self::Rejection> {
        // 从请求扩展中获取由认证中间件注入的用户信息
        let claims = parts
            .extensions
            .get::<Claims>()
            .ok_or_else(|| {
                // 返回401未认证错误
                ApiResponse::unauthorized("未找到用户认证信息").into_response()
            })?;

        Ok(AuthenticatedUser(claims.clone()))
    }
}





#[cfg(test)]
mod tests {
    use super::*;
    use axum::http::Request;
    use axum::body::Body;

    #[tokio::test]
    async fn test_authenticated_user_extractor() {
        // 创建测试Claims
        let test_claims = Claims {
            sub: "123".to_string(),
            username: "testuser".to_string(),
            is_admin: false,
            role_id: Some(2),
            exp: 0,
            iat: 0,
        };

        // 创建带有Claims的请求
        let mut request = Request::builder()
            .uri("/test")
            .body(Body::empty())
            .unwrap();
        
        request.extensions_mut().insert(test_claims.clone());
        
        let (mut parts, _body) = request.into_parts();
        
        // 测试提取器
        let auth_user = AuthenticatedUser::from_request_parts(&mut parts, &()).await;
        
        assert!(auth_user.is_ok());
        let auth_user = auth_user.unwrap();
        assert_eq!(auth_user.user_id().unwrap(), "123");
        assert!(!auth_user.is_admin());
        assert_eq!(auth_user.role_id(), Some(2));
    }


}
