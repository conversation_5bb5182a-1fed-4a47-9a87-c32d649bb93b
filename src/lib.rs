// U8 权限管理系统扩展项目 - 库文件
// 用于支持测试和模块化

pub mod config;
pub mod constants;
pub mod extractors;
pub mod handlers;
pub mod middleware;
pub mod models;
pub mod services;
pub mod tasks;
pub mod utils;

// 重新导出常用类型和函数
pub use config::{DatabaseConfig, Settings};
pub use utils::{AppResult, AppError};

// 重新导出服务
pub use services::{
    AuthService,
    UserService,
    RoleService,
    CustomPermissionService,

};

// 重新导出优化的权限服务
pub use services::permission_service_optimized::OptimizedPermissionService;

// 重新导出模型
pub use models::{
    User, Role, Permission,
    CreateUserRequest, UpdateUserRequest, UserResponse,
    CreateRoleRequest, UpdateRoleRequest, RoleResponse,
    LoginRequest, LoginResponse,
    CreateCustomPermissionRequest, CustomPermission, CustomPermissionAction,
};

// 从permission模块导出权限检查相关类型
pub use models::permission::{CheckPermissionRequest, CheckPermissionResponse};

// 从permission模块导出特定的类型
pub use models::permission::UpdateRolePermissionsRequest;

// 重新导出处理器
pub use handlers::{
    auth::*,
    user::*,
    role::*,
    permission_optimized::*,

};

// 重新导出中间件
pub use middleware::auth_optimized::*;

// 重新导出提取器
pub use extractors::AuthenticatedUser;
// 版本信息
pub const VERSION: &str = env!("CARGO_PKG_VERSION");
pub const NAME: &str = env!("CARGO_PKG_NAME");
pub const DESCRIPTION: &str = env!("CARGO_PKG_DESCRIPTION");

/// 获取系统信息
pub fn get_system_info() -> serde_json::Value {
    serde_json::json!({
        "name": NAME,
        "version": VERSION,
        "description": DESCRIPTION,
    })
}

/// 初始化日志系统（用于测试）
#[cfg(test)]
pub fn init_test_logging() {
    use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
    
    let _ = tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "debug".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .try_init();
}
