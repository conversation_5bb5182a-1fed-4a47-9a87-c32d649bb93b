use crate::config::DatabaseConfig;
use crate::utils::{hash_password, AppResult};
use tiberius::Query;
use tracing::info;

/// 数据库初始化服务
pub struct DatabaseInitializer {
    db_config: DatabaseConfig,
}

impl DatabaseInitializer {
    pub fn new(db_config: DatabaseConfig) -> Self {
        Self { db_config }
    }

    /// 初始化数据库，包括创建admin账户
    pub async fn initialize(&self) -> AppResult<()> {
        info!("开始数据库初始化...");

        // 1. 初始化admin账户
        self.initialize_admin_account().await?;

        // 2. 初始化基础角色
        self.initialize_basic_roles().await?;

        // 3. 初始化权限数据
        self.initialize_permissions().await?;

        // 4. 分配admin角色给admin账户
        self.assign_admin_role().await?;

        // 5. 修复数据库约束
        self.fix_database_constraints().await?;

        // 6. 创建预维护零活表
        //self.create_pre_maintained_flexible_works_table().await?;

        info!("数据库初始化完成");
        Ok(())
    }

    /// 初始化admin账户
    async fn initialize_admin_account(&self) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        // 检查是否已存在admin账户
        let check_sql = "SELECT COUNT(*) FROM person WHERE cpsn_num = @P1";
        let mut check_query = Query::new(check_sql);
        check_query.bind("admin");

        let check_stream = check_query.query(&mut *client).await?;
        let check_rows: Vec<_> = check_stream.into_first_result().await?;
        let admin_exists = check_rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0) > 0;

        if admin_exists {
            info!("Admin账户已存在，跳过创建");
            return Ok(());
        }

        // 创建admin账户
        let password_hash = hash_password("admin123")?;
        
        let insert_sql = r#"
            INSERT INTO person (
                cpsn_num, cPsn_Name, rEmployState, rSex, cDept_num,
                username, password_hash, status,
                is_admin, created_at, updated_at
            ) VALUES (
                @P1, @P2, @P3, @P4, @P5,
                @P6, @P7, @P8,
                @P9, GETDATE(), GETDATE()
            )
        "#;

        let mut insert_query = Query::new(insert_sql);
        insert_query.bind("admin");           // cpsn_num
        insert_query.bind("系统管理员");        // cPsn_Name
        insert_query.bind("1");               // rEmployState (在职)
        insert_query.bind("未知");             // rSex
        insert_query.bind("IT部门");           // cDept_num
        insert_query.bind("admin");           // username
        insert_query.bind(&password_hash);    // password_hash
        insert_query.bind(1);                 // status (启用)
        insert_query.bind(true);              // is_admin

        insert_query.execute(&mut *client).await?;
        info!("Admin账户创建成功: admin/admin123");

        Ok(())
    }

    /// 初始化基础角色
    async fn initialize_basic_roles(&self) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        let roles = vec![
            ("admin", "系统管理员", 1),
            ("manager", "经理", 2),
            ("team_leader", "组长", 3),
            ("user", "普通用户", 4),
        ];

        for (name, description, level) in roles {
            // 检查角色是否已存在
            let check_sql = "SELECT COUNT(*) FROM roles WHERE name = @P1";
            let mut check_query = Query::new(check_sql);
            check_query.bind(name);

            let check_stream = check_query.query(&mut *client).await?;
            let check_rows: Vec<_> = check_stream.into_first_result().await?;
            let role_exists = check_rows.first()
                .and_then(|row| row.get::<i32, _>(0))
                .unwrap_or(0) > 0;

            if !role_exists {
                let insert_sql = "INSERT INTO roles (name, description, level) VALUES (@P1, @P2, @P3)";
                let mut insert_query = Query::new(insert_sql);
                insert_query.bind(name);
                insert_query.bind(description);
                insert_query.bind(level);

                insert_query.execute(&mut *client).await?;
                info!("角色创建成功: {}", name);
            }
        }

        Ok(())
    }

    /// 初始化权限数据
    async fn initialize_permissions(&self) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        // 检查权限是否已初始化
        let check_sql = "SELECT COUNT(*) FROM permission_definitions";
        let check_query = Query::new(check_sql);
        let check_stream = check_query.query(&mut *client).await?;
        let check_rows: Vec<_> = check_stream.into_first_result().await?;
        let permission_count = check_rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0);

        if permission_count > 0 {
            info!("权限数据已存在，跳过初始化");
            return Ok(());
        }

        // 权限数据现在由数据库初始化脚本管理，不在代码中硬编码
        tracing::info!("权限数据由数据库脚本管理，跳过代码初始化");
        Ok(())
    }

    /// 分配admin角色给admin账户
    async fn assign_admin_role(&self) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        // 检查是否已分配
        let check_sql = "SELECT COUNT(*) FROM user_roles WHERE user_id = @P1";
        let mut check_query = Query::new(check_sql);
        check_query.bind("admin");

        let check_stream = check_query.query(&mut *client).await?;
        let check_rows: Vec<_> = check_stream.into_first_result().await?;
        let role_assigned = check_rows.first()
            .and_then(|row| row.get::<i32, _>(0))
            .unwrap_or(0) > 0;

        if role_assigned {
            info!("Admin角色已分配，跳过");
            return Ok(());
        }

        // 获取admin角色ID
        let get_role_sql = "SELECT id FROM roles WHERE name = @P1";
        let mut get_role_query = Query::new(get_role_sql);
        get_role_query.bind("admin");

        let role_stream = get_role_query.query(&mut *client).await?;
        let role_rows: Vec<_> = role_stream.into_first_result().await?;
        let admin_role_id = role_rows.first()
            .and_then(|row| row.get::<i64, _>(0))
            .ok_or_else(|| anyhow::anyhow!("未找到admin角色"))?;

        // 分配角色
        let assign_sql = "INSERT INTO user_roles (user_id, role_id) VALUES (@P1, @P2)";
        let mut assign_query = Query::new(assign_sql);
        assign_query.bind("admin");
        assign_query.bind(admin_role_id);

        assign_query.execute(&mut *client).await?;
        info!("Admin角色分配成功");

        Ok(())
    }

    /// 修复数据库约束
    async fn fix_database_constraints(&self) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        // 删除InstructionCards表的production_instruction_count CHECK约束
        // 这个约束不允许0值，但业务需求需要支持0值
        let drop_constraint_sql = r#"
            IF EXISTS (
                SELECT * FROM sys.check_constraints
                WHERE name = 'CK__Instructi__produ__634EBE90'
            )
            BEGIN
                ALTER TABLE InstructionCards DROP CONSTRAINT CK__Instructi__produ__634EBE90;
                PRINT '✅ 删除production_instruction_count CHECK约束成功';
            END
            ELSE
            BEGIN
                PRINT 'ℹ️ production_instruction_count CHECK约束不存在或已删除';
            END
        "#;

        let query = Query::new(drop_constraint_sql);
        query.execute(&mut *client).await?;
        info!("数据库约束修复完成");

        Ok(())
    }

    /// 创建预维护零活表
    async fn create_pre_maintained_flexible_works_table(&self) -> AppResult<()> {
        let mut client = self.db_config.get_app_connection().await?;

        let create_table_sql = r#"
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PreMaintainedFlexibleWorks' AND xtype='U')
            BEGIN
                CREATE TABLE PreMaintainedFlexibleWorks (
                    opcode VARCHAR(50) NOT NULL PRIMARY KEY,
                    description NVARCHAR(200) NOT NULL,
                    workcenter_id VARCHAR(50) NOT NULL
                );

                -- 创建索引
                CREATE INDEX IX_PreMaintainedFlexibleWorks_Workcenter
                    ON PreMaintainedFlexibleWorks(workcenter_id);

                PRINT '✅ 预维护零活表创建成功';
            END
            ELSE
            BEGIN
                PRINT 'ℹ️ 预维护零活表已存在';
            END
        "#;

        let query = Query::new(create_table_sql);
        query.execute(&mut *client).await?;
        info!("预维护零活表初始化完成");

        Ok(())
    }
}
