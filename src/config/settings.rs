use serde::{Deserialize, Serialize};
// Duration 已移至其他模块使用


#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Settings {
    pub server: ServerConfig,
    pub database: DatabaseConfig,
    pub jwt: JwtConfig,
    pub security: SecurityConfig,
    pub admin: AdminConfig,
    pub cors: CorsConfig,
    pub rate_limit: RateLimitConfig,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ServerConfig {
    pub host: String,
    pub port: u16,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub url: String,
    pub source_database_name: String,
    pub max_connections: u32,
    pub min_connections: u32,
    pub acquire_timeout: u64,
    pub idle_timeout: u64,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct JwtConfig {
    pub secret: String,
    pub expires_in: String,
    pub refresh_expires_in: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SecurityConfig {
    pub enable_permission_check: bool,
    pub enable_rate_limit: bool,
    pub enable_cors: bool,
}



#[derive(Debug, <PERSON><PERSON>, <PERSON>ialize, Deserialize)]
pub struct AdminConfig {
    pub username: String,
    pub password: String,
    pub email: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CorsConfig {
    pub allowed_origins: Vec<String>,
    pub allowed_methods: Vec<String>,
    pub allowed_headers: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitConfig {
    pub requests_per_minute: u32,
    pub burst: u32,
}

impl Settings {
    pub fn new() -> Result<Self, config::ConfigError> {
        // 智能加载 .env 文件 - 开发环境和发布环境使用不同策略
        Self::load_env_file_smart();

        let settings = config::Config::builder()
            .add_source(config::Environment::default())
            .build()?;

        let server = ServerConfig {
            host: settings.get_string("SERVER_HOST").unwrap_or_else(|_| "0.0.0.0".to_string()),
            port: settings.get_int("SERVER_PORT").unwrap_or(8080) as u16,
        };

        let database = DatabaseConfig {
            url: settings.get_string("DATABASE_URL")?,
            source_database_name: settings.get_string("SOURCE_DATABASE_NAME").unwrap_or_else(|_| "zlkdata".to_string()),
            max_connections: settings.get_int("DB_MAX_CONNECTIONS").unwrap_or(20) as u32,
            min_connections: settings.get_int("DB_MIN_CONNECTIONS").unwrap_or(5) as u32,
            acquire_timeout: settings.get_int("DB_CONNECTION_TIMEOUT").unwrap_or(30) as u64,
            idle_timeout: settings.get_int("DB_IDLE_TIMEOUT").unwrap_or(600) as u64,
        };

        let jwt = JwtConfig {
            secret: settings.get_string("JWT_SECRET")?,
            expires_in: settings.get_string("JWT_EXPIRES_IN").unwrap_or_else(|_| "24h".to_string()),
            refresh_expires_in: settings.get_string("JWT_REFRESH_EXPIRES_IN").unwrap_or_else(|_| "7d".to_string()),
        };

        let security = SecurityConfig {
            enable_permission_check: Self::get_permission_check_setting(&settings),
            enable_rate_limit: settings.get_bool("ENABLE_RATE_LIMIT").unwrap_or(true),
            enable_cors: settings.get_bool("ENABLE_CORS").unwrap_or(true),
        };



        let admin = AdminConfig {
            username: settings.get_string("ADMIN_USERNAME").unwrap_or_else(|_| "admin".to_string()),
            password: settings.get_string("ADMIN_PASSWORD").unwrap_or_else(|_| "Admin123!".to_string()),
            email: settings.get_string("ADMIN_EMAIL").unwrap_or_else(|_| "<EMAIL>".to_string()),
        };

        let cors = CorsConfig {
            allowed_origins: settings
                .get_string("CORS_ALLOWED_ORIGINS")
                .unwrap_or_else(|_| "http://localhost:3000".to_string())
                .split(',')
                .map(|s| s.trim().to_string())
                .collect(),
            allowed_methods: settings
                .get_string("CORS_ALLOWED_METHODS")
                .unwrap_or_else(|_| "GET,POST,PUT,DELETE,OPTIONS".to_string())
                .split(',')
                .map(|s| s.trim().to_string())
                .collect(),
            allowed_headers: settings
                .get_string("CORS_ALLOWED_HEADERS")
                .unwrap_or_else(|_| "Content-Type,Authorization".to_string())
                .split(',')
                .map(|s| s.trim().to_string())
                .collect(),
        };

        let rate_limit = RateLimitConfig {
            requests_per_minute: settings.get_int("RATE_LIMIT_REQUESTS_PER_MINUTE").unwrap_or(60) as u32,
            burst: settings.get_int("RATE_LIMIT_BURST").unwrap_or(10) as u32,
        };

        Ok(Settings {
            server,
            database,
            jwt,
            security,
            admin,
            cors,
            rate_limit,
        })
    }

    /// 安全的权限检查设置获取
    /// 只有明确的生产环境才强制启用权限检查，其他情况遵循配置
    fn get_permission_check_setting(settings: &config::Config) -> bool {
        // 检查环境类型
        let env = std::env::var("RUST_ENV").unwrap_or_else(|_| "development".to_string());
        let is_production = env.to_lowercase() == "production";

        if is_production {
            // 只有明确的生产环境才强制启用权限检查
            tracing::warn!("🔒 生产环境检测到，强制启用权限检查 (环境: {})", env);
            true
        } else {
            // 开发环境和测试环境：遵循配置文件设置
            let setting = settings.get_bool("ENABLE_PERMISSION_CHECK").unwrap_or(true);
            if !setting {
                tracing::warn!("⚠️  权限检查已禁用，请确保这是预期行为 (环境: {})", env);
            } else {
                tracing::info!("✅ 权限检查已启用 (环境: {})", env);
            }
            setting
        }
    }

    /// 智能加载环境变量文件
    /// 开发环境：从项目根目录加载.env文件
    /// 发布环境：从exe同级目录加载.env文件
    fn load_env_file_smart() {
        // 检测是否在开发环境中（通过检查Cargo.toml文件是否存在）
        let is_dev_env = std::path::Path::new("Cargo.toml").exists();

        if is_dev_env {
            // 开发环境：使用原有方式从当前工作目录加载
            if let Err(e) = dotenvy::dotenv() {
                eprintln!("Warning: Could not load .env file: {}", e);
            }
        } else {
            // 发布环境：优先从exe同级目录加载
            let mut loaded = false;

            // 尝试从exe同级目录加载
            if let Ok(exe_path) = std::env::current_exe() {
                if let Some(exe_dir) = exe_path.parent() {
                    let env_file = exe_dir.join(".env");
                    if env_file.exists() {
                        if let Ok(_) = dotenvy::from_path(&env_file) {
                            loaded = true;
                        }
                    }
                }
            }

            // 如果exe同级目录没有.env文件，尝试从当前工作目录加载
            if !loaded {
                if let Err(e) = dotenvy::dotenv() {
                    eprintln!("Warning: Could not load .env file: {}", e);
                }
            }
        }
    }
}


