use bb8::Pool;
use bb8_tiberius::ConnectionManager;
use tiberius::{Config, AuthMethod, Client};
use tokio::net::TcpStream;
use tokio_util::compat::TokioAsyncWriteCompatExt;
use anyhow::Result;

pub type SqlPool = Pool<ConnectionManager>;
pub type SqlConnection<'a> = bb8::PooledConnection<'a, ConnectionManager>;

#[derive(Debu<PERSON>, Clone)]
pub struct DatabaseConfig {
    pub app_pool: SqlPool,
    pub source_database_name: String,
}

impl DatabaseConfig {
    pub async fn new(app_url: &str, source_database_name: &str, max_connections: u32, acquire_timeout: u64) -> Result<Self> {
        tracing::info!("正在创建数据库连接池...");
        let app_pool = Self::create_app_pool(app_url, max_connections, acquire_timeout).await?;

        tracing::info!("数据库连接池创建成功，源数据库名称: {}", source_database_name);

        Ok(Self {
            app_pool,
            source_database_name: source_database_name.to_string(),
        })
    }

    // 创建应用数据库连接池（需要确保数据库存在）
    async fn create_app_pool(connection_string: &str, max_connections: u32, acquire_timeout: u64) -> Result<SqlPool> {
        // 解析连接字符串
        let url = url::Url::parse(connection_string)?;
        let host = url.host_str().unwrap_or("localhost");
        let port = url.port().unwrap_or(1433);
        let username = url.username();
        let password = url.password().unwrap_or("");
        let database = url.path().trim_start_matches('/');

        // 对于应用数据库，确保数据库存在
        if !database.is_empty() {
            Self::ensure_database_exists(host, port, username, password, database).await?;
        }

        // 创建连接配置
        let mut config = Config::new();
        config.host(host);
        config.port(port);
        config.authentication(AuthMethod::sql_server(username, password));
        if !database.is_empty() {
            config.database(database);
        }
        config.trust_cert();

        // 设置字符编码以支持中文
        config.application_name("u8_extend");

        // 创建连接管理器
        let manager = ConnectionManager::new(config);

        // 创建连接池 - 优化配置以避免连接超时问题和请求拥塞
        let pool = Pool::builder()
            .max_size(max_connections)
            .min_idle(Some(std::cmp::min(max_connections / 4, 5))) // 动态设置最小空闲连接数
            .max_lifetime(Some(std::time::Duration::from_secs(3600))) // 1小时，避免长时间连接导致的问题
            .idle_timeout(Some(std::time::Duration::from_secs(900))) // 15分钟空闲超时，更积极地回收连接
            .connection_timeout(std::time::Duration::from_secs(acquire_timeout)) // 使用配置的超时时间
            .test_on_check_out(true) // 获取连接时测试连接有效性
            .retry_connection(true) // 启用连接重试
            .build(manager)
            .await?;

        Ok(pool)
    }



    async fn ensure_database_exists(host: &str, port: u16, username: &str, password: &str, database: &str) -> Result<()> {
        let mut master_config = Config::new();
        master_config.host(host);
        master_config.port(port);
        master_config.authentication(AuthMethod::sql_server(username, password));
        master_config.database("master");
        master_config.trust_cert();

        let tcp = TcpStream::connect(master_config.get_addr()).await?;
        tcp.set_nodelay(true)?;
        let mut client = Client::connect(master_config, tcp.compat_write()).await?;

        let create_db_sql = format!(
            "IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = '{}') CREATE DATABASE [{}]",
            database, database
        );
        tracing::info!("尝试创建数据库: {}", database);
        let _ = client.simple_query(&create_db_sql).await;

        Ok(())
    }

    pub async fn get_app_connection(&self) -> Result<SqlConnection<'_>> {
        // 只在连接池状态异常时记录日志，避免正常情况下的日志噪音
        let pool_state = self.app_pool.state();
        if pool_state.connections == 0 || pool_state.idle_connections == 0 {
            tracing::warn!("连接池状态异常 - 连接数: {}, 空闲连接: {}",
                          pool_state.connections, pool_state.idle_connections);
        }

        self.app_pool.get().await.map_err(|e| {
            tracing::error!("获取应用数据库连接失败: {}", e);
            anyhow::anyhow!("获取应用数据库连接失败: {}", e)
        })
    }

    pub async fn get_source_connection(&self) -> Result<SqlConnection<'_>> {
        // 现在使用同一个连接池，通过完全限定名称访问源数据库
        // 只在连接池状态异常时记录日志，避免正常情况下的日志噪音
        let pool_state = self.app_pool.state();
        if pool_state.connections == 0 || pool_state.idle_connections == 0 {
            tracing::warn!("获取源数据库连接 - 连接池状态异常: 连接数: {}, 空闲连接: {}",
                          pool_state.connections, pool_state.idle_connections);
        }

        self.app_pool.get().await.map_err(|e| {
            tracing::error!("获取源数据库连接失败: {}", e);
            anyhow::anyhow!("获取数据库连接失败: {}", e)
        })
    }

    pub fn get_source_database_name(&self) -> &str {
        &self.source_database_name
    }

    pub async fn health_check(&self) -> Result<()> {
        // 检查数据库连接
        {
            let mut client = self.get_app_connection().await?;
            client.simple_query("SELECT 1").await
                .map_err(|e| anyhow::anyhow!("数据库连接失败: {}", e))?;
        }

        // 检查源数据库访问（可选）
        match self.get_source_connection().await {
            Ok(mut client) => {
                let check_sql = format!("SELECT COUNT(*) FROM {}.INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'", self.source_database_name);
                match client.simple_query(&check_sql).await {
                    Ok(_) => {
                        tracing::info!("源数据库 {} 访问正常", self.source_database_name);
                    }
                    Err(e) => {
                        tracing::warn!("源数据库 {} 访问失败: {}", self.source_database_name, e);
                    }
                }
            }
            Err(e) => {
                tracing::warn!("源数据库连接检查失败: {}", e);
            }
        }

        Ok(())
    }


}
