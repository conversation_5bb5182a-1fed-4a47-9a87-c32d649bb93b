# U8 权限管理系统 - 项目Wiki

## 📋 项目概述

U8 权限管理系统是一个基于 Rust 开发的现代化权限管理解决方案，专为企业级应用设计。系统提供完整的用户管理、角色权限控制和数据同步功能。

### 核心价值
- **安全性**: 基于JWT的无状态认证，确保系统安全
- **性能**: Rust语言的高性能特性，支持高并发访问
- **可扩展**: 模块化设计，易于扩展和维护
- **企业级**: 支持SQL Server，满足企业级需求

## 🏗️ 系统架构

### 技术栈
- **后端框架**: Axum (异步Web框架)
- **数据库**: SQL Server 2019+
- **连接池**: bb8 (异步连接池)
- **认证**: JWT (JSON Web Token)
- **ORM**: Tiberius (SQL Server驱动)
- **日志**: Tracing
- **配置**: Serde + TOML

### 模块结构
```
├── 认证模块 (Auth)
│   ├── JWT Token生成/验证
│   ├── 用户登录/登出
│   └── 权限验证
├── 用户管理模块 (User)
│   ├── 用户CRUD操作
│   ├── 用户信息查询
│   └── 用户状态管理
├── 角色管理模块 (Role)
│   ├── 角色CRUD操作
│   ├── 权限配置
│   └── 角色分配
└── 数据同步模块 (Sync)
    ├── 源数据库同步
    ├── 用户状态同步
    └── 同步日志记录
```

## 🗄️ 数据库设计

### 核心表结构

#### users (用户表)
- `id`: 主键，自增
- `username`: 用户名，唯一
- `password_hash`: 密码哈希
- `nickname`: 昵称
- `employee_id`: 员工编号
- `email`: 邮箱
- `phone`: 电话
- `department`: 部门
- `position`: 职位
- `status`: 状态 (1:启用, 0:禁用)
- `is_admin`: 是否管理员
- `last_login_at`: 最后登录时间
- `created_at`: 创建时间
- `updated_at`: 更新时间

#### roles (角色表)
- `id`: 主键，自增
- `name`: 角色名称，唯一
- `description`: 角色描述
- `permissions`: 权限配置 (JSON)
- `is_system`: 是否系统角色
- `created_at`: 创建时间
- `updated_at`: 更新时间

#### user_roles (用户角色关联表)
- `id`: 主键，自增
- `user_id`: 用户ID
- `role_id`: 角色ID
- `created_at`: 创建时间

#### sync_logs (同步日志表)
- `id`: 主键，自增
- `sync_type`: 同步类型
- `status`: 同步状态
- `message`: 同步消息
- `details`: 详细信息 (JSON)
- `created_at`: 创建时间

## 🔐 权限系统设计

### 权限模型
采用基于角色的访问控制 (RBAC) 模型：
- **用户 (User)**: 系统的使用者
- **角色 (Role)**: 权限的集合
- **权限 (Permission)**: 对资源的操作权限

### 🆕 统一权限管理架构
系统采用统一的权限管理架构，支持系统权限和自定义权限：

#### permission_definitions (权限定义表)
- `id`: 主键，自增
- `resource_name`: 权限资源名称，唯一
- `resource_label`: 权限资源显示名称
- `description`: 权限描述
- `category`: 权限分类
- `icon`: 权限图标
- `actions`: 权限操作列表 (JSON)
- `is_system`: 是否系统权限 (1:系统权限, 0:自定义权限)
- `is_active`: 是否启用
- `created_at`: 创建时间
- `updated_at`: 更新时间

### 权限配置格式
```json
{
  "permissions": [
    {
      "resource": "users",
      "actions": ["read", "create", "update", "delete"]
    },
    {
      "resource": "inventory",
      "actions": ["read", "update", "transfer"]
    }
  ],
  "role_level": 2
}
```

### 系统权限资源
- **users**: 用户管理 - 管理系统用户
- **roles**: 角色管理 - 管理系统角色和权限
- **role_assignment**: 角色分配 - 分配和管理用户角色
- **sync**: 数据同步 - 管理数据同步功能
- **reports**: 报表管理 - 管理系统报表和统计
- **settings**: 系统设置 - 管理系统配置和参数
- **logs**: 日志管理 - 管理系统日志和审计

### 自定义权限功能
- **动态创建**: 支持运行时创建业务相关的权限资源
- **灵活配置**: 每个权限资源可配置多个操作
- **分类管理**: 权限按类别组织，便于管理
- **实时生效**: 创建后立即在权限系统中生效

### 🛡️ 角色权限体系
系统采用严格的角色层级管理，确保权限安全：

#### 核心原则
- ✅ **admin是唯一的最高权限角色** - 不存在super_admin角色
- ✅ **admin用户只能有一个角色** - 防止权限混乱
- ✅ **角色权限保护机制** - 核心权限不可删除
- ✅ **统一权限验证** - 系统权限和自定义权限统一管理

#### 默认角色模板
| 角色 | 级别 | 权限范围 | 描述 |
|------|------|----------|------|
| **admin** | 1 | `*:*` | 超级管理员，拥有所有权限 |
| **manager** | 3 | users:read, inventory:*, production:* | 负责人，管理业务 |
| **team_leader** | 3 | users:read, roles:read, role_assignment:user | 班长，管理普通用户 |
| **user** | 4 | profile:read,write | 普通用户，个人信息管理 |

#### 权限保护机制
- **核心权限保护**: 系统核心权限不可删除或修改
- **角色分配限制**: 用户只能分配低于或等于自己权限级别的角色
- **权限验证**: 所有操作都经过严格的权限验证
- **审计日志**: 记录所有权限相关的操作

## 🔄 数据同步机制

### 同步架构
- **统一数据库连接**: 使用单一数据库连接池连接到SQL Server实例
- **应用数据库**: u8_extend数据库，系统自动创建和管理所有表结构
- **源数据库**: u8数据库（生产环境），只读访问，通过完全限定名称访问视图
- **连接池管理**: 使用bb8连接池确保高性能和稳定性

### 视图配置
系统支持可配置的视图读取，通过完全限定名称访问源数据库：
```toml
# config/sync_views.toml
[views]
user_view = "v_users"              # 用户视图名称（必需）
department_view = "v_departments"  # 部门视图名称（可选）
position_view = "v_positions"      # 职位视图名称（可选）
```

实际访问时会自动添加数据库前缀，例如：`u8.dbo.v_users`

### 同步流程
1. **连接数据库**: 使用统一的数据库连接池
2. **视图检查**: 验证源数据库中配置的视图是否存在（使用完全限定名称）
3. **数据提取**: 从源数据库视图提取用户信息（如：u8.dbo.v_users）
4. **数据转换**: 将源数据转换为系统格式
5. **数据加载**: 将转换后的数据写入应用数据库（u8_extend）
6. **日志记录**: 记录同步过程和结果

### 同步策略
- **增量同步**: 只同步变更的数据
- **全量同步**: 同步所有数据
- **定时同步**: 按计划自动执行同步
- **手动同步**: 管理员手动触发同步
- **视图驱动**: 基于数据库视图的灵活数据源

## 🛡️ 安全特性

### 认证安全
- **密码加密**: 使用bcrypt算法加密存储密码
- **JWT Token**: 无状态的身份验证机制
- **Token过期**: 设置合理的Token过期时间
- **刷新机制**: 支持Token刷新，提升用户体验

### 授权安全
- **权限验证**: 每个API请求都进行权限验证
- **角色隔离**: 不同角色只能访问授权的资源
- **操作审计**: 记录关键操作的审计日志

### 数据安全
- **SQL注入防护**: 使用参数化查询防止SQL注入
- **数据验证**: 严格的输入数据验证
- **错误处理**: 安全的错误信息返回

## 📊 性能优化

### 数据库优化
- **bb8连接池**: 使用异步连接池管理数据库连接
  - 连接复用和自动回收
  - 连接健康检查和自动重连
  - 可配置的连接超时和生命周期
  - 解决"No more packets in the wire"等连接问题
- **索引优化**: 为常用查询字段建立索引
- **分页查询**: 大数据量查询使用分页
- **查询优化**: 优化SQL查询语句

### 应用优化
- **异步处理**: 使用Rust的异步特性提升性能
- **内存管理**: Rust的零成本抽象和内存安全
- **缓存策略**: 合理使用缓存减少数据库访问
- **并发控制**: 支持高并发请求处理

## 🔧 配置管理

### 环境配置
- **开发环境**: 本地开发配置
- **测试环境**: 测试环境配置
- **生产环境**: 生产环境配置

### 配置项说明
- **数据库配置**: 连接字符串、连接池大小等
- **视图配置**: 源数据库视图名称配置
- **JWT配置**: 密钥、过期时间等
- **服务器配置**: 端口、CORS等
- **日志配置**: 日志级别、输出格式等

### 关键环境变量
```env
# 数据库连接（运维配置）
DATABASE_URL=mssql://sa:password@localhost:1433/u8_extend
SOURCE_DATABASE_NAME=u8

# JWT配置（运维配置）
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=24h

# 服务器配置（运维配置）
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
```

### 视图配置文件
视图配置由开发人员在 `config/sync_views.toml` 中预配置，运维人员无需修改：

```toml
# 数据同步视图配置
[views]
user_view = "v_users"              # 用户视图名称（必需）
department_view = "v_departments"  # 部门视图名称（可选）
position_view = "v_positions"      # 职位视图名称（可选）

[view_fields.user]
# 用户视图字段映射
employee_id = "employee_id"
username = "username"
nickname = "nickname"
email = "email"
phone = "phone"
department = "department"
position = "position"
work_center = "work_center"
status = "status"
updated_at = "updated_at"

[sync_rules]
# 同步规则配置
user_status_filter = "status = 1"  # 只同步启用的用户
default_password = "123456"        # 新用户默认密码
default_role = "user"              # 新用户默认角色
```

## 🧪 测试策略

### 测试类型
- **单元测试**: 测试单个函数或模块
- **集成测试**: 测试模块间的集成
- **API测试**: 测试HTTP接口
- **性能测试**: 测试系统性能

### 测试覆盖
- **功能覆盖**: 覆盖所有核心功能
- **边界测试**: 测试边界条件
- **异常测试**: 测试异常情况处理
- **安全测试**: 测试安全漏洞

## 📈 监控和运维

### 日志管理
- **结构化日志**: 使用结构化格式记录日志
- **日志级别**: 合理设置日志级别
- **日志轮转**: 防止日志文件过大
- **日志分析**: 支持日志分析和查询

### 健康检查
- **服务健康**: 检查服务运行状态
- **数据库健康**: 检查数据库连接状态
- **依赖检查**: 检查外部依赖状态

### 性能监控
- **响应时间**: 监控API响应时间
- **吞吐量**: 监控系统吞吐量
- **错误率**: 监控系统错误率
- **资源使用**: 监控CPU、内存使用情况

## 🚀 部署指南

### 部署方式
- **Docker部署**: 容器化部署
- **二进制部署**: 直接部署可执行文件
- **云平台部署**: 部署到云平台

### 配置分离原则
- **开发人员负责**: 视图名称、字段映射、同步规则（config/sync_views.toml）
- **运维人员负责**: 数据库连接、JWT密钥、服务器配置（环境变量）
- **数据库工程师负责**: 创建源数据库视图，确保字段名称与配置匹配

### 部署检查清单
- [ ] 环境变量配置（运维）
- [ ] 视图配置文件检查（开发）
- [ ] 数据库连接测试
- [ ] 源数据库视图验证（DBA）
- [ ] 防火墙配置
- [ ] SSL证书配置
- [ ] 日志目录权限
- [ ] 健康检查配置
- [ ] 监控配置
- [ ] 备份策略

## 📚 API文档

### 🔑 认证接口

#### 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "Admin123!"
}
```

**响应示例:**
```json
{
  "code": 200,
  "data": {
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": {
      "id": 1,
      "username": "admin",
      "nickname": "管理员",
      "is_admin": true
    }
  },
  "message": "登录成功"
}
```

### 👥 用户管理接口

#### 获取用户列表
```http
GET /api/users?page=1&page_size=10
Authorization: Bearer {token}
```

#### 创建用户
```http
POST /api/users
Authorization: Bearer {token}
Content-Type: application/json

{
  "username": "newuser",
  "password": "password123",
  "nickname": "新用户",
  "email": "<EMAIL>",
  "phone": "13800138000",
  "department": "技术部",
  "position": "开发工程师"
}
```

#### 更新用户
```http
PUT /api/users/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "nickname": "更新昵称",
  "email": "<EMAIL>",
  "department": "产品部"
}
```

#### 删除用户
```http
DELETE /api/users/{id}
Authorization: Bearer {token}
```

### 🛡️ 角色管理接口

#### 获取角色列表
```http
GET /api/roles
Authorization: Bearer {token}
```

#### 创建角色
```http
POST /api/roles
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "业务经理",
  "description": "负责业务管理的角色",
  "permissions": {
    "permissions": [
      {"resource": "users", "actions": ["read"]},
      {"resource": "inventory", "actions": ["read", "update"]}
    ],
    "role_level": 3
  }
}
```

#### 更新角色权限
```http
PUT /api/roles/{id}/permissions
Authorization: Bearer {token}
Content-Type: application/json

{
  "permissions": [
    {"resource": "users", "actions": ["read", "create"]},
    {"resource": "inventory", "actions": ["read", "update", "transfer"]}
  ],
  "role_level": 2
}
```

#### 分配用户角色
```http
POST /api/roles/{role_id}/users/{user_id}
Authorization: Bearer {token}
```

### 🔐 权限管理接口

#### 获取权限定义
```http
GET /api/permissions/definitions
Authorization: Bearer {token}
```

**响应示例:**
```json
{
  "code": 200,
  "data": {
    "resources": {
      "users": {
        "name": "用户管理",
        "description": "管理系统用户",
        "icon": "user",
        "category": "用户管理",
        "actions": [
          {"name": "read", "label": "查看用户", "description": "查看用户列表和详情"},
          {"name": "create", "label": "创建用户", "description": "创建新的用户账户"}
        ]
      }
    },
    "templates": {
      "admin": {
        "name": "超级管理员",
        "description": "拥有所有权限的超级管理员",
        "role_level": 1,
        "permissions": [{"resource": "*", "actions": ["*"]}]
      }
    },
    "categories": ["用户管理", "角色管理", "系统管理", "业务管理"]
  }
}
```

#### 获取权限树
```http
GET /api/permissions/tree
Authorization: Bearer {token}
```

#### 验证权限配置
```http
POST /api/permissions/validate
Authorization: Bearer {token}
Content-Type: application/json

{
  "permissions": [
    {"resource": "users", "actions": ["read", "create"]},
    {"resource": "inventory", "actions": ["read", "update"]}
  ],
  "role_level": 2
}
```

#### 检查用户权限
```http
POST /api/permissions/check
Authorization: Bearer {token}
Content-Type: application/json

{
  "user_id": 1,
  "resource": "users",
  "action": "read"
}
```

### 🎯 自定义权限接口

#### 获取自定义权限列表
```http
GET /api/permissions/custom/resources
Authorization: Bearer {token}
```

#### 创建自定义权限
```http
POST /api/permissions/custom/resources
Authorization: Bearer {token}
Content-Type: application/json

{
  "resource": "production",
  "label": "生产管理",
  "description": "管理生产计划和工艺流程",
  "category": "业务管理",
  "icon": "factory",
  "actions": [
    {"name": "read", "label": "查看生产", "description": "查看生产计划和进度"},
    {"name": "create", "label": "创建生产", "description": "创建生产计划"},
    {"name": "update", "label": "更新生产", "description": "更新生产状态"},
    {"name": "schedule", "label": "排产", "description": "安排生产计划"}
  ]
}
```

#### 删除自定义权限 - 按资源名称
```http
DELETE /api/permissions/custom/resources/{resource_name}
Authorization: Bearer {token}
```

#### 删除自定义权限 - 按ID
```http
DELETE /api/permissions/custom/resources/id/{id}
Authorization: Bearer {token}
```

**说明**: 系统提供两种删除方式：
- 按资源名称删除：适用于已知资源名称的场景
- 按ID删除：适用于前端通过列表操作的场景，解决了GET接口返回ID但删除需要resource_name的不一致问题

### 🔄 数据同步接口

#### 触发数据同步
```http
POST /api/sync/trigger
Authorization: Bearer {token}
Content-Type: application/json

{
  "sync_type": "users",
  "force": false
}
```

#### 获取同步状态
```http
GET /api/sync/status
Authorization: Bearer {token}
```

#### 获取同步日志
```http
GET /api/sync/logs?page=1&page_size=20
Authorization: Bearer {token}
```

### 📦 业务数据管理接口

#### 产品管理 (inventory表)
```http
# 获取产品列表（支持分页和模糊查询）
GET /api/products?page=1&page_size=20&cinvname=头料&keyword=P10
Authorization: Bearer {token}

# 获取单个产品详情
GET /api/products/{cinvcode}
Authorization: Bearer {token}
```

#### 工序管理 (operation表)
```http
# 获取工序列表（支持分页和模糊查询）
GET /api/operations?page=1&page_size=20&description=检验&keyword=头
Authorization: Bearer {token}

# 获取单个工序详情
GET /api/operations/{opcode}
Authorization: Bearer {token}
```

#### 设备管理 (EQ_QEQDataSel表)
```http
# 获取设备列表（支持分页和模糊查询）
GET /api/equipments?page=1&page_size=20&ceqname=车床&keyword=011
Authorization: Bearer {token}

# 获取单个设备详情
GET /api/equipments/{ceqcode}
Authorization: Bearer {token}
```

**查询参数说明:**
- `page`: 页码，默认1
- `page_size`: 每页大小，默认20，最大100
- `keyword`: 全字段模糊查询关键词
- 各表特定字段支持独立模糊查询

### 📊 响应格式

所有API响应都遵循统一格式：

**成功响应:**
```json
{
  "code": 200,
  "data": {...},
  "message": "操作成功",
  "timestamp": "2025-06-18T06:00:00Z"
}
```

**错误响应:**
```json
{
  "code": 400,
  "error": "参数错误",
  "message": "用户名不能为空",
  "timestamp": "2025-06-18T06:00:00Z"
}
```

### 🔒 认证说明

除登录接口外，所有API都需要在请求头中携带JWT Token：
```http
Authorization: Bearer {your_jwt_token}
```

Token有效期为24小时，过期后需要重新登录获取新Token。

## 🔧 重要修复记录

### Super_Admin角色自动创建问题修复 (2025-06-18)

#### 问题描述
- 系统在启动时自动创建`super_admin`角色，违反了"admin是唯一最高权限角色"的设计原则
- admin用户被分配多个角色，导致权限混乱

#### 修复措施
1. **移除super_admin角色创建逻辑** - 修改`src/config/database_init.rs`
2. **统一使用admin作为唯一最高权限角色** - 权限配置为`*:*`
3. **清理历史数据** - 删除多余的角色和角色分配
4. **建立防护机制** - 创建监控文档和检查脚本

#### 修复结果
- ✅ 只有4个标准角色：admin, manager, team_leader, user
- ✅ admin用户只有一个admin角色
- ✅ 权限体系清晰一致
- ✅ 自定义权限系统完美集成

### 自定义权限按ID删除接口 (2025-06-18)

#### 问题描述
- GET `/api/permissions/custom/resources` 返回数据包含`id`字段
- DELETE接口只支持按`resource_name`删除，前端使用不便

#### 解决方案
- 新增按ID删除接口：`DELETE /api/permissions/custom/resources/id/{id}`
- 保留原有按名称删除接口：`DELETE /api/permissions/custom/resources/{resource_name}`
- 完善错误处理和权限保护

### 数据库连接架构重构 (2025-06-19)

#### 问题描述
- 系统使用两个独立的数据库连接：DATABASE_URL 和 SOURCE_DATABASE_URL
- 用户需求是只使用一个数据库连接，在该连接下访问两个数据库

#### 修复措施
1. **重构数据库配置** - 移除 SOURCE_DATABASE_URL，添加 SOURCE_DATABASE_NAME
2. **统一连接池管理** - 使用单一连接池，通过完全限定名称访问不同数据库
3. **更新同步逻辑** - 修改用户导入和状态同步，使用 `u8.dbo.v_users` 格式访问源视图
4. **优化错误处理** - 源数据库不存在时优雅降级，不影响应用启动

#### 修复结果
- ✅ 使用单一数据库连接池连接到SQL Server实例
- ✅ 应用数据库：u8_extend（项目业务数据）
- ✅ 源数据库：u8（生产环境数据，通过完全限定名称访问）
- ✅ 保持所有现有功能正常工作
- ✅ 源数据库不存在时系统仍能正常启动和运行

## 📚 开发指南

### 代码规范
- **命名规范**: 使用清晰的命名
- **注释规范**: 添加必要的注释
- **错误处理**: 统一的错误处理机制
- **代码格式**: 使用rustfmt格式化代码

### 开发流程
1. **需求分析**: 明确功能需求
2. **设计方案**: 设计技术方案
3. **编码实现**: 实现功能代码
4. **单元测试**: 编写单元测试
5. **集成测试**: 进行集成测试
6. **代码审查**: 进行代码审查
7. **部署上线**: 部署到生产环境

### 扩展开发
- **新增API**: 添加新的API接口
- **新增功能**: 扩展系统功能
- **性能优化**: 优化系统性能
- **安全加固**: 增强系统安全性

## 🔮 未来规划

### 短期目标 (1-3个月)
- [x] bb8连接池实现 - 已完成
- [x] 视图配置功能 - 已完成
- [x] 源数据库只读架构 - 已完成
- [x] 统一权限管理架构 - 已完成
- [x] 自定义权限功能 - 已完成
- [x] 权限验证系统重构 - 已完成
- [x] 完善API文档 - 已完成
- [x] 修复super_admin角色自动创建问题 - 已完成 (2025-06-18)
- [x] 自定义权限按ID删除接口 - 已完成 (2025-06-18)
- [x] 权限系统架构优化 - 已完成 (2025-06-18)
- [x] 业务数据管理API - 已完成 (2025-06-28)
- [ ] 添加更多测试用例
- [ ] 安全加固

### 中期目标 (3-6个月)
- [ ] 支持更多数据库
- [ ] 添加缓存机制
- [ ] 实现微服务架构
- [ ] 添加监控面板

### 长期目标 (6-12个月)
- [ ] 支持多租户
- [ ] 实现分布式部署
- [ ] 添加AI功能
- [ ] 开源社区建设

---

## 🏭 班组管理系统

### 概述
班组管理系统是企业生产管理的核心模块，提供完整的班组创建、班组长任命、成员管理等功能，并与角色权限系统深度集成。

### 数据库设计

#### Teams (班组表)
```sql
CREATE TABLE Teams (
    TeamID int IDENTITY(1,1) PRIMARY KEY,
    TeamName nvarchar(100) NOT NULL,
    Workcenter_DeptCode nvarchar(50) NOT NULL,
    Description nvarchar(500),
    Status smallint NOT NULL DEFAULT 1,
    CreatedAt datetime2 NOT NULL DEFAULT GETDATE(),
    UpdatedAt datetime2 NOT NULL DEFAULT GETDATE(),
    CreatedBy nvarchar(50),
    CONSTRAINT FK_Teams_Workcenter FOREIGN KEY (Workcenter_DeptCode)
        REFERENCES workcenter(DeptCode),
    CONSTRAINT UQ_Teams_Name_Workcenter UNIQUE (TeamName, Workcenter_DeptCode)
);
```

#### TeamLeaders (班组长表)
```sql
CREATE TABLE TeamLeaders (
    LeaderID int IDENTITY(1,1) PRIMARY KEY,
    TeamID int NOT NULL,
    Leader_psn_num nvarchar(50) NOT NULL,
    StartDate date NOT NULL DEFAULT CAST(GETDATE() AS DATE),
    EndDate date,
    IsActive bit NOT NULL DEFAULT 1,
    CreatedAt datetime2 NOT NULL DEFAULT GETDATE(),
    CONSTRAINT FK_TeamLeaders_Team FOREIGN KEY (TeamID)
        REFERENCES Teams(TeamID) ON DELETE CASCADE,
    CONSTRAINT FK_TeamLeaders_Person FOREIGN KEY (Leader_psn_num)
        REFERENCES person(cpsn_num)
);
```

#### TeamMembers (班组成员表)
```sql
CREATE TABLE TeamMembers (
    MemberID int IDENTITY(1,1) PRIMARY KEY,
    TeamID int NOT NULL,
    Member_psn_num nvarchar(50) NOT NULL,
    JoinDate date NOT NULL DEFAULT CAST(GETDATE() AS DATE),
    LeaveDate date,
    IsActive bit NOT NULL DEFAULT 1,
    CreatedAt datetime2 NOT NULL DEFAULT GETDATE(),
    CONSTRAINT FK_TeamMembers_Team FOREIGN KEY (TeamID)
        REFERENCES Teams(TeamID) ON DELETE CASCADE,
    CONSTRAINT FK_TeamMembers_Person FOREIGN KEY (Member_psn_num)
        REFERENCES person(cpsn_num)
);
```

### 核心功能

#### 1. 班组管理
- **创建班组**: 支持按工作中心创建班组
- **更新班组**: 修改班组信息和状态
- **删除班组**: 级联删除相关班长和成员记录
- **查询班组**: 支持分页、筛选和排序

#### 2. 班组长管理
- **任命班组长**: 支持多个班长，带有生效时间
- **解除班组长**: 设置失效时间，保留历史记录
- **班长查询**: 获取班组的所有班长信息

#### 3. 班组成员管理
- **添加成员**: 支持单个和批量添加
- **移除成员**: 设置离开时间，保留历史记录
- **成员查询**: 获取班组的所有成员信息

### API 接口

#### 班组管理接口
```http
GET    /api/teams                     # 获取班组列表
POST   /api/teams                     # 创建班组
GET    /api/teams/:id                 # 获取班组详情
PUT    /api/teams/:id                 # 更新班组信息
DELETE /api/teams/:id                 # 删除班组
GET    /api/teams/by-workcenter/:code # 根据工作中心获取班组
```

#### 班组长管理接口
```http
GET    /api/teams/:id/leaders         # 获取班组长列表
POST   /api/teams/:id/leaders         # 任命班组长
DELETE /api/teams/:id/leaders/:psn    # 移除班组长
POST   /api/teams/:id/leaders/batch   # 批量任命班组长
```

#### 班组成员管理接口
```http
GET    /api/teams/:id/members         # 获取班组成员列表
POST   /api/teams/:id/members         # 添加班组成员
DELETE /api/teams/:id/members/:psn    # 移除班组成员
POST   /api/teams/:id/members/batch   # 批量添加班组成员
```

#### 统一管理接口
```http
POST   /api/teams/assign-leader       # 统一任命班组长
DELETE /api/teams/remove-leader/:psn  # 统一移除班组长
GET    /api/users/:psn/teams          # 获取用户班组信息
GET    /api/users/:psn/role-team-status # 获取角色班组协同状态
```

---

## 🔄 班组借用管理系统

### 概述
班组借用管理系统是企业生产管理的重要组成部分，支持跨班组人员借用，提供智能自动审核功能，基于工作中心dep字段实现自动化审批流程。

### 核心特性

#### 1. 智能自动审核
- **dep字段匹配**: 基于工作中心dep字段自动判断是否需要审核
- **同dep自动通过**: 相同dep字段的工作中心之间借用自动通过
- **不同dep需审核**: 不同dep字段的工作中心之间借用需要手动审核
- **权限控制**: 只有班长可以发起借用申请，只有原班组班长可以审核

#### 2. 完整借用流程
- **申请阶段**: 班长发起借用申请，系统自动检查dep字段
- **审核阶段**: 根据dep字段匹配结果自动通过或进入待审核状态
- **管理阶段**: 支持借入/借出成员查询，状态跟踪
- **历史记录**: 完整的借用历史和审核记录

### 数据库设计

#### TeamMemberBorrows (班组成员借用表)
```sql
CREATE TABLE TeamMemberBorrows (
    BorrowID int IDENTITY(1,1) PRIMARY KEY,
    OriginalTeamID int NOT NULL,              -- 原班组ID
    BorrowTeamID int NOT NULL,                -- 借用班组ID
    Member_psn_num nvarchar(50) NOT NULL,     -- 被借用人员编号
    StartDate datetime NOT NULL,              -- 借用开始时间
    EndDate datetime NOT NULL,                -- 借用结束时间
    BorrowStatus int NOT NULL DEFAULT 0,      -- 借用状态 (0:待审核, 1:已通过, 2:已拒绝, 3:已归还)
    ApprovedBy nvarchar(50) NULL,             -- 审批人
    ApprovedAt datetime NULL,                 -- 审批时间
    Remarks nvarchar(500) NULL,               -- 备注
    CreatedAt datetime DEFAULT GETDATE(),     -- 创建时间
    UpdatedAt datetime DEFAULT GETDATE(),     -- 更新时间

    CONSTRAINT FK_TMB_OriginalTeam FOREIGN KEY (OriginalTeamID)
        REFERENCES Teams(TeamID),
    CONSTRAINT FK_TMB_BorrowTeam FOREIGN KEY (BorrowTeamID)
        REFERENCES Teams(TeamID),
    CONSTRAINT FK_TMB_Member FOREIGN KEY (Member_psn_num)
        REFERENCES person(cpsn_num)
);
```

#### workcenter表dep字段
```sql
-- workcenter表结构（关键字段）
CREATE TABLE workcenter (
    DeptCode nvarchar(12) NULL,               -- 工作中心代码
    Description nvarchar(60) NULL,            -- 工作中心描述
    dep varchar(100) NULL                     -- 部门字段（用于自动审核）
);
```

### 自动审核逻辑

#### 审核规则
```rust
// 自动审核判断逻辑
async fn check_auto_approve_eligibility(
    &self,
    applicant_psn_num: &str,
    borrowed_person_psn_num: &str
) -> AppResult<bool> {
    // 1. 检查申请人是否为班长
    let is_leader = self.is_team_leader_by_psn(applicant_psn_num).await?;
    if !is_leader {
        return Ok(false);
    }

    // 2. 获取申请人和被借用人的工作中心dep字段
    let applicant_dep = self.get_person_workcenter_dep(applicant_psn_num).await?;
    let borrowed_dep = self.get_person_workcenter_dep(borrowed_person_psn_num).await?;

    // 3. 判断dep字段是否相同
    match (applicant_dep, borrowed_dep) {
        (Some(dep1), Some(dep2)) if dep1 == dep2 => Ok(true),  // 相同dep，自动通过
        _ => Ok(false),  // 不同dep或dep为空，需要手动审核
    }
}
```

#### 测试场景
| 申请人工作中心 | 被借用人工作中心 | dep字段 | 审核结果 |
|---------------|-----------------|---------|----------|
| WC_A001 | WC_A002 | 都是'DEP_A' | ✅ 自动通过 |
| WC_A001 | WC_B001 | 'DEP_A' vs 'DEP_B' | ⏳ 需要审核 |
| WC_A001 | WC_C001 | 'DEP_A' vs NULL | ⏳ 需要审核 |

### API 接口

#### 借用申请接口
```http
# 创建借用申请
POST /api/teams/:team_id/borrow-request
Authorization: Bearer {token}
Content-Type: application/json

{
    "member_psn_num": "MB001",           # 被借用人员编号
    "start_date": 1751472000,            # 借用开始时间（时间戳）
    "end_date": 1751479200,              # 借用结束时间（时间戳）
    "remarks": "生产任务需要"             # 借用原因
}

# 响应示例（自动通过）
{
    "code": 200,
    "message": "借用申请创建成功",
    "data": {
        "borrow_id": 4,
        "status": "Approved",            # 自动通过
        "auto_approved": true
    }
}

# 响应示例（需要审核）
{
    "code": 200,
    "message": "借用申请创建成功",
    "data": {
        "borrow_id": 5,
        "status": "Pending",             # 等待审核
        "auto_approved": false
    }
}
```

#### 借用审核接口
```http
# 审核借用申请（只有原班组班长可以审核）
PUT /api/teams/:team_id/borrow-request/:borrow_id/approve
Authorization: Bearer {token}
Content-Type: application/json

{
    "action": "approve",                 # approve: 通过, reject: 拒绝
    "remarks": "同意借用"                # 审核意见
}

# 响应示例
{
    "code": 200,
    "message": "借用申请审核完成",
    "data": {
        "borrow_id": 5,
        "status": "Approved",
        "approved_by": "LB001",
        "approved_at": "2025-07-02T16:00:00Z"
    }
}
```

#### 借用查询接口
```http
# 查询班组借用申请列表
GET /api/teams/:team_id/borrow-requests
Authorization: Bearer {token}

# 查询借入成员列表（被借来的成员）
GET /api/teams/:team_id/borrowed-in-members
Authorization: Bearer {token}

# 查询借出成员列表（被借走的成员）
GET /api/teams/:team_id/borrowed-out-members
Authorization: Bearer {token}
```

### 权限控制

#### 权限定义
```rust
// 借用相关权限
borrow:create    // 创建借用申请（仅班长）
borrow:approve   // 审核借用申请（仅原班组班长）
borrow:read      // 查看借用信息
borrow:manage    // 管理借用（管理员）
```

#### 权限验证
```rust
// 发起借用申请权限检查
if !auth_user.is_admin() {
    let is_leader = borrow_service.is_team_leader(team_id, &user_psn_num).await?;
    if !is_leader {
        return ApiResponse::error(403, "只有班长才能发起借用申请");
    }
}

// 审核借用申请权限检查
if !auth_user.is_admin() {
    let is_original_leader = borrow_service
        .is_original_team_leader(borrow_id, &user_psn_num).await?;
    if !is_original_leader {
        return ApiResponse::error(403, "只有原班组的班长才能审批借用申请");
    }
}
```

### 业务规则

#### 借用限制
1. **申请人限制**: 只有班长可以发起借用申请
2. **审核人限制**: 只有被借用人员的原班组班长可以审核
3. **时间限制**: 借用结束时间必须大于开始时间
4. **重复限制**: 同一人员不能有重叠时间的借用申请
5. **班组限制**: 不能借用本班组的人员

#### 状态管理
```rust
pub enum BorrowStatus {
    Pending = 0,     // 待审核
    Approved = 1,    // 已通过
    Rejected = 2,    // 已拒绝
    Returned = 3,    // 已归还（过期自动归还）
}
```

### 测试数据

#### 工作中心测试数据
```sql
-- 插入测试工作中心数据
INSERT INTO workcenter (DeptCode, Description, dep) VALUES
('WC_A001', '测试工作中心A1', 'DEP_A'),
('WC_A002', '测试工作中心A2', 'DEP_A'),
('WC_B001', '测试工作中心B1', 'DEP_B');
```

#### 测试用户数据
```sql
-- 创建测试用户
INSERT INTO users (username, password_hash, cpsn_num, name, dept_num) VALUES
('leader_a1', '$2b$12$...', 'LA001', '班长A1', 'WC_A001'),
('leader_b1', '$2b$12$...', 'LB001', '班长B1', 'WC_B001'),
('member_a1', '$2b$12$...', 'MA001', '成员A1', 'WC_A001'),
('member_b1', '$2b$12$...', 'MB001', '成员B1', 'WC_B001');
```

### 使用示例

#### Postman API集合
系统提供了完整的Postman API集合，位于 `postman/借用功能API集合_简化版.json`，包含：

1. **认证登录**: 班长A1和班长B1的登录接口
2. **借用申请**: 同dep字段（自动通过）和不同dep字段（需要审核）的测试用例
3. **借用审核**: 审核通过和拒绝的操作
4. **借用查询**: 申请列表、借入成员、借出成员的查询

#### 快速测试流程
1. 导入Postman集合文件
2. 设置环境变量 `base_url` 为 `http://localhost:8081`
3. 按顺序执行API请求，观察自动审核效果

#### 核心测试场景
```bash
# 场景1：同dep字段借用（自动通过）
# 班长A1（WC_A001, dep='DEP_A'）借用成员A3（WC_A002, dep='DEP_A'）
# 结果：状态为 "Approved"，自动通过

# 场景2：不同dep字段借用（需要审核）
# 班长A1（WC_A001, dep='DEP_A'）借用成员B1（WC_B001, dep='DEP_B'）
# 结果：状态为 "Pending"，需要班长B1手动审核

# 场景3：手动审核
# 班长B1审核借用申请，可以选择通过或拒绝
# 结果：状态变为 "Approved" 或 "Rejected"
```

---

## 🤖 智能角色分配系统

### 概述
智能角色分配系统是对传统角色管理的重大升级，特别针对 `team_leader` 角色提供智能化的分配和管理功能。

### 核心特性

#### 1. 自动工作中心检测
- 从 `person.cDept_num` 自动获取用户工作中心
- 查询该工作中心下的所有可用班组
- 提供智能推荐和自动分配选项

#### 2. 多种分配模式
- **精确指定模式**: 直接指定班组ID进行分配
- **智能自动模式**: 系统自动选择合适的班组
- **智能建议模式**: 返回可用班组列表供用户选择

#### 3. 角色与班组协同
- 任命班组长时自动分配 `team_leader` 角色
- 移除班组长时智能回收角色权限
- 实时状态检查和一致性验证

### API 接口详解

#### 原有接口（增强版）
```http
POST /api/users/{user_id}/roles/{role_id}
```
**行为变化**：
- 普通角色：保持原有行为不变
- team_leader 角色：增加智能提示和建议

#### 新增智能接口
```http
POST /api/roles/assign-with-options
Content-Type: application/json

{
    "user_id": "P001",           // 用户人员编号
    "role_id": 3,                // 角色ID
    "overwrite": true,           // 是否覆盖现有角色（默认true）
    "auto_assign_team": true,    // 是否自动分配到班组（可选）
    "target_team_id": 1          // 指定要分配的班组ID（可选）
}
```

### 参数使用指南

#### 参数关系
- `target_team_id` 和 `auto_assign_team` 是**互相独立**的参数
- `target_team_id` 优先级更高：如果指定了具体班组ID，系统直接分配到该班组
- `auto_assign_team` 仅在未指定 `target_team_id` 时生效
- 两个参数都不传：系统返回可用班组列表供用户选择

#### 使用场景

| 场景 | target_team_id | auto_assign_team | 说明 |
|------|----------------|------------------|------|
| **我知道具体班组** | ✅ 传班组ID | ❌ 不传 | 直接分配到指定班组 |
| **让系统自动选择** | ❌ 不传 | ✅ 传 true | 仅当只有一个班组时自动分配 |
| **我要看看有哪些班组** | ❌ 不传 | ❌ 不传 | 返回可用班组列表 |

#### 正确使用示例

**场景1：精确指定班组**
```json
{
    "user_id": "P001",
    "role_id": 3,
    "target_team_id": 1
    // 注意：不需要传 auto_assign_team
}
```

**场景2：智能自动分配**
```json
{
    "user_id": "P001",
    "role_id": 3,
    "auto_assign_team": true
    // 注意：不需要传 target_team_id
}
```

**场景3：获取智能建议**
```json
{
    "user_id": "P001",
    "role_id": 3
    // 注意：两个参数都不传
}
```

### 系统处理逻辑

```
智能角色分配流程:
1. 分配 team_leader 角色
2. 获取用户工作中心信息
3. 查询该工作中心下的可用班组
4. 根据参数执行不同策略:
   ├── 如果有 target_team_id
   │   ├── 验证班组是否属于用户工作中心
   │   └── 直接分配到指定班组
   ├── 如果有 auto_assign_team=true
   │   ├── 检查是否只有一个班组
   │   └── 自动分配或返回错误
   └── 否则
       └── 返回可用班组列表供选择
```

### 响应格式

#### 成功分配响应
```json
{
    "code": 200,
    "message": "成功分配 team_leader 角色并任命到班组：生产一班",
    "data": {
        "message": "team_leader 角色分配成功，并已自动任命到指定班组",
        "team_info": {
            "team_id": 1,
            "team_name": "生产一班",
            "workcenter_name": "生产车间一"
        },
        "auto_assigned": true
    }
}
```

#### 智能建议响应
```json
{
    "code": 200,
    "message": "team_leader 角色分配成功，请选择班组完成任命",
    "data": {
        "message": "team_leader 角色分配成功",
        "workcenter_info": {
            "dept_code": "001",
            "available_teams_count": 3
        },
        "available_teams": [
            {
                "team_id": 1,
                "team_name": "生产一班",
                "workcenter_dept_code": "001",
                "workcenter_name": "生产车间一",
                "description": "负责生产线A",
                "status": 1,
                "leader_count": 0,
                "member_count": 5
            }
        ],
        "suggestion": "请从可用班组中选择并使用统一任命接口完成班组任命",
        "unified_api": "POST /api/teams/assign-leader"
    }
}
```

### 最佳实践

#### 前端实现建议
1. **已知班组ID**: 使用 `target_team_id` 参数直接指定
2. **不确定班组**: 先不带参数调用，获取建议后再指定
3. **单一班组环境**: 使用 `auto_assign_team: true` 自动分配

#### 错误处理
- 根据响应结构判断是否需要进一步操作
- 优先使用精确指定模式，减少用户操作步骤
- 提供清晰的错误提示和操作建议

#### 调试技巧
- 查看响应中的 `auto_assigned` 字段判断是否自动分配成功
- 利用 `available_teams` 字段为用户提供选择界面
- 关注 `suggestion` 字段中的操作建议

---

## 📚 相关文档

### API 文档
- `docs/intelligent_role_assignment_api.md` - 智能角色分配完整API文档
- `docs/parameter_usage_examples.md` - 参数使用示例和最佳实践
- `docs/team_role_coordination.md` - 班组与角色协同机制说明
- `docs/unified_team_leader_api.md` - 统一班组长管理API文档

### 实现文档
- `docs/team_leader_role_implementation_plan.md` - 实现计划和技术细节

### 数据库文档
- 班组管理相关表结构和约束
- 角色权限表设计和关联关系
- 数据一致性和完整性保证

---

## 🔧 技术实现细节

### 权限控制
班组管理和智能角色分配系统集成了完整的权限控制机制：

#### 权限定义
```rust
// 班组管理权限
teams:read     // 查看班组信息
teams:create   // 创建班组
teams:update   // 更新班组信息
teams:delete   // 删除班组
teams:manage   // 管理班组成员和班长

// 角色管理权限
roles:read     // 查看角色信息
roles:assign   // 分配角色
roles:remove   // 移除角色
```

#### 权限检查
```rust
// 示例：检查班组管理权限
if !check_permission(&auth_user, "teams", "manage", &db_config).await {
    return ApiResponse::forbidden("权限不足，需要 teams:manage 权限");
}
```

### 数据验证

#### 业务规则验证
1. **工作中心验证**: 确保班组属于有效的工作中心
2. **人员验证**: 确保人员存在于 person 表
3. **重复检查**: 防止重复任命班组长或添加成员
4. **状态验证**: 确保班组和人员状态有效

#### 数据完整性
```sql
-- 外键约束确保数据一致性
CONSTRAINT FK_Teams_Workcenter FOREIGN KEY (Workcenter_DeptCode)
    REFERENCES workcenter(DeptCode)

CONSTRAINT FK_TeamLeaders_Person FOREIGN KEY (Leader_psn_num)
    REFERENCES person(cpsn_num)
```

### 事务处理

#### 角色与班组协同事务
```rust
// 统一任命班组长的事务处理
async fn unified_assign_leader(&self, request: &UnifiedAssignLeaderRequest) -> AppResult<()> {
    let mut client = self.db_config.get_app_connection().await?;

    // 开始事务
    // 1. 验证数据有效性
    // 2. 插入班长记录
    // 3. 检查并分配角色
    // 4. 记录操作日志
    // 如果任何步骤失败，自动回滚
}
```

### 性能优化

#### 查询优化
1. **索引设计**: 在常用查询字段上建立索引
2. **分页查询**: 支持大数据量的分页处理
3. **连接优化**: 使用 LEFT JOIN 优化关联查询

#### 缓存策略
```rust
// 工作中心信息缓存
// 角色权限缓存
// 用户班组关系缓存
```

### 错误处理

#### 统一错误类型
```rust
pub enum AppError {
    Database(String),      // 数据库错误
    Authentication(String), // 认证错误
    Permission(String),    // 权限错误
    Validation(String),    // 验证错误
    Business(String),      // 业务逻辑错误
}
```

#### 错误响应格式
```json
{
    "code": 400,
    "message": "指定的班组不存在或不属于该工作中心",
    "data": null
}
```

### 日志记录

#### 操作日志
```rust
tracing::info!(
    "统一任命：为用户 {} (ID: {}) 分配了 team_leader 角色并任命为班组 {} 的班长",
    request.leader_psn_num, user_id, request.team_id
);
```

#### 审计日志
- 记录所有角色分配和撤销操作
- 记录班组长任命和解除操作
- 便于追踪权限变更历史

---

## 🧪 测试指南

### 单元测试
```rust
#[tokio::test]
async fn test_assign_team_leader() {
    // 测试班组长任命功能
}

#[tokio::test]
async fn test_intelligent_role_assignment() {
    // 测试智能角色分配功能
}
```

### 集成测试
```bash
# 测试完整的班组管理流程
curl -X POST "/api/teams" -d '{"team_name": "测试班组", ...}'
curl -X POST "/api/teams/assign-leader" -d '{"team_id": 1, ...}'
curl -X GET "/api/users/P001/role-team-status"
```

### API 测试示例

#### 测试智能角色分配
```bash
# 1. 测试智能建议模式
curl -X POST "/api/roles/assign-with-options" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "P001",
    "role_id": 3
  }'

# 2. 测试精确指定模式
curl -X POST "/api/roles/assign-with-options" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "P001",
    "role_id": 3,
    "target_team_id": 1
  }'

# 3. 测试自动分配模式
curl -X POST "/api/roles/assign-with-options" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "P001",
    "role_id": 3,
    "auto_assign_team": true
  }'
```

### 测试数据准备
```sql
-- 创建测试工作中心
INSERT INTO workcenter (DeptCode, Description)
VALUES ('TEST001', '测试车间一');

-- 创建测试班组
INSERT INTO Teams (TeamName, Workcenter_DeptCode, Description, Status, CreatedBy)
VALUES ('测试班组', 'TEST001', '用于测试的班组', 1, 'admin');

-- 更新测试用户工作中心
UPDATE person SET cDept_num = 'TEST001'
WHERE cpsn_num IN ('P001', 'P002', 'P003');
```

---

## 🚀 部署指南

### 环境要求
- Rust 1.70+
- SQL Server 2019+
- 内存: 最低 2GB，推荐 4GB+
- CPU: 最低 2核，推荐 4核+

### 配置文件
```toml
# config.toml
[database]
server = "*************"
port = 1433
database = "zlkdata"
username = "sa"
password = "your_password"
trust_cert = true

[server]
host = "0.0.0.0"
port = 8081

[jwt]
secret = "your_jwt_secret"
expiration_hours = 24
```

### 启动命令
```bash
# 开发环境
cargo run --bin u8_extend

# 生产环境
cargo build --release
./target/release/u8_extend
```

### 健康检查
```bash
# 检查服务状态
curl http://localhost:8081/health

# 检查数据库连接
curl http://localhost:8081/api/health/db
```

---

## 📈 监控和维护

### 关键指标
- API 响应时间
- 数据库连接池状态
- 角色分配成功率
- 班组管理操作频率

### 日志监控
```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
grep "ERROR" logs/app.log

# 查看角色分配日志
grep "角色分配" logs/app.log
```

### 数据库维护
```sql
-- 检查班组数据一致性
SELECT t.TeamID, t.TeamName,
       COUNT(tl.LeaderID) as LeaderCount,
       COUNT(tm.MemberID) as MemberCount
FROM Teams t
LEFT JOIN TeamLeaders tl ON t.TeamID = tl.TeamID AND tl.IsActive = 1
LEFT JOIN TeamMembers tm ON t.TeamID = tm.TeamID AND tm.IsActive = 1
GROUP BY t.TeamID, t.TeamName;

-- 检查角色分配一致性
SELECT ur.user_id, r.role_name,
       COUNT(tl.LeaderID) as TeamLeaderCount
FROM user_roles ur
INNER JOIN roles r ON ur.role_id = r.role_id
LEFT JOIN person hp ON ur.user_id = hp.iUserID
LEFT JOIN TeamLeaders tl ON hp.cpsn_num = tl.Leader_psn_num AND tl.IsActive = 1
WHERE r.role_name = 'team_leader'
GROUP BY ur.user_id, r.role_name;
```
