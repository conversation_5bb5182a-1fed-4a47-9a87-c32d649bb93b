#!/bin/bash

# U8扩展系统 - Windows 构建脚本
# 用于在 macOS/Linux 上交叉编译 Windows 二进制文件
#
# 用法:
#   ./build-windows.sh          # 增量构建（推荐）
#   ./build-windows.sh --clean  # 完全重新构建

set -e

# 解析命令行参数
CLEAN_BUILD=false
for arg in "$@"; do
    case $arg in
        --clean|-c)
            CLEAN_BUILD=true
            shift
            ;;
        --help|-h)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --clean, -c    执行完全重新构建（清理缓存）"
            echo "  --help, -h     显示此帮助信息"
            exit 0
            ;;
        *)
            echo "未知参数: $arg"
            echo "使用 --help 查看帮助"
            exit 1
            ;;
    esac
done

echo "🚀 开始构建 Windows 版本..."
if [ "$CLEAN_BUILD" = true ]; then
    echo "🧹 模式: 完全重新构建"
else
    echo "⚡ 模式: 增量构建（更快）"
fi

# 检查是否安装了 Windows 目标
if ! rustup target list --installed | grep -q "x86_64-pc-windows-msvc"; then
    echo "📦 安装 Windows 目标..."
    rustup target add x86_64-pc-windows-msvc
fi

# 设置构建目标
TARGET="x86_64-pc-windows-msvc"
BINARY_NAME="u8_extend"
BUILD_DIR="target/${TARGET}/release"
DIST_DIR="dist/windows"

# 根据参数决定是否清理
if [ "$CLEAN_BUILD" = true ]; then
    echo "🔧 清理之前的构建缓存..."
    cargo clean --target ${TARGET}
    echo "  ✓ 已清理目标构建缓存"
else
    echo "⚡ 跳过清理，使用增量构建..."
    echo "  💡 提示: 如需完全重新构建，请使用 --clean 参数"
fi

echo "🏗️  开始编译 (目标: ${TARGET})..."
cargo build --release --target ${TARGET} --bin ${BINARY_NAME}

echo "📁 创建分发目录..."
mkdir -p ${DIST_DIR}

echo "📋 复制文件到分发目录..."
# 复制二进制文件
cp ${BUILD_DIR}/${BINARY_NAME}.exe ${DIST_DIR}/

# 复制配置文件模板
echo "📋 复制配置文件..."
if [ -f ".env.example" ]; then
    cp .env.example ${DIST_DIR}/
    echo "  ✓ 复制 .env.example"
fi
if [ -f ".env.testing" ]; then
    cp .env.testing ${DIST_DIR}/
    echo "  ✓ 复制 .env.testing"
fi
if [ -f ".env.production" ]; then
    cp .env.production ${DIST_DIR}/
    echo "  ✓ 复制 .env.production"
fi

# 始终使用.env.testing作为发布包的默认配置
# 这样不会影响开发者的本地.env文件
cp .env.testing ${DIST_DIR}/.env
echo "📋 使用 .env.testing 作为发布包默认配置文件 (.env)"

# 创建启动脚本
cat > ${DIST_DIR}/start.bat << 'EOF'
@echo off
chcp 65001 >nul
title U8扩展系统
echo ========================================
echo    U8扩展系统 - Windows版本
echo ========================================
echo.

REM 检查是否存在 .env 文件
if not exist .env (
    echo [警告] 未找到 .env 配置文件！
    echo.
    echo 请选择配置文件：
    echo   1. 测试环境 - 复制 .env.testing 为 .env
    echo   2. 生产环境 - 复制 .env.production 为 .env
    echo   3. 自定义配置 - 复制 .env.example 为 .env
    echo.
    set /p choice="请输入选择 (1-3): "

    if "%choice%"=="1" (
        copy .env.testing .env >nul
        echo [信息] 已使用测试环境配置
    ) else if "%choice%"=="2" (
        copy .env.production .env >nul
        echo [信息] 已使用生产环境配置
        echo [警告] 请修改 .env 文件中的数据库连接信息！
    ) else if "%choice%"=="3" (
        copy .env.example .env >nul
        echo [信息] 已创建自定义配置文件
        echo [警告] 请修改 .env 文件中的配置信息！
    ) else (
        echo [错误] 无效选择！
        pause
        exit /b 1
    )
    echo.
)

REM 显示配置信息
echo [信息] 正在启动服务器...
echo [信息] 配置文件: .env
echo [信息] 日志输出: 控制台
echo.

REM 启动应用程序
u8_extend.exe

REM 如果程序异常退出，暂停以查看错误信息
if %ERRORLEVEL% neq 0 (
    echo.
    echo [错误] 程序异常退出，错误代码: %ERRORLEVEL%
    echo.
    echo 常见问题解决方案：
    echo   1. 检查数据库连接配置
    echo   2. 确认数据库服务是否启动
    echo   3. 验证端口是否被占用
    echo   4. 查看上方的错误信息
    echo.
    pause
)
EOF

# 创建安装说明
cat > ${DIST_DIR}/README.md << 'EOF'
# U8扩展系统 - Windows 部署包

## 部署步骤

### 1. 环境准备
- Windows Server 2016 或更高版本
- SQL Server 2016 或更高版本
- 确保防火墙允许 8081 端口访问

### 2. 配置数据库
1. 根据环境选择配置文件：
   - 测试环境：复制 `.env.testing` 为 `.env`
   - 生产环境：复制 `.env.production` 为 `.env`
   - 自定义环境：复制 `.env.example` 为 `.env`
2. 修改 `.env` 文件中的数据库连接信息：
   ```
   DATABASE_URL=mssql://用户名:密码@服务器地址:端口/数据库名
   ```

### 3. 启动应用
1. 双击 `start.bat` 启动应用
2. 或在命令行中运行 `u8_extend.exe`

### 4. 验证部署
- 访问 `http://服务器IP:8081/health` 检查服务状态
- 使用默认管理员账户登录：
  - 用户名: admin
  - 密码: admin123

### 5. 生产环境配置
- 修改 JWT_SECRET 为强密钥
- 设置合适的 CORS_ORIGINS
- 配置日志级别为 warn 或 error
- 启用 HTTPS (建议使用反向代理)

## 故障排除

### 常见问题
1. **数据库连接失败**
   - 检查 SQL Server 是否启动
   - 验证连接字符串是否正确
   - 确认用户权限

2. **端口被占用**
   - 修改 `.env` 中的 `SERVER_PORT`
   - 或停止占用端口的程序

3. **权限问题**
   - 确保应用有读写权限
   - 检查防火墙设置

### 日志查看
应用日志会输出到控制台，生产环境建议：
- 使用 Windows 服务运行
- 配置日志文件输出
- 设置日志轮转

## 技术支持
如有问题，请联系技术支持团队。
EOF

# 创建配置检查脚本
cat > ${DIST_DIR}/check-config.bat << 'EOF'
@echo off
chcp 65001 >nul
title U8扩展系统 - 配置检查
echo ========================================
echo    U8扩展系统 - 配置检查工具
echo ========================================
echo.

REM 检查配置文件
if not exist .env (
    echo [错误] 未找到 .env 配置文件
    echo 请运行 start.bat 或手动创建配置文件
    goto :end
)

echo [信息] 正在检查配置文件...
echo.

REM 显示关键配置项
for /f "tokens=1,2 delims==" %%a in (.env) do (
    if "%%a"=="DATABASE_URL" echo [配置] 数据库连接: %%b
    if "%%a"=="SERVER_PORT" echo [配置] 服务端口: %%b
    if "%%a"=="JWT_SECRET" echo [配置] JWT密钥: [已设置]
    if "%%a"=="APP_ENV" echo [配置] 运行环境: %%b
)

echo.
echo [信息] 配置检查完成
echo.

:end
pause
EOF

# 复制Windows控制台stdin劫持解决方案脚本
echo "📋 复制Windows控制台解决方案脚本..."
if [ -f "dist/windows/start-detached.bat" ]; then
    echo "  ✓ start-detached.bat (方案7: 分离控制台启动)"
fi
if [ -f "dist/windows/download-nssm.bat" ]; then
    echo "  ✓ download-nssm.bat (NSSM下载器)"
fi
if [ -f "dist/windows/install-nssm-service.bat" ]; then
    echo "  ✓ install-nssm-service.bat (方案8: NSSM服务安装)"
fi
if [ -f "dist/windows/manage-nssm-service.bat" ]; then
    echo "  ✓ manage-nssm-service.bat (NSSM服务管理)"
fi

echo "✅ 构建完成！"
echo ""
echo "📦 分发包位置: ${DIST_DIR}/"
echo "📋 包含文件:"
ls -la ${DIST_DIR}/
echo ""
echo "🚀 部署说明:"
echo "1. 将 ${DIST_DIR} 目录复制到 Windows 服务器"
echo "2. 配置 .env 文件"
echo "3. 选择运行方式："
echo "   - 临时测试: 运行 start.bat"
echo "   - 生产环境: 运行 install-service.bat 安装为 Windows 服务"
echo "   - 服务管理: 运行 manage-service.bat 进行服务管理"
echo "   - 重新部署: 运行 redeploy.bat 进行自动化更新"
echo ""
echo "💡 构建优化提示:"
if [ "$CLEAN_BUILD" = true ]; then
    echo "   - 下次可使用 './build-windows.sh' 进行更快的增量构建"
else
    echo "   - 如遇到编译问题，可使用 './build-windows.sh --clean' 完全重新构建"
fi
echo "   - 增量构建通常比完全重新构建快 3-10 倍"
echo "   - 只有在依赖更新或遇到缓存问题时才需要 --clean"
echo ""
echo "✨ 构建完成！"
