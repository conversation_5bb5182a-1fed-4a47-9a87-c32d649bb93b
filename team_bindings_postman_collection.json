{"info": {"name": "班组绑定管理 API", "description": "班组绑定功能的完整API接口文档，包括产品、设备、工序绑定管理", "version": "1.0.0"}, "item": [{"name": "班组产品绑定", "item": [{"name": "创建班组产品绑定", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"team_id\": 49,\n  \"inventory_id\": \"TEST001\"\n}"}, "url": {"raw": "{{base_url}}/api/team-bindings/products", "host": ["{{base_url}}"], "path": ["api", "team-bindings", "products"]}, "description": "为指定班组创建产品绑定关系"}}, {"name": "批量创建班组产品绑定", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"team_id\": 48,\n  \"inventory_ids\": [\"PROD001\", \"PROD002\", \"PROD003\"]\n}"}, "url": {"raw": "{{base_url}}/api/team-bindings/products/batch", "host": ["{{base_url}}"], "path": ["api", "team-bindings", "products", "batch"]}, "description": "批量为指定班组创建多个产品绑定关系"}}, {"name": "获取班组产品绑定列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/team-bindings/products?team_id=49&page=1&page_size=20&status=1", "host": ["{{base_url}}"], "path": ["api", "team-bindings", "products"], "query": [{"key": "team_id", "value": "49", "description": "班组ID（可选）"}, {"key": "page", "value": "1", "description": "页码，默认1"}, {"key": "page_size", "value": "20", "description": "每页数量，默认20"}, {"key": "status", "value": "1", "description": "状态筛选（可选）：1-启用，0-禁用"}, {"key": "keyword", "value": "", "description": "关键字搜索（可选）", "disabled": true}]}, "description": "查询班组产品绑定列表，支持分页和筛选"}}, {"name": "删除班组产品绑定", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/team-bindings/products/1", "host": ["{{base_url}}"], "path": ["api", "team-bindings", "products", "1"]}, "description": "删除指定的班组产品绑定关系"}}, {"name": "更新班组产品绑定状态", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"status\": 0\n}"}, "url": {"raw": "{{base_url}}/api/team-bindings/products/1/status", "host": ["{{base_url}}"], "path": ["api", "team-bindings", "products", "1", "status"]}, "description": "更新班组产品绑定的状态（启用/禁用）"}}]}, {"name": "班组设备绑定", "item": [{"name": "创建班组设备绑定", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"team_id\": 49,\n  \"equipment_id\": \"EQ001\"\n}"}, "url": {"raw": "{{base_url}}/api/team-bindings/equipments", "host": ["{{base_url}}"], "path": ["api", "team-bindings", "equipments"]}, "description": "为指定班组创建设备绑定关系"}}, {"name": "批量创建班组设备绑定", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"team_id\": 48,\n  \"equipment_ids\": [\"EQ001\", \"EQ002\", \"EQ003\"]\n}"}, "url": {"raw": "{{base_url}}/api/team-bindings/equipments/batch", "host": ["{{base_url}}"], "path": ["api", "team-bindings", "equipments", "batch"]}, "description": "批量为指定班组创建多个设备绑定关系"}}, {"name": "获取班组设备绑定列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/team-bindings/equipments?team_id=49&page=1&page_size=20&status=1", "host": ["{{base_url}}"], "path": ["api", "team-bindings", "equipments"], "query": [{"key": "team_id", "value": "49", "description": "班组ID（可选）"}, {"key": "page", "value": "1", "description": "页码，默认1"}, {"key": "page_size", "value": "20", "description": "每页数量，默认20"}, {"key": "status", "value": "1", "description": "状态筛选（可选）：1-启用，0-禁用"}, {"key": "keyword", "value": "", "description": "关键字搜索（可选）", "disabled": true}]}, "description": "查询班组设备绑定列表，支持分页和筛选"}}, {"name": "删除班组设备绑定", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/team-bindings/equipments/1", "host": ["{{base_url}}"], "path": ["api", "team-bindings", "equipments", "1"]}, "description": "删除指定的班组设备绑定关系"}}, {"name": "更新班组设备绑定状态", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"status\": 0\n}"}, "url": {"raw": "{{base_url}}/api/team-bindings/equipments/1/status", "host": ["{{base_url}}"], "path": ["api", "team-bindings", "equipments", "1", "status"]}, "description": "更新班组设备绑定的状态（启用/禁用）"}}]}, {"name": "班组工序绑定", "item": [{"name": "创建班组工序绑定", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"team_id\": 49,\n  \"operation_id\": \"OP001\"\n}"}, "url": {"raw": "{{base_url}}/api/team-bindings/operations", "host": ["{{base_url}}"], "path": ["api", "team-bindings", "operations"]}, "description": "为指定班组创建工序绑定关系"}}, {"name": "批量创建班组工序绑定", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"team_id\": 48,\n  \"operation_ids\": [\"OP001\", \"OP002\", \"OP003\"]\n}"}, "url": {"raw": "{{base_url}}/api/team-bindings/operations/batch", "host": ["{{base_url}}"], "path": ["api", "team-bindings", "operations", "batch"]}, "description": "批量为指定班组创建多个工序绑定关系"}}, {"name": "获取班组工序绑定列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/team-bindings/operations?team_id=49&page=1&page_size=20&status=1", "host": ["{{base_url}}"], "path": ["api", "team-bindings", "operations"], "query": [{"key": "team_id", "value": "49", "description": "班组ID（可选）"}, {"key": "page", "value": "1", "description": "页码，默认1"}, {"key": "page_size", "value": "20", "description": "每页数量，默认20"}, {"key": "status", "value": "1", "description": "状态筛选（可选）：1-启用，0-禁用"}, {"key": "keyword", "value": "", "description": "关键字搜索（可选）", "disabled": true}]}, "description": "查询班组工序绑定列表，支持分页和筛选"}}, {"name": "删除班组工序绑定", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/team-bindings/operations/1", "host": ["{{base_url}}"], "path": ["api", "team-bindings", "operations", "1"]}, "description": "删除指定的班组工序绑定关系"}}, {"name": "更新班组工序绑定状态", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"status\": 0\n}"}, "url": {"raw": "{{base_url}}/api/team-bindings/operations/1/status", "host": ["{{base_url}}"], "path": ["api", "team-bindings", "operations", "1", "status"]}, "description": "更新班组工序绑定的状态（启用/禁用）"}}]}, {"name": "班组资源查询", "item": [{"name": "获取班组所有可用资源", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/teams/49/available-resources", "host": ["{{base_url}}"], "path": ["api", "teams", "49", "available-resources"]}, "description": "获取指定班组的所有可用资源（产品、设备、工序），用于指令卡创建时的资源选择"}}, {"name": "获取班组可用产品列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/teams/49/available-products", "host": ["{{base_url}}"], "path": ["api", "teams", "49", "available-products"]}, "description": "获取指定班组的可用产品列表"}}, {"name": "获取班组可用设备列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/teams/49/available-equipments", "host": ["{{base_url}}"], "path": ["api", "teams", "49", "available-equipments"]}, "description": "获取指定班组的可用设备列表"}}, {"name": "获取班组可用工序列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/teams/49/available-operations", "host": ["{{base_url}}"], "path": ["api", "teams", "49", "available-operations"]}, "description": "获取指定班组的可用工序列表"}}, {"name": "验证班组资源可用性", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/teams/49/validate-resources/TEST001/EQ001/OP001", "host": ["{{base_url}}"], "path": ["api", "teams", "49", "validate-resources", "TEST001", "EQ001", "OP001"]}, "description": "验证指定班组是否可以使用指定的产品、设备、工序组合"}}]}], "variable": [{"key": "base_url", "value": "http://localhost:8081", "type": "string"}, {"key": "token", "value": "", "type": "string", "description": "JWT认证令牌，通过登录接口获取"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// 如果没有token，可以先调用登录接口获取", "// pm.sendRequest({", "//     url: pm.variables.get('base_url') + '/api/auth/login',", "//     method: 'POST',", "//     header: {'Content-Type': 'application/json'},", "//     body: {", "//         mode: 'raw',", "//         raw: JSON.stringify({username: 'admin', password: '1234'})", "//     }", "// }, function(err, res) {", "//     if (!err && res.json().data.token) {", "//         pm.variables.set('token', res.json().data.token);", "//     }", "// });"]}}]}