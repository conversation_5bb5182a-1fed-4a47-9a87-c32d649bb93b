{"info": {"name": "U8扩展系统 - 借用功能API", "description": "班组成员借用管理功能的API接口文档\n\n## 功能概述\n借用功能允许班组之间临时借用人员，支持完整的申请、审批、查询流程。\n\n## 核心功能 ⭐\n**B班组班长查看借用来的人员** - 显示姓名、到期时间、原班组等关键信息\n\n## 使用流程\n1. B班组申请借用A班组的人员\n2. A班组班长审批借用申请\n3. 审批通过后，B班组能查看借用来的人员，A班组能查看被借走的人员\n\n## 环境变量设置\n- `base_url`: 服务器地址（默认：http://localhost:8081）\n- `auth_token`: 认证令牌（通过登录接口获取）\n\n## 认证说明\n所有API都需要Bearer Token认证，请先调用登录接口获取token。", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:8081", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}], "item": [{"name": "1. 发起借用申请", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/teams/:team_id/borrow-request", "host": ["{{base_url}}"], "path": ["api", "teams", ":team_id", "borrow-request"], "variable": [{"key": "team_id", "value": "3", "description": "借用班组ID（申请借用人员的班组）"}]}, "body": {"mode": "raw", "raw": "{\n  \"member_psn_num\": \"P006\",\n  \"start_date\": 1751190200,\n  \"end_date\": 1751276600,\n  \"remarks\": \"临时支援生产任务\"\n}"}, "description": "班组申请借用其他班组的成员\n\n**请求参数说明：**\n- `team_id`: 借用班组ID（URL路径参数）\n- `member_psn_num`: 被借用人员编号\n- `start_date`: 借用开始时间（时间戳，秒）\n- `end_date`: 借用结束时间（时间戳，秒）\n- `remarks`: 借用备注\n\n**业务规则：**\n- 借用开始时间不能早于当前时间\n- 被借用人员必须存在且属于某个班组\n- 不能重复申请借用同一人员"}, "response": [{"name": "成功响应", "originalRequest": {"method": "POST", "url": {"raw": "{{base_url}}/api/teams/3/borrow-request"}}, "status": "OK", "code": 200, "body": "{\n  \"code\": 200,\n  \"message\": \"借用申请创建成功\",\n  \"data\": {\n    \"borrow_id\": 1\n  },\n  \"timestamp\": 1751190153\n}"}]}, {"name": "2. 审批借用申请", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/teams/:team_id/borrow-request/:borrow_id/approve", "host": ["{{base_url}}"], "path": ["api", "teams", ":team_id", "borrow-request", ":borrow_id", "approve"], "variable": [{"key": "team_id", "value": "4", "description": "原班组ID（被借用人员所属班组）"}, {"key": "borrow_id", "value": "1", "description": "借用申请ID"}]}, "body": {"mode": "raw", "raw": "{\n  \"action\": \"approve\",\n  \"remarks\": \"同意借用，支援生产任务\"\n}"}, "description": "原班组班长审批借用申请\n\n**请求参数说明：**\n- `team_id`: 原班组ID（URL路径参数）\n- `borrow_id`: 借用申请ID（URL路径参数）\n- `action`: 审批动作（\"approve\"=同意，\"reject\"=拒绝）\n- `remarks`: 审批备注\n\n**权限要求：**\n- 只有原班组的班长才能审批借用申请\n- 申请状态必须是待审批（Pending）"}, "response": [{"name": "审批成功", "originalRequest": {"method": "PUT", "url": {"raw": "{{base_url}}/api/teams/4/borrow-request/1/approve"}}, "status": "OK", "code": 200, "body": "{\n  \"code\": 200,\n  \"message\": \"借用申请同意成功\",\n  \"data\": null,\n  \"timestamp\": 1751190177\n}"}]}, {"name": "3. 查询借用申请列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/teams/:team_id/borrow-requests?type=outgoing&status=1&page=1&page_size=20", "host": ["{{base_url}}"], "path": ["api", "teams", ":team_id", "borrow-requests"], "query": [{"key": "type", "value": "outgoing", "description": "申请类型：outgoing=发出的申请，incoming=收到的申请"}, {"key": "status", "value": "1", "description": "申请状态：0=待审批，1=已同意，2=已拒绝"}, {"key": "page", "value": "1", "description": "页码"}, {"key": "page_size", "value": "20", "description": "每页数量"}], "variable": [{"key": "team_id", "value": "3", "description": "班组ID"}]}, "description": "查询班组的借用申请列表\n\n**查询参数说明：**\n- `type`: 申请类型（可选）\n  - `outgoing`: 发出的申请（本班组申请借用其他班组人员）\n  - `incoming`: 收到的申请（其他班组申请借用本班组人员）\n- `status`: 申请状态（可选）\n  - `0`: 待审批\n  - `1`: 已同意\n  - `2`: 已拒绝\n- `page`: 页码（默认1）\n- `page_size`: 每页数量（默认20）"}, "response": [{"name": "成功响应", "originalRequest": {"method": "GET", "url": {"raw": "{{base_url}}/api/teams/3/borrow-requests"}}, "status": "OK", "code": 200, "body": "{\n  \"code\": 200,\n  \"message\": \"获取借用申请列表成功\",\n  \"data\": {\n    \"requests\": [\n      {\n        \"borrow_id\": 1,\n        \"member_psn_num\": \"P006\",\n        \"member_name\": \"张三\",\n        \"original_team_id\": 4,\n        \"original_team_name\": \"生产二班\",\n        \"borrow_team_id\": 3,\n        \"borrow_team_name\": \"生产一班\",\n        \"borrow_status\": \"Approved\",\n        \"start_date\": 1751188900,\n        \"end_date\": 1751275300,\n        \"requested_by\": \"admin\",\n        \"requested_by_name\": \"系统管理员\",\n        \"approved_by\": \"P002\",\n        \"approved_by_name\": \"李四\",\n        \"requested_at\": 1751188893,\n        \"approved_at\": 1751188950,\n        \"remarks\": \"同意借用，支援生产任务\"\n      }\n    ],\n    \"total\": 1,\n    \"page\": 1,\n    \"page_size\": 20,\n    \"total_pages\": 1\n  },\n  \"timestamp\": 1751190221\n}"}]}, {"name": "4. 查询借用来的人员 ⭐", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/teams/:team_id/borrowed-in-members", "host": ["{{base_url}}"], "path": ["api", "teams", ":team_id", "borrowed-in-members"], "variable": [{"key": "team_id", "value": "3", "description": "班组ID（查看借用来人员的班组）"}]}, "description": "B班组班长查看借用来的人员 - 核心功能 ⭐\n\n**功能说明：**\n- B班组班长能看到从其他班组借用来的人员\n- 显示人员姓名、到期时间、原班组等关键信息\n- 支持多人员同时借用管理\n- 只显示当前借用中的人员（已审批且未到期）\n\n**返回数据包含：**\n- 人员姓名（member_name）\n- 到期时间（borrow_end_date，时间戳格式）\n- 原班组信息（original_team_name）\n- 借用状态（is_borrowed_in: true）"}, "response": [{"name": "成功响应 - 多人员借用", "originalRequest": {"method": "GET", "url": {"raw": "{{base_url}}/api/teams/3/borrowed-in-members"}}, "status": "OK", "code": 200, "body": "{\n  \"code\": 200,\n  \"message\": \"获取借用人员列表成功\",\n  \"data\": [\n    {\n      \"team_id\": 3,\n      \"member_psn_num\": \"P007\",\n      \"member_name\": \"王五\",\n      \"joined_at\": 1751190200,\n      \"status\": 1,\n      \"borrow_status\": {\n        \"is_borrowed_out\": false,\n        \"is_borrowed_in\": true,\n        \"borrow_team_id\": null,\n        \"borrow_team_name\": null,\n        \"original_team_id\": 4,\n        \"original_team_name\": \"生产二班\",\n        \"borrow_end_date\": 1751276600\n      }\n    },\n    {\n      \"team_id\": 3,\n      \"member_psn_num\": \"P006\",\n      \"member_name\": \"张三\",\n      \"joined_at\": 1751188900,\n      \"status\": 1,\n      \"borrow_status\": {\n        \"is_borrowed_out\": false,\n        \"is_borrowed_in\": true,\n        \"borrow_team_id\": null,\n        \"borrow_team_name\": null,\n        \"original_team_id\": 4,\n        \"original_team_name\": \"生产二班\",\n        \"borrow_end_date\": 1751275300\n      }\n    }\n  ],\n  \"timestamp\": 1751190200\n}"}]}, {"name": "5. 查询被借走的人员", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/api/teams/:team_id/borrowed-out-members", "host": ["{{base_url}}"], "path": ["api", "teams", ":team_id", "borrowed-out-members"], "variable": [{"key": "team_id", "value": "4", "description": "班组ID（查看被借走人员的班组）"}]}, "description": "A班组班长查看被借走的人员\n\n**功能说明：**\n- A班组班长能看到被借用到其他班组的人员\n- 显示人员姓名、借用班组、到期时间等信息\n- 支持多人员被借走管理\n- 只显示当前被借走的人员（已审批且未到期）\n\n**返回数据包含：**\n- 人员姓名（member_name）\n- 借用班组（borrow_team_name）\n- 到期时间（borrow_end_date，时间戳格式）\n- 借用状态（is_borrowed_out: true）"}, "response": [{"name": "成功响应 - 多人员被借走", "originalRequest": {"method": "GET", "url": {"raw": "{{base_url}}/api/teams/4/borrowed-out-members"}}, "status": "OK", "code": 200, "body": "{\n  \"code\": 200,\n  \"message\": \"获取被借走人员列表成功\",\n  \"data\": [\n    {\n      \"team_id\": 4,\n      \"member_psn_num\": \"P007\",\n      \"member_name\": \"王五\",\n      \"joined_at\": 1751188830,\n      \"status\": 1,\n      \"borrow_status\": {\n        \"is_borrowed_out\": true,\n        \"is_borrowed_in\": false,\n        \"borrow_team_id\": 3,\n        \"borrow_team_name\": \"生产一班\",\n        \"original_team_id\": null,\n        \"original_team_name\": null,\n        \"borrow_end_date\": 1751276600\n      }\n    },\n    {\n      \"team_id\": 4,\n      \"member_psn_num\": \"P006\",\n      \"member_name\": \"张三\",\n      \"joined_at\": 1751188819,\n      \"status\": 1,\n      \"borrow_status\": {\n        \"is_borrowed_out\": true,\n        \"is_borrowed_in\": false,\n        \"borrow_team_id\": 3,\n        \"borrow_team_name\": \"生产一班\",\n        \"original_team_id\": null,\n        \"original_team_name\": null,\n        \"borrow_end_date\": 1751275300\n      }\n    }\n  ],\n  \"timestamp\": 1751190210\n}"}]}]}