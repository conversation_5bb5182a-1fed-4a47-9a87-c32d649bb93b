# U8扩展系统 - 借用功能API文档

## 功能概述

借用功能允许班组之间临时借用人员，支持完整的申请、审批、查询流程。

### 核心功能 ⭐
**B班组班长查看借用来的人员** - 显示姓名、到期时间、原班组等关键信息

### 使用流程
1. B班组申请借用A班组的人员
2. A班组班长审批借用申请  
3. 审批通过后，B班组能查看借用来的人员，A班组能查看被借走的人员

## API接口列表

### 1. 发起借用申请

**接口地址：** `POST /api/teams/{team_id}/borrow-request`

**功能说明：** 班组申请借用其他班组的成员

**请求参数：**
- `team_id` (路径参数): 借用班组ID（申请借用人员的班组）
- `member_psn_num` (string): 被借用人员编号
- `start_date` (number): 借用开始时间（时间戳，秒）
- `end_date` (number): 借用结束时间（时间戳，秒）
- `remarks` (string): 借用备注

**业务规则：**
- 借用开始时间不能早于当前时间
- 被借用人员必须存在且属于某个班组
- 不能重复申请借用同一人员

**请求示例：**
```json
{
  "member_psn_num": "P006",
  "start_date": 1751190200,
  "end_date": 1751276600,
  "remarks": "临时支援生产任务"
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "借用申请创建成功",
  "data": {
    "borrow_id": 1
  },
  "timestamp": 1751190153
}
```

### 2. 审批借用申请

**接口地址：** `PUT /api/teams/{team_id}/borrow-request/{borrow_id}/approve`

**功能说明：** 原班组班长审批借用申请

**请求参数：**
- `team_id` (路径参数): 原班组ID（被借用人员所属班组）
- `borrow_id` (路径参数): 借用申请ID
- `action` (string): 审批动作（"approve"=同意，"reject"=拒绝）
- `remarks` (string): 审批备注

**权限要求：**
- 只有原班组的班长才能审批借用申请
- 申请状态必须是待审批（Pending）

**请求示例：**
```json
{
  "action": "approve",
  "remarks": "同意借用，支援生产任务"
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "借用申请同意成功",
  "data": null,
  "timestamp": 1751190177
}
```

### 3. 查询借用申请列表

**接口地址：** `GET /api/teams/{team_id}/borrow-requests`

**功能说明：** 查询班组的借用申请列表

**查询参数：**
- `type` (可选): 申请类型
  - `outgoing`: 发出的申请（本班组申请借用其他班组人员）
  - `incoming`: 收到的申请（其他班组申请借用本班组人员）
- `status` (可选): 申请状态
  - `0`: 待审批
  - `1`: 已同意
  - `2`: 已拒绝
- `page` (可选): 页码（默认1）
- `page_size` (可选): 每页数量（默认20）

**响应示例：**
```json
{
  "code": 200,
  "message": "获取借用申请列表成功",
  "data": {
    "requests": [
      {
        "borrow_id": 1,
        "member_psn_num": "P006",
        "member_name": "张三",
        "original_team_id": 4,
        "original_team_name": "生产二班",
        "borrow_team_id": 3,
        "borrow_team_name": "生产一班",
        "borrow_status": "Approved",
        "start_date": 1751188900,
        "end_date": 1751275300,
        "requested_by": "admin",
        "requested_by_name": "系统管理员",
        "approved_by": "P002",
        "approved_by_name": "李四",
        "requested_at": 1751188893,
        "approved_at": 1751188950,
        "remarks": "同意借用，支援生产任务"
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 20,
    "total_pages": 1
  },
  "timestamp": 1751190221
}
```

### 4. 查询借用来的人员 ⭐

**接口地址：** `GET /api/teams/{team_id}/borrowed-in-members`

**功能说明：** B班组班长查看借用来的人员 - 核心功能 ⭐

**功能特点：**
- B班组班长能看到从其他班组借用来的人员
- 显示人员姓名、到期时间、原班组等关键信息
- 支持多人员同时借用管理
- 只显示当前借用中的人员（已审批且未到期）

**返回数据包含：**
- 人员姓名（member_name）
- 到期时间（borrow_end_date，时间戳格式）
- 原班组信息（original_team_name）
- 借用状态（is_borrowed_in: true）

**响应示例：**
```json
{
  "code": 200,
  "message": "获取借用人员列表成功",
  "data": [
    {
      "team_id": 3,
      "member_psn_num": "P007",
      "member_name": "王五",
      "joined_at": 1751190200,
      "status": 1,
      "borrow_status": {
        "is_borrowed_out": false,
        "is_borrowed_in": true,
        "borrow_team_id": null,
        "borrow_team_name": null,
        "original_team_id": 4,
        "original_team_name": "生产二班",
        "borrow_end_date": 1751276600
      }
    }
  ],
  "timestamp": 1751190200
}
```

### 5. 查询被借走的人员

**接口地址：** `GET /api/teams/{team_id}/borrowed-out-members`

**功能说明：** A班组班长查看被借走的人员

**功能特点：**
- A班组班长能看到被借用到其他班组的人员
- 显示人员姓名、借用班组、到期时间等信息
- 支持多人员被借走管理
- 只显示当前被借走的人员（已审批且未到期）

**返回数据包含：**
- 人员姓名（member_name）
- 借用班组（borrow_team_name）
- 到期时间（borrow_end_date，时间戳格式）
- 借用状态（is_borrowed_out: true）

**响应示例：**
```json
{
  "code": 200,
  "message": "获取被借走人员列表成功",
  "data": [
    {
      "team_id": 4,
      "member_psn_num": "P006",
      "member_name": "张三",
      "joined_at": 1751188819,
      "status": 1,
      "borrow_status": {
        "is_borrowed_out": true,
        "is_borrowed_in": false,
        "borrow_team_id": 3,
        "borrow_team_name": "生产一班",
        "original_team_id": null,
        "original_team_name": null,
        "borrow_end_date": 1751275300
      }
    }
  ],
  "timestamp": 1751190210
}
```

## 认证说明

所有API都需要Bearer Token认证，请先调用登录接口获取token：

```bash
POST /api/auth/login
{
  "username": "your_username",
  "password": "your_password"
}
```

获取token后，在请求头中添加：
```
Authorization: Bearer {your_token}
```

## 错误码说明

- `200`: 成功
- `400`: 请求参数错误或业务规则验证失败
- `401`: 未认证或token无效
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 业务场景示例

### 完整借用流程示例

1. **班组3申请借用班组4的P006**
   ```bash
   POST /api/teams/3/borrow-request
   {
     "member_psn_num": "P006",
     "start_date": 1751190200,
     "end_date": 1751276600,
     "remarks": "临时支援生产任务"
   }
   ```

2. **班组4班长审批申请**
   ```bash
   PUT /api/teams/4/borrow-request/1/approve
   {
     "action": "approve",
     "remarks": "同意借用"
   }
   ```

3. **班组3查看借用来的人员**
   ```bash
   GET /api/teams/3/borrowed-in-members
   # 能看到P006，包含姓名、到期时间、原班组等信息
   ```

4. **班组4查看被借走的人员**
   ```bash
   GET /api/teams/4/borrowed-out-members  
   # 能看到P006被借用到班组3的状态
   ```

## 数据库表结构

借用功能使用 `TeamMemberBorrows` 表存储借用记录：

```sql
CREATE TABLE TeamMemberBorrows (
    BorrowID int IDENTITY(1,1) PRIMARY KEY,
    Member_psn_num nvarchar(20) NOT NULL,
    OriginalTeamID int NOT NULL,
    BorrowTeamID int NOT NULL,
    BorrowStatus int NOT NULL DEFAULT 0,  -- 0=待同意，1=已同意，2=已拒绝
    StartDate datetime NOT NULL,
    EndDate datetime NOT NULL,
    RequestedBy nvarchar(20) NOT NULL,
    ApprovedBy nvarchar(20) NULL,
    RequestedAt datetime NOT NULL DEFAULT GETDATE(),
    ApprovedAt datetime NULL,
    Remarks nvarchar(500) NULL,
    CreatedAt datetime NOT NULL DEFAULT GETDATE(),
    UpdatedAt datetime NOT NULL DEFAULT GETDATE()
);
```
