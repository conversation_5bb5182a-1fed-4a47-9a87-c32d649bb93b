# U8权限管理系统 Postman API 使用指南

## 📋 概述

本文档提供了 U8权限管理系统的完整 Postman API 集合使用指南，包含所有功能模块的接口测试方法。

## 📁 文件说明

### Postman 集合文件
- **`U8_Permission_System_Complete_API.postman_collection.json`** - 完整的API接口集合
- **`U8_Permission_System.postman_environment.json`** - 环境变量配置

### 导入方法
1. 打开 Postman
2. 点击 "Import" 按钮
3. 选择上述两个 JSON 文件导入
4. 选择 "U8权限管理系统环境" 作为当前环境

## 🔧 环境配置

### 环境变量说明
| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `base_url` | `http://localhost:8081` | API服务器地址 |
| `auth_token` | (自动设置) | JWT认证令牌 |
| `refresh_token` | (自动设置) | 刷新令牌 |
| `admin_username` | `admin` | 管理员用户名 |
| `admin_password` | `admin123` | 管理员密码 |
| `test_user_id` | `P001` | 测试用户ID |
| `test_role_id` | `3` | 测试角色ID |
| `test_team_id` | `1` | 测试班组ID |
| `workcenter_code` | `001` | 工作中心代码 |

### 修改服务器地址
如果您的服务器不在本地运行，请修改 `base_url` 变量：
```
生产环境: https://your-domain.com
测试环境: http://test-server:8081
```

## 🚀 快速开始

### 1. 启动服务器
```bash
cd /path/to/u8_extend
cargo run --bin u8_extend
```

### 2. 执行登录
1. 展开 "🔐 认证管理" 文件夹
2. 运行 "用户登录" 请求
3. 系统会自动保存返回的 `auth_token` 到环境变量

### 3. 测试其他接口
登录成功后，所有需要认证的接口都会自动使用保存的 token。

## 📚 API 模块说明

### 🏠 系统基础
- **根路径**: 检查服务是否运行
- **健康检查**: 检查服务健康状态

### 🔐 认证管理
- **用户登录**: 获取访问令牌
- **刷新Token**: 刷新过期的令牌
- **用户登出**: 注销当前会话
- **获取用户信息**: 获取当前用户详情
- **验证Token**: 验证令牌有效性
- **获取用户权限**: 获取当前用户权限列表

### 📱 移动端认证
专为移动端设计的认证接口，提供简化的认证流程。

### 👥 用户管理
- **获取用户列表**: 支持分页和关键词搜索
- **创建用户**: 创建新用户账号
- **获取单个用户**: 根据用户ID获取详情
- **更新用户**: 修改用户信息
- **删除用户**: 删除用户账号

### 🎭 角色管理
- **基础CRUD**: 角色的创建、读取、更新、删除
- **用户角色关联**: 分配和移除用户角色
- **智能角色分配**: 
  - 精确指定班组分配
  - 自动分配模式
  - 获取智能建议

### 🏭 班组管理
完整的班组管理功能，包括：
- **班组CRUD**: 班组的基础管理
- **班组长管理**: 任命、移除班组长
- **班组成员管理**: 添加、移除班组成员
- **批量操作**: 支持批量任命和添加
- **统一接口**: 一步到位的任命和移除

### 🛡️ 权限管理
- **权限定义**: 获取系统权限定义
- **权限树**: 获取层级权限结构
- **权限验证**: 验证用户权限
- **角色权限**: 管理角色权限分配
- **自定义权限**: 创建和管理自定义权限

### 📦 业务数据管理
- **产品管理**: 查询产品信息
- **工序管理**: 查询工序信息  
- **设备管理**: 查询设备信息

## 🧪 测试场景

### 场景1: 完整的用户角色分配流程
```
1. 登录系统 → 认证管理/用户登录
2. 创建用户 → 用户管理/创建用户
3. 创建班组 → 班组管理/创建班组
4. 智能分配角色 → 角色管理/智能角色分配
5. 验证结果 → 用户班组信息查询/获取用户角色班组协同状态
```

### 场景2: 班组长任命流程
```
1. 获取班组列表 → 班组管理/获取班组列表
2. 统一任命班组长 → 班组长管理/统一任命班组长
3. 验证任命结果 → 班组长管理/获取班组长列表
```

### 场景3: 权限管理流程
```
1. 获取角色列表 → 角色管理/获取角色列表
2. 获取权限定义 → 权限管理/获取权限定义
3. 更新角色权限 → 权限管理/更新角色权限
4. 验证权限 → 权限管理/检查用户权限
```

## 🔍 调试技巧

### 1. 查看请求日志
在 Postman Console 中查看详细的请求和响应日志。

### 2. 环境变量调试
在 Environment 面板中查看和修改环境变量值。

### 3. 响应验证
使用 Postman 的 Tests 功能验证响应格式和数据。

### 4. 批量测试
使用 Collection Runner 执行批量测试。

## ⚠️ 注意事项

### 1. Token 过期
- JWT Token 默认24小时过期
- 过期后需要重新登录或使用刷新接口

### 2. 权限检查
- 大部分接口需要相应的权限
- 管理员账号拥有所有权限

### 3. 数据依赖
- 某些接口依赖特定的测试数据
- 建议按照测试场景顺序执行

### 4. 服务器状态
- 确保服务器正常运行
- 检查数据库连接状态

## 🆘 常见问题

### Q: 登录失败怎么办？
A: 检查用户名密码是否正确，确认服务器是否正常运行。

### Q: Token 无效怎么办？
A: 重新执行登录请求，或使用刷新Token接口。

### Q: 接口返回权限不足？
A: 确认当前用户是否有相应权限，或使用管理员账号测试。

### Q: 找不到测试数据？
A: 先执行相关的创建接口，或检查数据库中是否有测试数据。

## 📞 技术支持

如有问题，请查看：
1. 服务器日志文件
2. Postman Console 输出
3. 项目 Wiki 文档
4. API 响应错误信息

---

**祝您测试愉快！** 🎉
