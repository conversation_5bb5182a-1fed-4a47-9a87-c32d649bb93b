我现在需要一个新功能，这个功能的目的是为班长创建指令卡时候，提供班长选择的他心仪的产品，设备，和工序。   即班长选择的这三个值是自己维护的三个列表。

现在需要新增三个功能，班长可以从所有的产品或设备或工序里面选择自己的数据，

1. 班组绑定的产品列表
2. 班组绑定的设备列表
3. 班组绑定的工序列表

然后新增指令卡的时候，只能从这三个列表中选择数据。

这样应该还需要新增对应的表。

后续查询的话，就从这些表里面查询数据。


工序表：operations
工作中心表： workcenter
产品表： inventory
设备表：EQ_QEQDataSel
人员表：person
指令卡：InstructionCards  
  自动提交： auto_submit
  班长自动审核：auto_leader_approve 0 false 1 true  键入数值  
  负责人自动审核：auto_manager_approve 0 false 1 true  键入数值  
  数据状态： status  键入数值
    /// 待处理 - 班长发布后，等待指定人员填写完成数量
    Pending = 0,
    /// 已提交 - 指定人员已填写完成数量并提交，等待班长审核
    Submitted = 1,
    /// 班长审核通过 - 班长审核完成，等待负责人审核
    LeaderApproved = 2,
    /// 已完成 - 负责人审核完成，所有流程结束
    Completed = 3,


指令卡审核操作记录：InstructionCardAuditLogs 
班组：Teams
班组班长：TeamLeaders
班组成员：TeamMembers
班组成员借用：TeamMemberBorrows
  借用状态  BorrowStatus： 
              /// 待同意
              Pending = 0,
              /// 已同意（借用中）
              Approved = 1,
              /// 已归还（包含过期自动归还和主动归还）
              Returned = 3,
角色表：role  里面包含permissions权限信息
权限表：  permission*
用户和角色的关联：user_roles
零活录入：FlexibleEntries
  审核状态:  status
            /// 待审核 - 班长录入后，等待负责人审核
            Pending = 0,
            /// 已审核 - 负责人审核完成
            Approved = 1,


未使用： sfc_group，group，sync_logs