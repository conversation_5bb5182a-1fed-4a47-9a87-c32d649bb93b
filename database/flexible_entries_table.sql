-- 零活录入表创建脚本
-- 用于记录班长录入的零活数据和负责人审核信息

-- 使用ZLKDATA数据库
USE ZLKDATA;

-- 创建零活录入表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='FlexibleEntries' AND xtype='U')
BEGIN
    CREATE TABLE FlexibleEntries (
        -- 主键
        id BIGINT IDENTITY(1,1) PRIMARY KEY,
        
        -- 产品和工序信息
        inventory_id NVARCHAR(50) COLLATE Chinese_PRC_CI_AS NOT NULL,  -- 产品ID，对应inventory.cinvcode
        operation_id NVARCHAR(50) COLLATE Chinese_PRC_CI_AS NOT NULL,  -- 工序ID，对应operation.opcode
        
        -- 数量信息
        flexible_quantity INT NOT NULL CHECK (flexible_quantity > 0),  -- 零活数量（班长录入）
        approved_quantity INT NULL CHECK (approved_quantity > 0),      -- 审核通过的零活数量（负责人审核时填写）
        
        -- 人员信息
        creator_psn_num NVARCHAR(20) COLLATE Chinese_PRC_CI_AS NOT NULL,  -- 录入人（班长）
        approver_psn_num NVARCHAR(20) COLLATE Chinese_PRC_CI_AS NULL,     -- 审核人（负责人）
        
        -- 时间信息
        created_time DATETIME2 NOT NULL DEFAULT GETDATE(),  -- 录入时间
        approved_time DATETIME2 NULL,                       -- 审核时间
        
        -- 审核信息
        approval_remarks NVARCHAR(500) COLLATE Chinese_PRC_CI_AS NULL,  -- 审核备注
        
        -- 状态信息
        status TINYINT NOT NULL DEFAULT 0,  -- 状态：0=待审核，1=已审核
        
        -- 系统字段
        created_at DATETIME2 NOT NULL DEFAULT GETDATE(),
        updated_at DATETIME2 NOT NULL DEFAULT GETDATE(),
        
        -- 外键约束 (引用同数据库中的person表)
        CONSTRAINT FK_FlexibleEntries_Creator FOREIGN KEY (creator_psn_num)
            REFERENCES person(cpsn_num),
        CONSTRAINT FK_FlexibleEntries_Approver FOREIGN KEY (approver_psn_num)
            REFERENCES person(cpsn_num),
            
        -- 索引
        INDEX IX_FlexibleEntries_Status (status),
        INDEX IX_FlexibleEntries_CreatedTime (created_time),
        INDEX IX_FlexibleEntries_InventoryId (inventory_id),
        INDEX IX_FlexibleEntries_OperationId (operation_id),
        INDEX IX_FlexibleEntries_Creator (creator_psn_num)
    );
    
    PRINT N'✅ 零活录入表 FlexibleEntries 创建成功';
END
ELSE
BEGIN
    PRINT N'ℹ️ 零活录入表 FlexibleEntries 已存在';
END

-- 添加表注释
EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'零活录入表，记录班长录入的零活数据和负责人审核信息', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'FlexibleEntries';

-- 添加字段注释
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'主键ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FlexibleEntries', @level2type = N'COLUMN', @level2name = N'id';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'产品ID，对应inventory.cinvcode', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FlexibleEntries', @level2type = N'COLUMN', @level2name = N'inventory_id';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'工序ID，对应operation.opcode', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FlexibleEntries', @level2type = N'COLUMN', @level2name = N'operation_id';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'零活数量（班长录入）', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FlexibleEntries', @level2type = N'COLUMN', @level2name = N'flexible_quantity';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'审核通过的零活数量（负责人审核时填写）', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FlexibleEntries', @level2type = N'COLUMN', @level2name = N'approved_quantity';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'录入人（班长）', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FlexibleEntries', @level2type = N'COLUMN', @level2name = N'creator_psn_num';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'审核人（负责人）', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FlexibleEntries', @level2type = N'COLUMN', @level2name = N'approver_psn_num';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'录入时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FlexibleEntries', @level2type = N'COLUMN', @level2name = N'created_time';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'审核时间', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FlexibleEntries', @level2type = N'COLUMN', @level2name = N'approved_time';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'审核备注', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FlexibleEntries', @level2type = N'COLUMN', @level2name = N'approval_remarks';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'状态：0=待审核，1=已审核', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'FlexibleEntries', @level2type = N'COLUMN', @level2name = N'status';

PRINT N'🎯 零活录入表结构创建完成！';
