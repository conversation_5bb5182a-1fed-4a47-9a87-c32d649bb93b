-- 完整的一键部署SQL脚本
-- 适用于全新环境部署，包含所有表结构、默认数据、权限定义等
-- 支持SQL Server 2016/2017/2019

-- 1. 创建数据库（如果不存在）
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = N'u8_extend')
BEGIN
    CREATE DATABASE u8_extend
    COLLATE Chinese_PRC_CI_AS;
    PRINT N'✅ 数据库 u8_extend 创建成功';
END
ELSE
BEGIN
    PRINT N'ℹ️ 数据库 u8_extend 已存在';
END

USE u8_extend;

-- 2. 创建用户表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='users' AND xtype='U')
BEGIN
    CREATE TABLE users (
        id BIGINT IDENTITY(1,1) PRIMARY KEY,
        username NVARCHAR(50) UNIQUE NOT NULL,
        nickname NVARCHAR(100),
        password_hash NVARCHAR(255) NOT NULL,
        employee_id NVARCHAR(50),
        work_center NVARCHAR(100),
        email NVARCHAR(100),
        phone NVARCHAR(20),
        department NVARCHAR(100),
        position NVARCHAR(100),
        status NVARCHAR(20) DEFAULT 'active',
        is_admin BIT DEFAULT 0,
        last_login_at DATETIME2,
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE()
    );
    PRINT N'✅ 用户表创建成功';
END

-- 3. 创建角色表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='roles' AND xtype='U')
BEGIN
    CREATE TABLE roles (
        id BIGINT IDENTITY(1,1) PRIMARY KEY,
        name NVARCHAR(50) UNIQUE NOT NULL,
        description NVARCHAR(500),
        permissions NVARCHAR(MAX),
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE()
    );
    PRINT N'✅ 角色表创建成功';
END

-- 4. 创建用户角色关联表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='user_roles' AND xtype='U')
BEGIN
    CREATE TABLE user_roles (
        id BIGINT IDENTITY(1,1) PRIMARY KEY,
        user_id BIGINT NOT NULL,
        role_id BIGINT NOT NULL,
        assigned_at DATETIME2 DEFAULT GETDATE(),
        assigned_by BIGINT,
        FOREIGN KEY (user_id) REFERENCES users(id),
        FOREIGN KEY (role_id) REFERENCES roles(id),
        UNIQUE(user_id, role_id)
    );
    PRINT N'✅ 用户角色关联表创建成功';
END

-- 5. 创建同步日志表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='sync_logs' AND xtype='U')
BEGIN
    CREATE TABLE sync_logs (
        id BIGINT IDENTITY(1,1) PRIMARY KEY,
        sync_type NVARCHAR(50) NOT NULL,
        status NVARCHAR(20) NOT NULL,
        message NVARCHAR(MAX),
        records_processed INT DEFAULT 0,
        records_success INT DEFAULT 0,
        records_failed INT DEFAULT 0,
        started_at DATETIME2 NOT NULL,
        completed_at DATETIME2,
        created_by BIGINT,
        error_details NVARCHAR(MAX)
    );
    PRINT N'✅ 同步日志表创建成功';
END

-- 6. 创建权限定义表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='permission_definitions' AND xtype='U')
BEGIN
    CREATE TABLE permission_definitions (
        id BIGINT IDENTITY(1,1) PRIMARY KEY,
        resource_name NVARCHAR(50) UNIQUE NOT NULL,
        resource_label NVARCHAR(100) NOT NULL,
        description NVARCHAR(500),
        category NVARCHAR(50),
        icon NVARCHAR(50),
        actions NVARCHAR(MAX),
        is_active BIT DEFAULT 1,
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE()
    );
    PRINT N'✅ 权限定义表创建成功';
END

-- 7. 创建权限分类表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='permission_categories' AND xtype='U')
BEGIN
    CREATE TABLE permission_categories (
        id BIGINT IDENTITY(1,1) PRIMARY KEY,
        name NVARCHAR(50) UNIQUE NOT NULL,
        label NVARCHAR(100) NOT NULL,
        description NVARCHAR(500),
        sort_order INT DEFAULT 0,
        is_active BIT DEFAULT 1,
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE()
    );
    PRINT N'✅ 权限分类表创建成功';
END

-- 8. 创建权限操作表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='permission_actions' AND xtype='U')
BEGIN
    CREATE TABLE permission_actions (
        id BIGINT IDENTITY(1,1) PRIMARY KEY,
        permission_id BIGINT NOT NULL,
        action_name NVARCHAR(50) NOT NULL,
        action_label NVARCHAR(100) NOT NULL,
        description NVARCHAR(500),
        sort_order INT DEFAULT 0,
        is_active BIT DEFAULT 1,
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE(),
        FOREIGN KEY (permission_id) REFERENCES permission_definitions(id),
        UNIQUE(permission_id, action_name)
    );
    PRINT N'✅ 权限操作表创建成功';
END

-- 9. 创建权限模板表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='permission_templates' AND xtype='U')
BEGIN
    CREATE TABLE permission_templates (
        id BIGINT IDENTITY(1,1) PRIMARY KEY,
        name NVARCHAR(50) UNIQUE NOT NULL,
        label NVARCHAR(100) NOT NULL,
        description NVARCHAR(500),
        role_level TINYINT NOT NULL DEFAULT 4,
        permissions_config NVARCHAR(MAX) NOT NULL,
        is_active BIT DEFAULT 1,
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE()
    );
    PRINT N'✅ 权限模板表创建成功';
END

PRINT N'🎯 所有表结构创建完成！';

-- ========================================
-- 第二部分：插入默认数据
-- ========================================

-- 10. 插入权限分类数据
IF NOT EXISTS (SELECT 1 FROM permission_categories WHERE name = N'user_management')
BEGIN
    INSERT INTO permission_categories (name, label, description, sort_order) VALUES
    (N'user_management', N'用户管理', N'用户账户和个人信息管理', 1),
    (N'permission_management', N'权限管理', N'角色和权限配置管理', 2),
    (N'data_sync', N'数据同步', N'数据同步和集成功能', 3),
    (N'business_management', N'业务管理', N'业务相关功能管理', 4);
    PRINT N'✅ 权限分类数据插入成功';
END
ELSE
BEGIN
    PRINT N'ℹ️ 权限分类数据已存在';
END

-- 11. 插入系统权限定义
IF NOT EXISTS (SELECT 1 FROM permission_definitions WHERE resource_name = N'auth' AND is_system = 1)
BEGIN
    INSERT INTO permission_definitions (resource_name, resource_label, description, category, icon, actions, is_system, is_active) VALUES
    (N'auth', N'认证管理', N'用户认证和授权相关操作', N'authentication', N'lock', N'[{"name":"login","label":"用户登录","description":"用户登录系统"},{"name":"logout","label":"用户登出","description":"用户登出系统"},{"name":"verify","label":"Token验证","description":"验证用户Token"},{"name":"profile","label":"获取用户信息","description":"获取当前用户详细信息"}]', 1, 1),
    (N'users', N'用户管理', N'管理系统用户账户', N'user_management', N'user', N'[{"name":"read","label":"查看用户","description":"查看用户列表和详情"},{"name":"create","label":"创建用户","description":"创建新的用户账户"},{"name":"update","label":"更新用户","description":"更新用户信息"},{"name":"delete","label":"删除用户","description":"删除用户账户"},{"name":"status","label":"用户状态","description":"启用或禁用用户"}]', 1, 1),
    (N'roles', N'角色管理', N'管理系统角色和权限', N'permission_management', N'shield', N'[{"name":"read","label":"查看角色","description":"查看角色列表和详情"},{"name":"create","label":"创建角色","description":"创建新的角色"},{"name":"update","label":"更新角色","description":"更新角色信息"},{"name":"delete","label":"删除角色","description":"删除角色"}]', 1, 1),
    (N'role_assignment', N'角色分配', N'管理用户角色分配', N'permission_management', N'user-check', N'[{"name":"admin","label":"分配管理员","description":"分配管理员角色"},{"name":"manager","label":"分配负责人","description":"分配负责人角色"},{"name":"team_leader","label":"分配班长","description":"分配班长角色"},{"name":"user","label":"分配普通用户","description":"分配普通用户角色"}]', 1, 1),
    (N'permissions', N'权限管理', N'管理系统权限配置', N'permission_management', N'key', N'[{"name":"read","label":"查看权限","description":"查看权限配置"},{"name":"create","label":"创建权限","description":"创建自定义权限"},{"name":"update","label":"更新权限","description":"更新权限配置"},{"name":"delete","label":"删除权限","description":"删除自定义权限"}]', 1, 1),
    (N'sync', N'数据同步', N'数据同步和集成操作', N'data_sync', N'sync', N'[{"name":"read","label":"查看同步","description":"查看同步状态和日志"},{"name":"trigger","label":"触发同步","description":"手动触发数据同步"}]', 1, 1),
    (N'profile', N'个人信息', N'管理个人信息', N'user_management', N'user-circle', N'[{"name":"read","label":"查看个人信息","description":"查看个人详细信息"},{"name":"update","label":"更新个人信息","description":"更新个人信息"}]', 1, 1),
    (N'mobile', N'移动端', N'移动端专用接口和功能', N'mobile_app', N'mobile', N'[{"name":"login","label":"移动端登录","description":"移动端用户登录"},{"name":"user_info","label":"获取用户信息","description":"获取移动端用户信息"},{"name":"permission_check","label":"权限检查","description":"移动端权限检查"},{"name":"logout","label":"移动端登出","description":"移动端用户登出"}]', 1, 1);
    PRINT N'✅ 系统权限定义插入成功';
END
ELSE
BEGIN
    PRINT N'ℹ️ 系统权限定义已存在';
END

-- 12. 插入权限操作数据
DECLARE @users_id BIGINT, @roles_id BIGINT, @permissions_id BIGINT, @sync_id BIGINT;

SELECT @users_id = id FROM permission_definitions WHERE resource_name = N'users';
SELECT @roles_id = id FROM permission_definitions WHERE resource_name = N'roles';
SELECT @permissions_id = id FROM permission_definitions WHERE resource_name = N'permissions';
SELECT @sync_id = id FROM permission_definitions WHERE resource_name = N'sync';

IF NOT EXISTS (SELECT 1 FROM permission_actions WHERE permission_id = @users_id AND action_name = N'read')
BEGIN
    -- 用户管理操作
    INSERT INTO permission_actions (permission_id, action_name, action_label, description, sort_order) VALUES
    (@users_id, N'read', N'查看用户', N'查看用户列表和基本信息', 1),
    (@users_id, N'create', N'创建用户', N'创建新的用户账户', 2),
    (@users_id, N'update', N'修改用户', N'修改用户信息', 3),
    (@users_id, N'delete', N'删除用户', N'删除用户账户', 4);

    -- 角色管理操作
    INSERT INTO permission_actions (permission_id, action_name, action_label, description, sort_order) VALUES
    (@roles_id, N'read', N'查看角色', N'查看角色列表和权限配置', 1),
    (@roles_id, N'create', N'创建角色', N'创建新的角色', 2),
    (@roles_id, N'update', N'修改角色', N'修改角色信息和权限', 3),
    (@roles_id, N'delete', N'删除角色', N'删除角色', 4);

    -- 权限管理操作
    INSERT INTO permission_actions (permission_id, action_name, action_label, description, sort_order) VALUES
    (@permissions_id, N'read', N'查看权限', N'查看权限配置', 1),
    (@permissions_id, N'create', N'创建权限', N'创建新的权限', 2),
    (@permissions_id, N'update', N'修改权限', N'修改权限配置', 3),
    (@permissions_id, N'delete', N'删除权限', N'删除权限', 4);

    -- 数据同步操作
    INSERT INTO permission_actions (permission_id, action_name, action_label, description, sort_order) VALUES
    (@sync_id, N'read', N'查看同步日志', N'查看数据同步历史和状态', 1),
    (@sync_id, N'trigger', N'触发同步', N'手动触发数据同步', 2);

    PRINT N'✅ 权限操作数据插入成功';
END
ELSE
BEGIN
    PRINT N'ℹ️ 权限操作数据已存在';
END

-- 13. 插入权限模板数据
IF NOT EXISTS (SELECT 1 FROM permission_templates WHERE name = N'admin')
BEGIN
    INSERT INTO permission_templates (name, label, description, role_level, permissions_config) VALUES
    (N'admin', N'超级管理员', N'拥有系统所有权限的最高管理员', 1, N'{"permissions": [{"resource": "*", "actions": ["*"]}], "role_level": 1}'),
    (N'manager', N'部门负责人', N'可以管理用户和分配角色的负责人', 2, N'{"permissions": [{"resource": "users", "actions": ["read", "create", "update"]}, {"resource": "roles", "actions": ["read"]}, {"resource": "role_assignment", "actions": ["manager", "team_leader", "user"]}, {"resource": "sync", "actions": ["read"]}, {"resource": "profile", "actions": ["read", "update"]}], "role_level": 2}'),
    (N'team_leader', N'班长/组长', N'可以查看用户信息的班组长', 3, N'{"permissions": [{"resource": "users", "actions": ["read"]}, {"resource": "roles", "actions": ["read"]}, {"resource": "role_assignment", "actions": ["user"]}, {"resource": "profile", "actions": ["read", "update"]}], "role_level": 3}'),
    (N'user', N'普通用户', N'只能管理自己信息的普通用户', 4, N'{"permissions": [{"resource": "profile", "actions": ["read", "update"]}], "role_level": 4}');
    PRINT N'✅ 权限模板数据插入成功';
END
ELSE
BEGIN
    PRINT N'ℹ️ 权限模板数据已存在';
END

-- 14. 插入默认角色
IF NOT EXISTS (SELECT 1 FROM roles WHERE name = N'admin')
BEGIN
    INSERT INTO roles (name, description, permissions, is_system) VALUES
    (N'admin', N'系统管理员，拥有所有权限', N'{"permissions": [{"resource": "auth", "actions": ["login", "logout", "verify", "profile"]}, {"resource": "users", "actions": ["read", "create", "update", "delete", "status"]}, {"resource": "roles", "actions": ["read", "create", "update", "delete"]}, {"resource": "role_assignment", "actions": ["admin", "manager", "team_leader", "user"]}, {"resource": "permissions", "actions": ["read", "create", "update", "delete"]}, {"resource": "sync", "actions": ["read", "trigger"]}, {"resource": "profile", "actions": ["read", "update"]}, {"resource": "mobile", "actions": ["login", "user_info", "permission_check", "logout"]}], "role_level": 1}', 1),
    (N'manager', N'部门负责人，可以管理用户和分配角色', N'{"permissions": [{"resource": "auth", "actions": ["login", "logout", "verify", "profile"]}, {"resource": "users", "actions": ["read", "create", "update", "delete"]}, {"resource": "roles", "actions": ["read", "create", "update"]}, {"resource": "role_assignment", "actions": ["team_leader", "user"]}, {"resource": "permissions", "actions": ["read"]}, {"resource": "profile", "actions": ["read", "update"]}, {"resource": "mobile", "actions": ["login", "user_info", "permission_check", "logout"]}], "role_level": 2}', 1),
    (N'team_leader', N'班长/组长，可以查看和管理普通用户', N'{"permissions": [{"resource": "auth", "actions": ["login", "logout", "verify", "profile"]}, {"resource": "users", "actions": ["read", "update"]}, {"resource": "roles", "actions": ["read"]}, {"resource": "role_assignment", "actions": ["user"]}, {"resource": "profile", "actions": ["read", "update"]}, {"resource": "mobile", "actions": ["login", "user_info", "permission_check", "logout"]}], "role_level": 3}', 1),
    (N'user', N'普通用户，可以查看基础信息和管理自己的资料', N'{"permissions": [{"resource": "auth", "actions": ["login", "logout", "verify", "profile"]}, {"resource": "users", "actions": ["read"]}, {"resource": "roles", "actions": ["read"]}, {"resource": "permissions", "actions": ["read"]}, {"resource": "profile", "actions": ["read", "update"]}, {"resource": "mobile", "actions": ["login", "user_info", "permission_check", "logout"]}], "role_level": 4}', 1);
    PRINT N'✅ 默认角色插入成功';
END
ELSE
BEGIN
    PRINT N'ℹ️ 默认角色已存在';
END

-- 15. 创建默认admin用户
DECLARE @admin_role_id BIGINT;
SELECT @admin_role_id = id FROM roles WHERE name = N'admin';

IF NOT EXISTS (SELECT 1 FROM users WHERE username = N'admin')
BEGIN
    INSERT INTO users (username, password_hash, email, nickname) VALUES
    (N'admin', N'$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PJ/..G', N'<EMAIL>', N'系统管理员');

    DECLARE @admin_user_id BIGINT = SCOPE_IDENTITY();

    -- 分配admin角色给admin用户
    INSERT INTO user_roles (user_id, role_id, assigned_by) VALUES
    (@admin_user_id, @admin_role_id, @admin_user_id);

    PRINT N'✅ 默认admin用户创建成功 (用户名: admin, 密码: admin123)';
END
ELSE
BEGIN
    PRINT N'ℹ️ admin用户已存在';
END

-- ========================================
-- 第三部分：验证部署结果
-- ========================================

PRINT N'';
PRINT N'🔍 验证部署结果...';
PRINT N'';

-- 验证表结构
SELECT 'tables' as type, COUNT(*) as count FROM information_schema.tables WHERE table_schema = 'dbo' AND table_name IN ('users', 'roles', 'user_roles', 'sync_logs', 'permission_definitions', 'permission_categories', 'permission_actions', 'permission_templates')
UNION ALL
SELECT 'permission_categories' as type, COUNT(*) as count FROM permission_categories
UNION ALL
SELECT 'permission_definitions' as type, COUNT(*) as count FROM permission_definitions
UNION ALL
SELECT 'permission_actions' as type, COUNT(*) as count FROM permission_actions
UNION ALL
SELECT 'permission_templates' as type, COUNT(*) as count FROM permission_templates
UNION ALL
SELECT 'roles' as type, COUNT(*) as count FROM roles
UNION ALL
SELECT 'users' as type, COUNT(*) as count FROM users
UNION ALL
SELECT 'user_roles' as type, COUNT(*) as count FROM user_roles;

PRINT N'';
PRINT N'🎉 部署完成总结:';
PRINT N'✅ 数据库: u8_extend';
PRINT N'✅ 表结构: 8个核心表';
PRINT N'✅ 权限分类: 4个分类';
PRINT N'✅ 系统权限: 4个资源';
PRINT N'✅ 权限操作: 14个操作';
PRINT N'✅ 权限模板: 4个模板';
PRINT N'✅ 默认角色: 4个角色';
PRINT N'✅ 管理员账户: admin/admin123';
PRINT N'';
PRINT N'🚀 系统已准备就绪，可以启动应用！';
PRINT N'';
PRINT N'📋 默认登录信息:';
PRINT N'   用户名: admin';
PRINT N'   密码: admin123';
PRINT N'   权限: 超级管理员 (所有权限)';
PRINT N'';
PRINT N'🔧 技术特性:';
PRINT N'   ✓ 完全数据库驱动权限系统';
PRINT N'   ✓ 支持中文权限名称和描述';
PRINT N'   ✓ 分层权限管理 (1-4级)';
PRINT N'   ✓ 动态权限配置';
PRINT N'   ✓ 权限模板系统';
PRINT N'   ✓ 用户角色关联';
PRINT N'   ✓ 数据同步日志';
PRINT N'';

-- ========================================
-- 部署脚本结束
-- ========================================
