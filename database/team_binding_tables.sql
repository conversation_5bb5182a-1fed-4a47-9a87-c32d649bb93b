-- 班组绑定表创建脚本
-- 用于班长创建指令卡时选择班组绑定的产品、设备、工序
-- 创建日期: 2025-01-13

-- 使用ZLKDATA数据库
USE ZLKDATA;

-- ========================================
-- 1. 班组产品绑定表
-- ========================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='TeamProducts' AND xtype='U')
BEGIN
    CREATE TABLE TeamProducts (
        -- 主键
        id BIGINT IDENTITY(1,1) PRIMARY KEY,
        
        -- 关联字段
        team_id INT NOT NULL,                                                    -- 班组ID，关联Teams.TeamID
        inventory_id NVARCHAR(50) COLLATE Chinese_PRC_CI_AS NOT NULL,          -- 产品ID，关联inventory.cinvcode
        
        -- 管理字段
        created_by NVARCHAR(20) COLLATE Chinese_PRC_CI_AS,                     -- 创建人员工号
        status TINYINT NOT NULL DEFAULT 1,                                      -- 状态：1=启用，0=禁用
        created_at DATETIME2 NOT NULL DEFAULT GETDATE(),                        -- 创建时间
        updated_at DATETIME2 NOT NULL DEFAULT GETDATE(),                        -- 更新时间
        
        -- 外键约束
        CONSTRAINT FK_TeamProducts_Team FOREIGN KEY (team_id)
            REFERENCES Teams(TeamID) ON DELETE CASCADE,
        -- 注意：inventory表可能没有主键约束，所以不创建外键约束，只做逻辑关联
        
        -- 唯一约束：防止同一班组重复绑定同一产品
        CONSTRAINT UQ_TeamProducts_Team_Inventory UNIQUE (team_id, inventory_id),
        
        -- 索引
        INDEX IX_TeamProducts_TeamId (team_id),
        INDEX IX_TeamProducts_Status (status),
        INDEX IX_TeamProducts_CreatedAt (created_at)
    );
    
    PRINT N'✅ 班组产品绑定表 TeamProducts 创建成功';
END
ELSE
BEGIN
    PRINT N'ℹ️ 班组产品绑定表 TeamProducts 已存在';
END

-- ========================================
-- 2. 班组设备绑定表
-- ========================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='TeamEquipments' AND xtype='U')
BEGIN
    CREATE TABLE TeamEquipments (
        -- 主键
        id BIGINT IDENTITY(1,1) PRIMARY KEY,
        
        -- 关联字段
        team_id INT NOT NULL,                                                    -- 班组ID，关联Teams.TeamID
        equipment_id NVARCHAR(50) COLLATE Chinese_PRC_CI_AS NOT NULL,          -- 设备ID，关联EQ_QEQDataSel.ceqcode
        
        -- 管理字段
        created_by NVARCHAR(20) COLLATE Chinese_PRC_CI_AS,                     -- 创建人员工号
        status TINYINT NOT NULL DEFAULT 1,                                      -- 状态：1=启用，0=禁用
        created_at DATETIME2 NOT NULL DEFAULT GETDATE(),                        -- 创建时间
        updated_at DATETIME2 NOT NULL DEFAULT GETDATE(),                        -- 更新时间
        
        -- 外键约束
        CONSTRAINT FK_TeamEquipments_Team FOREIGN KEY (team_id)
            REFERENCES Teams(TeamID) ON DELETE CASCADE,
        -- 注意：EQ_QEQDataSel表可能没有主键约束，所以不创建外键约束，只做逻辑关联
        
        -- 唯一约束：防止同一班组重复绑定同一设备
        CONSTRAINT UQ_TeamEquipments_Team_Equipment UNIQUE (team_id, equipment_id),
        
        -- 索引
        INDEX IX_TeamEquipments_TeamId (team_id),
        INDEX IX_TeamEquipments_Status (status),
        INDEX IX_TeamEquipments_CreatedAt (created_at)
    );
    
    PRINT N'✅ 班组设备绑定表 TeamEquipments 创建成功';
END
ELSE
BEGIN
    PRINT N'ℹ️ 班组设备绑定表 TeamEquipments 已存在';
END

-- ========================================
-- 3. 班组工序绑定表
-- ========================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='TeamOperations' AND xtype='U')
BEGIN
    CREATE TABLE TeamOperations (
        -- 主键
        id BIGINT IDENTITY(1,1) PRIMARY KEY,
        
        -- 关联字段
        team_id INT NOT NULL,                                                    -- 班组ID，关联Teams.TeamID
        operation_id NVARCHAR(50) COLLATE Chinese_PRC_CI_AS NOT NULL,          -- 工序ID，关联operation.opcode
        
        -- 管理字段
        created_by NVARCHAR(20) COLLATE Chinese_PRC_CI_AS,                     -- 创建人员工号
        status TINYINT NOT NULL DEFAULT 1,                                      -- 状态：1=启用，0=禁用
        created_at DATETIME2 NOT NULL DEFAULT GETDATE(),                        -- 创建时间
        updated_at DATETIME2 NOT NULL DEFAULT GETDATE(),                        -- 更新时间
        
        -- 外键约束
        CONSTRAINT FK_TeamOperations_Team FOREIGN KEY (team_id)
            REFERENCES Teams(TeamID) ON DELETE CASCADE,
        -- 注意：operation表可能没有主键约束，所以不创建外键约束，只做逻辑关联
        
        -- 唯一约束：防止同一班组重复绑定同一工序
        CONSTRAINT UQ_TeamOperations_Team_Operation UNIQUE (team_id, operation_id),
        
        -- 索引
        INDEX IX_TeamOperations_TeamId (team_id),
        INDEX IX_TeamOperations_Status (status),
        INDEX IX_TeamOperations_CreatedAt (created_at)
    );
    
    PRINT N'✅ 班组工序绑定表 TeamOperations 创建成功';
END
ELSE
BEGIN
    PRINT N'ℹ️ 班组工序绑定表 TeamOperations 已存在';
END

-- ========================================
-- 4. 添加表注释
-- ========================================
-- 班组产品绑定表注释
EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'班组产品绑定表，记录班组可选择的产品列表，用于班长创建指令卡时的产品选择', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'TeamProducts';

-- 班组设备绑定表注释
EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'班组设备绑定表，记录班组可选择的设备列表，用于班长创建指令卡时的设备选择', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'TeamEquipments';

-- 班组工序绑定表注释
EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'班组工序绑定表，记录班组可选择的工序列表，用于班长创建指令卡时的工序选择', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'TeamOperations';

-- ========================================
-- 5. 验证创建结果
-- ========================================
PRINT N'';
PRINT N'🔍 验证表创建结果...';
PRINT N'';

-- 验证表是否创建成功
SELECT 
    'TeamProducts' as table_name,
    CASE WHEN EXISTS (SELECT * FROM sysobjects WHERE name='TeamProducts' AND xtype='U') 
         THEN '✅ 已创建' 
         ELSE '❌ 未创建' 
    END as status
UNION ALL
SELECT 
    'TeamEquipments' as table_name,
    CASE WHEN EXISTS (SELECT * FROM sysobjects WHERE name='TeamEquipments' AND xtype='U') 
         THEN '✅ 已创建' 
         ELSE '❌ 未创建' 
    END as status
UNION ALL
SELECT 
    'TeamOperations' as table_name,
    CASE WHEN EXISTS (SELECT * FROM sysobjects WHERE name='TeamOperations' AND xtype='U') 
         THEN '✅ 已创建' 
         ELSE '❌ 未创建' 
    END as status;

PRINT N'';
PRINT N'🎉 班组绑定表创建完成！';
PRINT N'';
PRINT N'📋 创建的表：';
PRINT N'   ✓ TeamProducts - 班组产品绑定表';
PRINT N'   ✓ TeamEquipments - 班组设备绑定表';
PRINT N'   ✓ TeamOperations - 班组工序绑定表';
PRINT N'';
PRINT N'🔧 功能特性：';
PRINT N'   ✓ 外键约束保证数据完整性';
PRINT N'   ✓ 唯一约束防止重复绑定';
PRINT N'   ✓ 索引优化查询性能';
PRINT N'   ✓ 软删除机制';
PRINT N'   ✓ 审计字段支持';
PRINT N'   ✓ 中文排序规则一致性';
PRINT N'';

-- ========================================
-- 脚本结束
-- ========================================
